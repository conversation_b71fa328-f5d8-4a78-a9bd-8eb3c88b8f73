<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\NewsPost;
use App\Models\Category;
use App\Models\Subcategory;
use App\Models\Banner;
use Carbon\Carbon;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use App;
use DateTime;
use App\Models\User;

class IndexController extends Controller
{
    public function Index(){
        // Cache the homepage data for 15 minutes
        $cacheKey = 'homepage_data';
        $data = Cache::remember($cacheKey, 900, function () {
            // Get approved posts base query
            $approvedQuery = NewsPost::where('status', 1)->where('approval_status', 'approved');

            // Get recent and popular posts with eager loading
            $newnewspost = $approvedQuery->clone()->with(['category', 'user'])
                ->orderBy('id', 'DESC')->limit(8)->get();
            $newspopular = $approvedQuery->clone()->with(['category', 'user'])
                ->orderBy('view_count', 'DESC')->limit(8)->get();

            // Get categories and their news in optimized way
            $categories = Category::orderBy('id')->limit(9)->get();
            $categoryNews = [];

            foreach ($categories as $index => $category) {
                $categoryNews["skip_cat_{$index}"] = $category;
                $limits = [5, 3, 6, 5, 5, 4, 4, 4, 4]; // Limits for each category
                $limit = $limits[$index] ?? 4;

                $categoryNews["skip_news_{$index}"] = $approvedQuery->clone()
                    ->with(['category', 'user'])
                    ->where('category_id', $category->id)
                    ->orderBy('id', 'DESC')
                    ->limit($limit)
                    ->get();
            }

            // Get special section posts
            $news_slider = $approvedQuery->clone()->with(['category', 'user'])
                ->where('top_slider', 1)->limit(3)->get();
            $section_three = $approvedQuery->clone()->with(['category', 'user'])
                ->where('first_section_three', 1)->limit(3)->get();
            $section_nine = $approvedQuery->clone()->with(['category', 'user'])
                ->where('first_section_nine', 1)->limit(9)->get();

            // Get banner
            $banner = Banner::find(1);

            // Get all categories ordered by display_order (backend settings)
            $allCategories = Category::where('is_active', true)
                ->orderBy('display_order', 'asc')
                ->orderBy('category_name', 'ASC')
                ->get();
            $categories = $allCategories; // Alias for backward compatibility

            // Get general news for section two
            $news = $approvedQuery->clone()->with(['category', 'user'])
                ->orderBy('id', 'ASC')->limit(8)->get();

            return compact('newnewspost', 'newspopular', 'categoryNews', 'banner',
                          'news_slider', 'section_three', 'section_nine', 'allCategories', 'news', 'categories');
        });

        // Extract data for backward compatibility
        extract($data['categoryNews']);

        // Check if this is the public homepage (not subscriber dashboard)
        if (!auth()->check() || !request()->is('subscriber/*')) {
            // Use aggressive smart layout for public homepage
            return view('frontend.index_aggressive_smart', array_merge($data, compact(
                'skip_cat_0', 'skip_news_0', 'skip_cat_1', 'skip_news_1',
                'skip_cat_2', 'skip_news_2', 'skip_cat_4', 'skip_news_4',
                'skip_cat_6', 'skip_news_6', 'skip_cat_7', 'skip_news_7',
                'skip_cat_8', 'skip_news_8'
            )));
        }

        // Use original modern layout for subscriber dashboard pages
        return view('frontend.index_modern', array_merge($data, compact(
            'skip_cat_0', 'skip_news_0', 'skip_cat_1', 'skip_news_1',
            'skip_cat_2', 'skip_news_2', 'skip_cat_4', 'skip_news_4',
            'skip_cat_6', 'skip_news_6', 'skip_cat_7', 'skip_news_7',
            'skip_cat_8', 'skip_news_8'
        )));
    } // End Method

    public function IndexV2(){
        // Cache the homepage data for 5 minutes (reduced for fresh content)
        $cacheKey = 'homepage_v2_data';
        $data = Cache::remember($cacheKey, 300, function () {
            // Get approved posts base query
            $approvedQuery = NewsPost::where('status', 1)->where('approval_status', 'approved');

            // Get recent and popular posts with eager loading
            $newnewspost = $approvedQuery->clone()->with(['category', 'user'])
                ->forHomepage()->limit(12)->get();
            $newspopular = $approvedQuery->clone()->with(['category', 'user'])
                ->orderBy('view_count', 'DESC')->limit(10)->get();

            // Get special posts for designated sections
            $featuredPosts = $approvedQuery->clone()->with(['category', 'user'])
                ->featured()->ordered()->limit(8)->get();
            $trendingPosts = $approvedQuery->clone()->with(['category', 'user'])
                ->trending()->ordered()->limit(8)->get();
            $pinnedPosts = $approvedQuery->clone()->with(['category', 'user'])
                ->pinned()->ordered()->limit(5)->get();

            // Get active advertisements for homepage
            $advertisements = \App\Models\Advertisement::where('is_active', true)
                ->where(function($query) {
                    $query->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                })
                ->where(function($query) {
                    $query->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                })
                ->where(function($query) {
                    $query->whereNull('target_pages')
                          ->orWhereJsonContains('target_pages', 'homepage')
                          ->orWhereJsonContains('target_pages', 'all');
                })
                ->orderBy('display_order', 'asc')
                ->get();

            // Get active sponsored ads for homepage
            $sponsoredAds = \App\Models\SponsoredAd::where('is_active', true)
                ->where(function($query) {
                    $query->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                })
                ->where(function($query) {
                    $query->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                })
                ->whereIn('placement', ['homepage_top', 'homepage_middle', 'homepage_bottom', 'sidebar'])
                ->orderBy('display_order', 'asc')
                ->get()
                ->groupBy('placement');

            // Get category-wise news for different sections
            $categoryNews = [];
            $categories = Category::with([
                'subcategories' => function($query) {
                    $query->withCount([
                        'newsPosts as news_posts_count' => function($subQuery) {
                            $subQuery->where('status', 1)->where('approval_status', 'approved');
                        }
                    ])->orderBy('subcategory_name', 'ASC');
                }
            ])->withCount([
                'newsPosts as news_posts_count' => function($query) {
                    $query->where('status', 1)->where('approval_status', 'approved');
                }
            ])->orderBy('category_name', 'ASC')->get();

            foreach ($categories->take(9) as $index => $category) {
                $categoryNews["skip_cat_$index"] = $category;
                $categoryNews["skip_news_$index"] = $approvedQuery->clone()
                    ->with(['category', 'user'])
                    ->where('category_id', $category->id)
                    ->orderBy('id', 'DESC')
                    ->limit(6)
                    ->get();
            }

            // Get special section posts with proper ordering
            $news_slider = $approvedQuery->clone()->with(['category', 'user'])
                ->where('top_slider', 1)->ordered()->limit(10)->get();
            $section_three = $approvedQuery->clone()->with(['category', 'user'])
                ->where('first_section_three', 1)->ordered()->limit(6)->get();
            $section_nine = $approvedQuery->clone()->with(['category', 'user'])
                ->where('first_section_nine', 1)->ordered()->limit(12)->get();

            // Get banner
            $banner = Banner::find(1);

            // Get all categories for tabs (ordered by backend settings)
            $allCategories = Category::where('is_active', true)
                ->orderBy('display_order', 'asc')
                ->orderBy('category_name', 'ASC')
                ->get();

            // Get general news for section two
            $news = $approvedQuery->clone()->with(['category', 'user'])
                ->orderBy('id', 'DESC')->limit(12)->get();

            return compact('newnewspost', 'newspopular', 'categoryNews', 'banner',
                          'news_slider', 'section_three', 'section_nine', 'allCategories', 'news', 'categories',
                          'featuredPosts', 'trendingPosts', 'pinnedPosts', 'advertisements', 'sponsoredAds');
        });

        // Extract data for backward compatibility
        extract($data['categoryNews']);

        // Add interaction data to news posts (outside cache for real-time data)
        $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

        // Get fresh news data with proper ordering (not cached) - this is what the view actually uses
        $newnewspost = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->with(['category', 'user'])
            ->forHomepage() // Uses proper ordering: pinned first, then display_order, then date
            ->limit(12)
            ->get();

        foreach ($newnewspost as $post) {
            $post->likes_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'like')->count();
            $post->shares_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'share')->count();
            $post->comments_count = \App\Models\NewsComment::where('news_post_id', $post->id)
                ->approved()->count();

            $post->user_liked = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'like')->exists();

            $post->user_saved = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'save')->exists();
        }

        // Update the data array with fresh news data
        $data['newnewspost'] = $newnewspost;

        return view('frontend.index_facebook_style', array_merge($data, compact(
            'newnewspost', 'skip_cat_0', 'skip_news_0', 'skip_cat_1', 'skip_news_1',
            'skip_cat_2', 'skip_news_2', 'skip_cat_4', 'skip_news_4',
            'skip_cat_6', 'skip_news_6', 'skip_cat_7', 'skip_news_7',
            'skip_cat_8', 'skip_news_8'
        ), [
            'trendingPosts' => $data['trendingPosts'] ?? collect(),
            'featuredPosts' => $data['featuredPosts'] ?? collect(),
            'pinnedPosts' => $data['pinnedPosts'] ?? collect(),
            'newspopular' => $data['newspopular'] ?? collect()
        ]));
    }

    /**
     * Load more posts for infinite scroll (AJAX endpoint)
     */
    public function loadMorePosts(Request $request)
    {
        $page = $request->get('page', 1);
        $perPage = 6; // Number of posts per load
        $offset = ($page - 1) * $perPage;

        // Get posts with proper ordering (pinned first, then by display order, then by date)
        $posts = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->with(['category', 'user'])
            ->forHomepage() // Uses the ordered scope
            ->skip($offset)
            ->take($perPage)
            ->get();

        // Add interaction data to posts
        $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

        foreach ($posts as $post) {
            // Add interaction data (likes, shares, etc.)
            $post->user_has_liked = false;
            $post->user_has_shared = false;
            $post->likes_count = 0;
            $post->shares_count = 0;
            $post->comments_count = 0;
        }

        // Check if there are more posts
        $totalPosts = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->count();

        $hasMore = ($offset + $perPage) < $totalPosts;

        return response()->json([
            'success' => true,
            'posts' => $posts,
            'hasMore' => $hasMore,
            'nextPage' => $page + 1
        ]);
    } // End Method


    public function NewsDetails($id,$slug){
        // Cache news details for 30 minutes
        $cacheKey = "news_details_{$id}";
        $data = Cache::remember($cacheKey, 1800, function () use ($id) {
            $news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'subcategory', 'user'])
                ->findOrFail($id);

            $tags_all = explode(',', $news->tags);

            // Get related news from same category
            $related_news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->where('category_id', $news->category_id)
                ->where('id', '!=', $id)
                ->with(['category', 'user'])
                ->orderBy('id', 'DESC')
                ->limit(6)
                ->get();

            // Get all categories for sidebar
            $categories = Category::withCount(['newsPosts' => function($query) {
                $query->where('status', 1)->where('approval_status', 'approved');
            }])->orderBy('category_name', 'ASC')->get();

            // Get popular news for sidebar
            $popular_news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('view_count', 'DESC')
                ->limit(5)
                ->get();

            // Get recent news for sidebar
            $recent_news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('id', 'DESC')
                ->limit(5)
                ->get();

            // Legacy data for backward compatibility
            $relatedNews = $related_news; // Alias for backward compatibility
            $newnewspost = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('id', 'DESC')
                ->limit(8)
                ->get();

            $newspopular = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('view_count', 'DESC')
                ->limit(8)
                ->get();

            return compact('news', 'tags_all', 'related_news', 'categories', 'popular_news', 'recent_news',
                          'relatedNews', 'newnewspost', 'newspopular');
        });

        // Handle view count increment (not cached)
        $newsKey = "blog{$data['news']->id}";
        if (!Session::has($newsKey)) {
           $data['news']->increment('view_count');
           Session::put($newsKey, 1);
           // Clear cache to update view count
           Cache::forget($cacheKey);
        }

        return view('frontend.news.news_details', $data);
    }// End Method




    public function CatWiseNews($id,$slug){
        // Cache category news data for 15 minutes
        $cacheKey = "category_news_{$id}";
        $data = Cache::remember($cacheKey, 900, function () use ($id) {
            // Get category info
            $breadcat = Category::findOrFail($id);

            // Get paginated news for this category
            $news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->where('category_id', $id)
                ->with(['category', 'user'])
                ->orderBy('id', 'DESC')
                ->paginate(12);

            // Get all categories for sidebar
            $categories = Category::withCount(['newsPosts' => function($query) {
                $query->where('status', 1)->where('approval_status', 'approved');
            }])->orderBy('category_name', 'ASC')->get();

            // Get popular news for sidebar
            $popular_news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('view_count', 'DESC')
                ->limit(5)
                ->get();

            // Get recent news for sidebar
            $recent_news = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user'])
                ->orderBy('id', 'DESC')
                ->limit(5)
                ->get();

            // Legacy data for backward compatibility
            $newstwo = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->where('category_id', $id)
                ->orderBy('id', 'DESC')
                ->limit(2)
                ->get();

            $newnewspost = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->orderBy('id', 'DESC')
                ->limit(8)
                ->get();

            $newspopular = NewsPost::where('status', 1)
                ->where('approval_status', 'approved')
                ->orderBy('view_count', 'DESC')
                ->limit(8)
                ->get();

            return compact('news', 'breadcat', 'categories', 'popular_news', 'recent_news',
                          'newstwo', 'newnewspost', 'newspopular');
        });

        return view('frontend.news.category_news_modern', $data);

    }// End Method


     public function SubCatWiseNews($id,$slug){

        $news = NewsPost::where('status',1)->where('approval_status','approved')->where('subcategory_id',$id)->orderBy('id','DESC')->get();

        $breadsubcat = Subcategory::where('id',$id)->first();

        $newstwo = NewsPost::where('status',1)->where('approval_status','approved')->where('subcategory_id',$id)->orderBy('id','DESC')->limit(2)->get();

        $newnewspost = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('id','DESC')->limit(8)->get();
        $newspopular = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('view_count','DESC')->limit(8)->get();

        return view('frontend.news.subcategory_news_modern',compact('news','breadsubcat','newstwo','newnewspost','newspopular'));

    }// End Method


    public function Change(Request $request){

        App::setLocale($request->lang);
        session()->put('locale',$request->lang);

        return redirect()->back();

    }// End Method

    public function ChangeLanguage(Request $request){
        $language = $request->input('language');

        if (in_array($language, ['en', 'bn', 'hi', 'fr'])) {
            Session::put('locale', $language);
            App::setLocale($language);

            return response()->json(['success' => true, 'message' => 'Language changed successfully']);
        }

        return response()->json(['success' => false, 'message' => 'Invalid language']);
    }// End Method

    public function Search(Request $request){
        $query = $request->get('search');

        if (empty($query)) {
            return redirect()->back()->with('error', 'Please enter a search term');
        }

        // Get recent and popular news for sidebar
        $newnewspost = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->orderBy('id', 'DESC')
            ->limit(8)
            ->get();

        $newspopular = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->orderBy('id', 'ASC')
            ->limit(8)
            ->get();

        // Search in news posts
        $news = NewsPost::where('status', 1)
            ->where('approval_status', 'approved')
            ->where(function($q) use ($query) {
                $q->where('news_title', 'LIKE', '%' . $query . '%')
                  ->orWhere('news_details', 'LIKE', '%' . $query . '%')
                  ->orWhere('tags', 'LIKE', '%' . $query . '%');
            })
            ->latest()
            ->paginate(12);

        return view('frontend.news.search_results_modern', compact('news', 'query', 'newnewspost', 'newspopular'));
    }// End Method

    public function SearchByDate(Request $request){

        $date = new DateTime($request->date);
        $formatDate = $date->format('d-m-Y');

        $newnewspost = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('id','DESC')->limit(8)->get();
        $newspopular = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('view_count','DESC')->limit(8)->get();

        $news = NewsPost::where('status',1)->where('approval_status','approved')->where('post_date',$formatDate)->latest()->get();
        return view('frontend.news.search_by_date_modern',compact('news','formatDate','newnewspost','newspopular'));

    }// End Method

    public function NewsSearch(Request $request){


        $request->validate(['search' => "required"]);

        $item = $request->search;

        $news = NewsPost::where('status',1)->where('approval_status','approved')->where('news_title','LIKE',"%$item%")->get();
        $newnewspost = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('id','DESC')->limit(8)->get();
        $newspopular = NewsPost::where('status',1)->where('approval_status','approved')->orderBy('view_count','DESC')->limit(8)->get();

        return view('frontend.news.search_modern',compact('news','newnewspost','newspopular','item'));


    }// End Method


    public function ReporterNews($id){

        $reporter = User::findOrFail($id);

        $news = NewsPost::where('status',1)->where('approval_status','approved')->where('user_id',$id)->get();
        return view('frontend.reporter.reporter_news_post_modern',compact('reporter','news'));

    }// End Method

    // Facebook-style Category Page
    public function CatWiseNewsV2($id, $slug){
        $category = Category::findOrFail($id);
        $breadcat = Category::where('id', $id)->first();
        $catnews = NewsPost::where('category_id', $id)->where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->paginate(12);
        $categories = Category::with([
            'subcategories' => function($query) {
                $query->withCount([
                    'newsPosts as news_posts_count' => function($subQuery) {
                        $subQuery->where('status', 1)->where('approval_status', 'approved');
                    }
                ])->orderBy('subcategory_name', 'ASC');
            }
        ])->withCount([
            'newsPosts as news_posts_count' => function($query) {
                $query->where('status', 1)->where('approval_status', 'approved');
            }
        ])->orderBy('category_name', 'ASC')->get();
        $newspopular = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('view_count', 'DESC')->limit(8)->get();
        $newnewspost = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->limit(6)->get();

        // Add interaction data to category news posts
        $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

        foreach ($catnews as $post) {
            $post->likes_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'like')->count();
            $post->shares_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'share')->count();
            $post->comments_count = \App\Models\NewsComment::where('news_post_id', $post->id)
                ->approved()->count();

            $post->user_liked = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'like')->exists();

            $post->user_saved = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'save')->exists();
        }

        // Get active advertisements for category pages
        $advertisements = \App\Models\Advertisement::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->where(function($query) {
                $query->whereNull('target_pages')
                      ->orWhereJsonContains('target_pages', 'category')
                      ->orWhereJsonContains('target_pages', 'all');
            })
            ->orderBy('display_order', 'asc')
            ->get();

        // Get active sponsored ads for category pages
        $sponsoredAds = \App\Models\SponsoredAd::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->whereIn('placement', ['category_top', 'category_middle', 'category_bottom', 'sidebar'])
            ->orderBy('display_order', 'asc')
            ->get()
            ->groupBy('placement');

        return view('frontend.facebook_category', compact('category', 'breadcat', 'catnews', 'categories', 'newspopular', 'newnewspost', 'advertisements', 'sponsoredAds'));
    } // End Method

    // Facebook-style News Details Page
    public function NewsDetailsV2($id, $slug){
        $news = NewsPost::findOrFail($id);

        // Increment view count
        $news->increment('view_count');

        // Get interaction counts
        $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

        $likesCount = \App\Models\NewsInteraction::where('news_post_id', $id)->where('interaction_type', 'like')->count();
        $sharesCount = \App\Models\NewsInteraction::where('news_post_id', $id)->where('interaction_type', 'share')->count();
        $commentsCount = \App\Models\NewsComment::where('news_post_id', $id)->approved()->count();

        // Check user interactions
        $userLiked = \App\Models\NewsInteraction::where('news_post_id', $id)
            ->where('user_identifier', $userIdentifier)
            ->where('interaction_type', 'like')
            ->exists();

        $userSaved = \App\Models\NewsInteraction::where('news_post_id', $id)
            ->where('user_identifier', $userIdentifier)
            ->where('interaction_type', 'save')
            ->exists();

        $categories = Category::with([
            'subcategories' => function($query) {
                $query->withCount([
                    'newsPosts as news_posts_count' => function($subQuery) {
                        $subQuery->where('status', 1)->where('approval_status', 'approved');
                    }
                ])->orderBy('subcategory_name', 'ASC');
            }
        ])->withCount([
            'newsPosts as news_posts_count' => function($query) {
                $query->where('status', 1)->where('approval_status', 'approved');
            }
        ])->orderBy('category_name', 'ASC')->get();

        $newspopular = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('view_count', 'DESC')->limit(8)->get();
        $related_news = NewsPost::where('category_id', $news->category_id)->where('id', '!=', $id)->where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->limit(6)->get();
        $newnewspost = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->limit(6)->get();

        // Get comments
        $comments = \App\Models\NewsComment::where('news_post_id', $id)
            ->whereNull('parent_id')
            ->approved()
            ->with(['user', 'replies.user'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get active advertisements for news detail pages
        $advertisements = \App\Models\Advertisement::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->where(function($query) {
                $query->whereNull('target_pages')
                      ->orWhereJsonContains('target_pages', 'news_detail')
                      ->orWhereJsonContains('target_pages', 'all');
            })
            ->orderBy('display_order', 'asc')
            ->get();

        // Get active sponsored ads for news detail pages
        $sponsoredAds = \App\Models\SponsoredAd::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('start_date')
                      ->orWhere('start_date', '<=', now());
            })
            ->where(function($query) {
                $query->whereNull('end_date')
                      ->orWhere('end_date', '>=', now());
            })
            ->whereIn('placement', ['news_detail_top', 'news_detail_middle', 'news_detail_bottom', 'sidebar'])
            ->orderBy('display_order', 'asc')
            ->get()
            ->groupBy('placement');

        return view('frontend.facebook_news_details', compact(
            'news', 'categories', 'newspopular', 'related_news', 'newnewspost',
            'likesCount', 'sharesCount', 'commentsCount', 'userLiked', 'userSaved', 'comments',
            'advertisements', 'sponsoredAds'
        ));
    } // End Method

    // Facebook-style Subcategory Page
    public function SubCatWiseNewsV2($id, $slug){
        $subcategory = Subcategory::findOrFail($id);
        $breadsubcat = Subcategory::where('id', $id)->first();
        $subcatnews = NewsPost::where('subcategory_id', $id)->where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->paginate(12);
        $categories = Category::with([
            'subcategories' => function($query) {
                $query->withCount([
                    'newsPosts as news_posts_count' => function($subQuery) {
                        $subQuery->where('status', 1)->where('approval_status', 'approved');
                    }
                ])->orderBy('subcategory_name', 'ASC');
            }
        ])->withCount([
            'newsPosts as news_posts_count' => function($query) {
                $query->where('status', 1)->where('approval_status', 'approved');
            }
        ])->orderBy('category_name', 'ASC')->get();

        $newspopular = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('view_count', 'DESC')->limit(8)->get();
        $newnewspost = NewsPost::where('status', 1)->where('approval_status', 'approved')->orderBy('id', 'DESC')->limit(6)->get();

        // Add interaction data to subcategory news posts
        $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

        foreach ($subcatnews as $post) {
            $post->likes_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'like')->count();
            $post->shares_count = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('interaction_type', 'share')->count();
            $post->comments_count = \App\Models\NewsComment::where('news_post_id', $post->id)
                ->approved()->count();

            $post->user_liked = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'like')->exists();

            $post->user_saved = \App\Models\NewsInteraction::where('news_post_id', $post->id)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'save')->exists();
        }

        return view('frontend.facebook_subcategory', compact('subcategory', 'breadsubcat', 'subcatnews', 'categories', 'newspopular', 'newnewspost'));
    } // End Method

    /**
     * Toggle like for a news post
     */
    public function toggleLike(Request $request)
    {
        try {
            $newsPostId = $request->input('news_post_id');
            $action = $request->input('action'); // 'like' or 'unlike'

            $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

            $interaction = \App\Models\NewsInteraction::where('news_post_id', $newsPostId)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'like')
                ->first();

            if ($action === 'like' && !$interaction) {
                \App\Models\NewsInteraction::create([
                    'news_post_id' => $newsPostId,
                    'user_identifier' => $userIdentifier,
                    'interaction_type' => 'like',
                    'user_id' => Auth::id()
                ]);
                $liked = true;
            } elseif ($action === 'unlike' && $interaction) {
                $interaction->delete();
                $liked = false;
            } else {
                $liked = $interaction ? true : false;
            }

            $likesCount = \App\Models\NewsInteraction::where('news_post_id', $newsPostId)
                ->where('interaction_type', 'like')
                ->count();

            return response()->json([
                'success' => true,
                'liked' => $liked,
                'likes_count' => $likesCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle like'
            ], 500);
        }
    }

    /**
     * Record share for a news post
     */
    public function recordShare(Request $request)
    {
        try {
            $newsPostId = $request->input('news_post_id');
            $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

            \App\Models\NewsInteraction::create([
                'news_post_id' => $newsPostId,
                'user_identifier' => $userIdentifier,
                'interaction_type' => 'share',
                'user_id' => Auth::id()
            ]);

            $sharesCount = \App\Models\NewsInteraction::where('news_post_id', $newsPostId)
                ->where('interaction_type', 'share')
                ->count();

            return response()->json([
                'success' => true,
                'shares_count' => $sharesCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record share'
            ], 500);
        }
    }

    /**
     * Add comment to a news post
     */
    public function addComment(Request $request)
    {
        try {
            $request->validate([
                'news_post_id' => 'required|exists:news_posts,id',
                'comment' => 'required|string|max:1000'
            ]);

            \App\Models\NewsComment::create([
                'news_post_id' => $request->input('news_post_id'),
                'user_id' => Auth::id(),
                'user_name' => Auth::check() ? Auth::user()->name : 'Anonymous',
                'user_email' => Auth::check() ? Auth::user()->email : null,
                'comment' => $request->input('comment'),
                'status' => 'pending' // Requires approval
            ]);

            $commentsCount = \App\Models\NewsComment::where('news_post_id', $request->input('news_post_id'))
                ->approved()
                ->count();

            return response()->json([
                'success' => true,
                'message' => 'Comment submitted for approval',
                'comments_count' => $commentsCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add comment'
            ], 500);
        }
    }

    /**
     * Filter category posts by type
     */
    public function filterCategoryPosts(Request $request, $id)
    {
        try {
            $type = $request->get('type', 'all');
            $query = NewsPost::where('category_id', $id)
                ->where('status', 1)
                ->where('approval_status', 'approved')
                ->with(['category', 'user']);

            switch ($type) {
                case 'recent':
                    $query->orderBy('created_at', 'DESC');
                    break;
                case 'popular':
                    $query->orderBy('view_count', 'DESC');
                    break;
                case 'trending':
                    $query->withCount(['interactions as likes_count' => function($q) {
                        $q->where('interaction_type', 'like');
                    }])->orderBy('likes_count', 'DESC');
                    break;
                default:
                    $query->orderBy('id', 'DESC');
            }

            $posts = $query->limit(20)->get();

            return response()->json([
                'success' => true,
                'posts' => $posts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to filter posts'
            ], 500);
        }
    }

    /**
     * Toggle category follow
     */
    public function toggleCategoryFollow(Request $request)
    {
        try {
            $categoryId = $request->input('category_id');
            $action = $request->input('action');

            // For now, just return success
            // In a real app, you'd store this in a user_category_follows table

            return response()->json([
                'success' => true,
                'action' => $action,
                'message' => $action === 'follow' ? 'Following category' : 'Unfollowed category'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle follow'
            ], 500);
        }
    }

    /**
     * Add reaction to a news post
     */
    public function addReaction(Request $request)
    {
        try {
            $newsPostId = $request->input('news_post_id');
            $reaction = $request->input('reaction');

            $userIdentifier = Auth::check() ? 'user_' . Auth::id() : 'ip_' . request()->ip();

            // Remove existing reaction from this user for this post
            \App\Models\NewsInteraction::where('news_post_id', $newsPostId)
                ->where('user_identifier', $userIdentifier)
                ->where('interaction_type', 'like')
                ->delete();

            // Add new reaction
            \App\Models\NewsInteraction::create([
                'news_post_id' => $newsPostId,
                'user_identifier' => $userIdentifier,
                'interaction_type' => 'like',
                'reaction_type' => $reaction,
                'user_id' => Auth::id()
            ]);

            // Get reaction counts
            $reactions = \App\Models\NewsInteraction::where('news_post_id', $newsPostId)
                ->where('interaction_type', 'like')
                ->selectRaw('reaction_type, COUNT(*) as count')
                ->groupBy('reaction_type')
                ->pluck('count', 'reaction_type')
                ->toArray();

            return response()->json([
                'success' => true,
                'reactions' => $reactions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add reaction'
            ], 500);
        }
    }

    /**
     * Get comments for a news post
     */
    public function getComments($id)
    {
        try {
            $comments = \App\Models\NewsComment::where('news_post_id', $id)
                ->where('status', 'approved')
                ->with('user')
                ->orderBy('created_at', 'DESC')
                ->get()
                ->map(function($comment) {
                    return [
                        'id' => $comment->id,
                        'comment' => $comment->comment,
                        'user_name' => $comment->user_name,
                        'user_avatar' => $comment->user && $comment->user->photo
                            ? url('upload/admin_images/' . $comment->user->photo)
                            : url('upload/no_image.jpg'),
                        'created_at' => $comment->created_at,
                        'likes_count' => 0 // Placeholder for comment likes
                    ];
                });

            return response()->json([
                'success' => true,
                'comments' => $comments
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load comments'
            ], 500);
        }
    }

}
  