<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <?php
    $seo = App\Models\SeoSetting::find(1);
    ?>
    
    <title><?php echo $__env->yieldContent('title'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="title" content="<?php echo e($seo->meta_title ?? 'NitiKotha - Modern News Portal'); ?>">
    <meta name="author" content="<?php echo e($seo->meta_author ?? 'NitiKotha'); ?>">
    <meta name="keywords" content="<?php echo e($seo->meta_keyword ?? 'news, breaking news, latest news'); ?>">
    <meta name="description" content="<?php echo e($seo->meta_description ?? 'Stay updated with the latest news and breaking stories from around the world.'); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?php echo $__env->yieldContent('title'); ?>">
    <meta property="og:description" content="<?php echo e($seo->meta_description ?? 'Stay updated with the latest news and breaking stories from around the world.'); ?>">
    <meta property="og:image" content="<?php echo e(asset('frontend/assets/images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $__env->yieldContent('title'); ?>">
    <meta name="twitter:description" content="<?php echo e($seo->meta_description ?? 'Stay updated with the latest news and breaking stories from around the world.'); ?>">
    <meta name="twitter:image" content="<?php echo e(asset('frontend/assets/images/og-image.jpg')); ?>">
    
    <!-- Favicon -->
    <?php
        $siteFavicon = \App\Models\SiteSetting::get('site_favicon');
        $faviconPath = $siteFavicon ? asset($siteFavicon) : asset('frontend/assets/images/favicon.gif');
    ?>
    <link rel="shortcut icon" href="<?php echo e($faviconPath); ?>" type="image/x-icon">
    <link rel="apple-touch-icon" href="<?php echo e(asset('frontend/assets/images/apple-touch-icon.png')); ?>">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="<?php echo e(asset('frontend/assets/css/modern-base.css')); ?>" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    
    <!-- Critical CSS Variables Only -->
    <style>
        /* Essential CSS variables for immediate use */
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --text-muted: #9ca3af;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        /* Header styles moved to external CSS file */
        
        /* Hero Section */
        .hero-section {
            padding: 2rem 0;
            background: var(--bg-white);
        }
        
        .hero-slider {
            position: relative;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }
        
        .hero-slide {
            position: relative;
            height: 500px;
            display: flex;
            align-items: end;
        }
        
        .hero-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .hero-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            padding: 2rem;
            color: white;
        }
        
        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .hero-title a {
            color: white;
            text-decoration: none;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .main-nav {
                display: none;
            }
            
            .hero-title {
                font-size: 1.75rem;
            }
            
            .hero-content {
                padding: 1rem;
            }
        }
    </style>
    
    <!-- Essential CSS loaded immediately -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Aggressive Smart Homepage CSS - Only for public homepage -->
    <?php if(!auth()->check() || !request()->is('subscriber/*')): ?>
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/aggressive-smart-homepage.css')); ?>?v=<?php echo e(time()); ?>">
    <?php endif; ?>

    <!-- Premium Design System - Unified CSS -->
    <style>
        /* CSS Reset and Base Styles */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
            overflow-x: hidden;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Unified Container System */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .container-fluid {
            width: 100%;
            padding: 0 1rem;
        }

        /* Modern Grid System */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-cols-1 { grid-template-columns: 1fr; }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        .grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

        /* Responsive Grid */
        .grid-responsive {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        /* Flex System */
        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .flex-wrap {
            flex-wrap: wrap;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .gap-1 { gap: 0.25rem; }
        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 0.75rem; }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }
        .gap-8 { gap: 2rem; }

        /* Premium Header Styles */
        .premium-header, .modern-header {
            background: #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid #e5e7eb;
        }

        .header-top {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 1px solid #e5e7eb;
            padding: 0.75rem 0;
        }

        .header-top-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
        }

        .header-left, .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .header-date, .weather-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
            font-weight: 500;
        }

        .header-date i {
            color: #3b82f6;
        }

        .weather-info i {
            color: #f59e0b;
        }

        .language-selector select {
            border: 1px solid #e5e7eb;
            background: #ffffff;
            color: #6b7280;
            font-size: 0.875rem;
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }

        .language-selector select:hover {
            border-color: #3b82f6;
        }

        .header-social {
            display: flex;
            gap: 0.5rem;
        }

        .social-link {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }

        .social-link:hover {
            color: #ffffff;
            transform: translateY(-2px);
        }

        .social-link.facebook:hover { background: #1877f2; border-color: #1877f2; }
        .social-link.twitter:hover { background: #1da1f2; border-color: #1da1f2; }
        .social-link.youtube:hover { background: #ff0000; border-color: #ff0000; }
        .social-link.instagram:hover { background: #e4405f; border-color: #e4405f; }

        .header-main {
            padding: 1.5rem 0;
            background: #ffffff;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 2rem;
        }

        .header-logo {
            flex-shrink: 0;
        }

        .logo, .logo-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }

        .logo-image, .logo-img {
            height: 50px;
            width: auto;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1;
        }

        .logo-tagline {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
        }

        .header-search {
            flex: 1;
            max-width: 500px;
        }

        .search-form {
            position: relative;
        }

        .search-input-group, .search-container {
            display: flex;
            align-items: center;
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .search-input-group:focus-within, .search-container:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            outline: none;
            color: #1f2937;
        }

        .search-input::placeholder {
            color: #9ca3af;
        }

        .search-button, .search-btn, .search-submit {
            background: #3b82f6;
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .search-button:hover, .search-btn:hover, .search-submit:hover {
            background: #1e40af;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-shrink: 0;
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: #dc2626;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .login-btn, .auth-btn {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
            border-radius: 0.75rem;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .login-btn:hover, .auth-btn:hover {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: translateY(-2px);
        }

        .menu-toggle, .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            flex-direction: column;
            gap: 0.25rem;
        }

        .hamburger-line {
            width: 24px;
            height: 3px;
            background: #1f2937;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        /* Modern Categories Navigation with Trending News */
        .categories-nav {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 1px solid #e5e7eb;
            padding: 2rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .nav-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            align-items: start;
        }

        /* Categories Column */
        .categories-column {
            background: #ffffff;
            overflow: hidden;
            max-height: 600px;
            overflow-y: auto;
        }

        .column-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .column-title {
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .categories-list {
            padding: 0.5rem 0;
        }

        .category-group {
            border-bottom: 1px solid #f1f5f9;
        }

        .category-group:last-child {
            border-bottom: none;
        }

        /* Trending Columns */
        .trending-columns {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        .trending-column {
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid #e5e7eb;
            height: fit-content;
        }

        .category-item {
            transition: all 0.3s ease;
        }

        .category-item.featured {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            margin: 0.5rem;
            border-radius: 0.75rem;
        }

        .category-item.featured .category-link {
            color: white;
        }

        .category-item.featured .category-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .category-item.featured .category-count {
            color: rgba(255, 255, 255, 0.8);
        }

        .category-item:not(.featured):hover {
            background: #f8fafc;
        }

        .category-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: #1f2937;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-link:hover {
            color: #3b82f6;
        }

        .category-link.active {
            color: #3b82f6;
            background: #f0f9ff;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3b82f6;
            font-size: 1rem;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .category-link:hover .category-icon {
            background: #3b82f6;
            color: white;
            transform: scale(1.05);
        }

        .category-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            position: relative;
        }

        .category-name {
            font-size: 0.95rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }

        .category-count {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
        }

        .expand-icon {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 0.75rem;
            transition: transform 0.3s ease;
        }

        /* Subcategories */
        .subcategories {
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .subcategory-item {
            border-bottom: 1px solid #e5e7eb;
        }

        .subcategory-item:last-child {
            border-bottom: none;
        }

        .subcategory-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem 0.75rem 3rem;
            text-decoration: none;
            color: #4b5563;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .subcategory-link:hover {
            color: #3b82f6;
            background: #f0f9ff;
        }

        .subcategory-icon {
            color: #9ca3af;
            font-size: 0.75rem;
            width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .subcategory-name {
            flex: 1;
            font-weight: 500;
        }

        .subcategory-count {
            font-size: 0.75rem;
            color: #9ca3af;
            background: #e5e7eb;
            padding: 0.125rem 0.5rem;
            border-radius: 1rem;
            font-weight: 500;
        }

        /* Trending News Styles */
        .trending-news {
            padding: 0.5rem 0;
        }

        .trending-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-item:hover {
            background: #f8fafc;
        }

        .trending-image {
            position: relative;
            width: 80px;
            height: 60px;
            border-radius: 0.5rem;
            overflow: hidden;
            flex-shrink: 0;
        }

        .trending-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .trending-badge {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            color: white;
            background: #dc2626;
        }

        .trending-badge.popular {
            background: #f59e0b;
        }

        .trending-badge.recent {
            background: #10b981;
        }

        .trending-content {
            flex: 1;
            min-width: 0;
        }

        .trending-title {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            line-height: 1.3;
        }

        .trending-title a {
            color: #1f2937;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .trending-title a:hover {
            color: #3b82f6;
        }

        .trending-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.75rem;
        }

        .trending-category {
            color: #3b82f6;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .trending-time {
            color: #6b7280;
        }

        .trending-views {
            color: #9ca3af;
        }

        /* Main Content Area */
        .main-content {
            min-height: calc(100vh - 400px);
            background: #ffffff;
            padding: 0;
            margin: 0;
        }

        /* Page Content */
        .page-content {
            padding: 3rem 0;
            min-height: 60vh;
        }

        .content-placeholder {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .placeholder-content h1 {
            font-size: 3rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .placeholder-content p {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        /* Subscriber Dashboard Styles */
        .dashboard-welcome {
            margin-bottom: 2rem;
        }

        .welcome-card {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            border-radius: 1rem;
            padding: 3rem 2rem;
            text-align: center;
            color: white;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }

        .welcome-content h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }

        .welcome-content p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            color: white;
        }

        .create-post-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .create-post-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            color: white;
        }

        /* Create Post Welcome */
        .create-post-welcome {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .form-tips {
            display: flex;
            gap: 1.5rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .tip-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .tip-item i {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Posts List Welcome */
        .posts-welcome {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .posts-stats {
            display: flex;
            gap: 2rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .posts-stats .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .posts-stats .stat-item i {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Edit Post Welcome */
        .edit-post-welcome {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        /* Edit Post Form Styles */
        .edit-post-column {
            grid-column: 1 / -1;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .edit-post-form-container {
            padding: 2rem;
        }

        .current-image-display {
            margin-bottom: 1rem;
        }

        .current-image {
            max-width: 300px;
            height: auto;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        /* CKEditor Styles */
        .ckeditor {
            min-height: 300px !important;
        }

        .ck-editor__editable {
            min-height: 300px !important;
            border-radius: 0.75rem !important;
            border: 2px solid #e5e7eb !important;
        }

        .ck-editor__editable:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        /* Profile Welcome */
        .profile-welcome {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        }

        /* Profile Form Styles */
        .profile-edit-column {
            grid-column: 1 / -1;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .profile-form-container {
            padding: 2rem;
        }

        .modern-profile-form {
            max-width: 800px;
            margin: 0 auto;
        }

        /* Profile Avatar Section */
        .profile-avatar-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 3rem;
            padding: 2rem;
            background: #f8fafc;
            border-radius: 1rem;
            border: 1px solid #e5e7eb;
        }

        .current-avatar {
            position: relative;
        }

        .avatar-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .default-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #9ca3af;
            border: 4px solid #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .avatar-upload {
            flex: 1;
        }

        .avatar-upload-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .avatar-upload-btn:hover {
            background: #1e40af;
            transform: translateY(-2px);
        }

        .avatar-input {
            display: none;
        }

        .avatar-help {
            color: #6b7280;
            font-size: 0.8rem;
        }

        /* Form Sections */
        .form-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: #ffffff;
            border-radius: 1rem;
            border: 1px solid #f1f5f9;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .section-title i {
            color: #3b82f6;
            font-size: 1.1rem;
        }

        /* Form Rows */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .profile-avatar-section {
                flex-direction: column;
                text-align: center;
            }
        }

        /* Dashboard Stats in Categories Navigation */
        .dashboard-stats {
            padding: 1rem 0;
        }

        .stat-card {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Recent Posts List */
        .recent-posts-list {
            padding: 0.5rem 0;
        }

        .post-item {
            display: flex;
            gap: 0.75rem;
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            transition: background 0.3s ease;
        }

        .post-item:hover {
            background: #f8fafc;
        }

        .post-item:last-child {
            border-bottom: none;
        }

        .post-image {
            position: relative;
            width: 60px;
            height: 45px;
            border-radius: 0.375rem;
            overflow: hidden;
            flex-shrink: 0;
        }

        .post-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-status {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            color: white;
        }

        .post-status.approved {
            background: #10b981;
        }

        .post-status.pending {
            background: #f59e0b;
        }

        .post-status.rejected {
            background: #ef4444;
        }

        .post-content {
            flex: 1;
            min-width: 0;
        }

        .post-title {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            line-height: 1.3;
        }

        .post-title a {
            color: #1f2937;
            text-decoration: none;
        }

        .post-title a:hover {
            color: #3b82f6;
        }

        .post-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.75rem;
        }

        .post-category {
            color: #3b82f6;
            font-weight: 600;
        }

        .post-status-text {
            color: #6b7280;
        }

        .post-time {
            color: #9ca3af;
        }

        .view-all-link {
            text-align: center;
            padding: 1rem;
            border-top: 1px solid #f1f5f9;
        }

        .view-all-link a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .no-posts {
            text-align: center;
            padding: 2rem 1rem;
            color: #6b7280;
        }

        .no-posts i {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .no-posts a {
            color: #3b82f6;
            text-decoration: none;
        }

        /* Quick Actions */
        .quick-actions-section {
            padding: 0.5rem 0;
        }

        .quick-actions-section .action-buttons {
            margin-bottom: 2rem;
            display: flex !important;
            flex-direction: column !important;
            gap: 0.75rem !important;
        }

        .quick-actions-section .action-btn {
            display: flex !important;
            align-items: center !important;
            gap: 0.75rem !important;
            padding: 1rem 1.25rem !important;
            margin-bottom: 0 !important;
            background: #f8fafc !important;
            border: 1px solid #e5e7eb !important;
            border-radius: 0.75rem !important;
            text-decoration: none !important;
            color: #1f2937 !important;
            transition: all 0.3s ease !important;
            font-weight: 600 !important;
            width: 100% !important;
            height: auto !important;
            font-size: 0.95rem !important;
            justify-content: flex-start !important;
        }

        .quick-actions-section .action-btn:hover {
            background: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
            transform: translateY(-1px) !important;
        }

        .quick-actions-section .action-btn.primary {
            background: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
        }

        .quick-actions-section .action-btn.primary:hover {
            background: #1e40af !important;
            border-color: #1e40af !important;
            color: white !important;
        }

        .quick-actions-section .action-btn span {
            color: inherit !important;
        }

        .quick-actions-section .action-btn.primary span {
            color: white !important;
        }

        /* Publishing Guidelines */
        .guidelines-section {
            background: #f8fafc;
            border-radius: 0.5rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
        }

        .guidelines-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .guidelines-title i {
            color: #3b82f6;
        }

        .guidelines-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .guidelines-list li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-size: 0.8rem;
            color: #4b5563;
        }

        .guidelines-list li:last-child {
            margin-bottom: 0;
        }

        .guidelines-list i {
            color: #10b981;
            font-size: 0.7rem;
        }

        /* Create Post Form Styles */
        .create-post-column {
            grid-column: 1 / -1;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .create-post-form-container {
            padding: 2rem;
        }

        .modern-post-form {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
        }

        .form-label i {
            color: #3b82f6;
            width: 16px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #ffffff;
            color: #1f2937;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 200px;
            font-family: inherit;
        }

        /* Image Upload Styles */
        .image-upload-area {
            position: relative;
            border: 2px dashed #d1d5db;
            border-radius: 0.75rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .image-upload-area:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .form-file {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
        }

        .upload-placeholder i {
            font-size: 2rem;
            color: #9ca3af;
        }

        .upload-placeholder span {
            font-weight: 500;
        }

        .upload-placeholder small {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .image-preview {
            position: relative;
            max-width: 300px;
            margin: 0 auto;
        }

        .image-preview img {
            width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        .remove-image {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .remove-image:hover {
            background: #dc2626;
        }

        /* Form Actions */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        .btn-primary, .btn-secondary {
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #1e40af;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #6b7280;
            border: 1px solid #e5e7eb;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            color: #1f2937;
        }

        /* Posts List Styles */
        .posts-list-column {
            grid-column: 1 / -1;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .posts-list-column .column-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .create-new-btn {
            background: #3b82f6 !important;
            color: white !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .create-new-btn span {
            color: white !important;
            font-weight: 600 !important;
        }

        .create-new-btn:hover {
            background: #1e40af !important;
            transform: translateY(-2px) !important;
            color: white !important;
        }

        .create-new-btn:hover span {
            color: white !important;
        }

        .back-dashboard-btn {
            background: #6b7280 !important;
            color: white !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .back-dashboard-btn span {
            color: white !important;
            font-weight: 600 !important;
        }

        .back-dashboard-btn:hover {
            background: #4b5563 !important;
            transform: translateY(-2px) !important;
            color: white !important;
        }

        .back-dashboard-btn:hover span {
            color: white !important;
        }

        .posts-table-container {
            padding: 2rem;
        }

        .posts-table-wrapper {
            overflow-x: auto;
            border-radius: 0.75rem;
            border: 1px solid #e5e7eb;
        }

        .posts-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .posts-table th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
            font-size: 0.875rem;
        }

        .posts-table td {
            padding: 1rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .posts-table tr:hover {
            background: #f8fafc;
        }

        .post-thumbnail {
            width: 60px;
            height: 45px;
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .post-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-title {
            margin: 0;
            font-size: 0.9rem;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.4;
        }

        .category-badge {
            background: #dbeafe;
            color: #1e40af;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-badge {
            background: #d1fae5;
            color: #065f46;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .approval-badge {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .approval-badge.approved {
            background: #d1fae5;
            color: #065f46;
        }

        .approval-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .approval-badge.rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .post-date {
            display: flex;
            flex-direction: column;
            gap: 0.125rem;
        }

        .post-date .date {
            font-weight: 500;
            color: #1f2937;
            font-size: 0.8rem;
        }

        .post-date .time {
            color: #6b7280;
            font-size: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .action-btn.view {
            background: #dbeafe;
            color: #1e40af;
        }

        .action-btn.view:hover {
            background: #3b82f6;
            color: white;
        }

        .action-btn.edit {
            background: #fef3c7;
            color: #92400e;
        }

        .action-btn.edit:hover {
            background: #f59e0b;
            color: white;
        }

        .action-btn.delete {
            background: #fee2e2;
            color: #991b1b;
        }

        .action-btn.delete:hover {
            background: #ef4444;
            color: white;
        }

        /* No Posts Message */
        .no-posts-message {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }

        .no-posts-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.5;
        }

        .no-posts-message h3 {
            font-size: 1.5rem;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .no-posts-message p {
            font-size: 1rem;
            margin-bottom: 2rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .create-first-post-btn {
            background: #3b82f6;
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .create-first-post-btn:hover {
            background: #1e40af;
            transform: translateY(-2px);
            color: white;
        }

        /* Pagination */
        .pagination-wrapper {
            margin-top: 2rem;
            display: flex;
            justify-content: center;
        }

        /* Premium Footer Styles - Full Width Background with Container Content */
        .premium-footer, .modern-footer {
            background: linear-gradient(135deg, #1e40af 0%, #1f2937 100%);
            color: #e2e8f0;
            margin-top: 3rem;
            border-top: 3px solid #3b82f6;
            width: 100vw !important;
            margin-left: calc(-50vw + 50%) !important;
            position: relative;
        }

        .footer-main {
            padding: 4rem 0 2rem;
            width: 100%;
        }

        .footer-container {
            margin: 0 !important;
            padding: 100px !important;
            width: 100%;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr;
            gap: 3rem;
            margin-bottom: 2rem;
            padding: 0 2rem;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .nav-layout {
                grid-template-columns: 280px 1fr;
                gap: 1.5rem;
            }

            .trending-columns {
                gap: 1rem;
            }
        }

        @media (max-width: 1024px) {
            .nav-layout {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .categories-column {
                max-height: 400px;
            }

            .trending-columns {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .categories-nav {
                padding: 1.5rem 0;
            }

            .nav-layout {
                gap: 1.5rem;
            }

            .trending-columns {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .categories-column {
                max-height: 300px;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .footer-grid {
                padding: 0 1rem;
            }

            .footer-bottom-content {
                padding: 0 1rem;
            }
        }

        @media (max-width: 480px) {
            .categories-nav {
                padding: 1rem 0;
            }

            .nav-layout {
                gap: 1rem;
            }

            .category-link {
                padding: 0.75rem 1rem;
            }

            .category-icon {
                width: 32px;
                height: 32px;
                font-size: 0.875rem;
            }

            .trending-item {
                padding: 0.75rem 1rem;
                gap: 0.75rem;
            }

            .trending-image {
                width: 60px;
                height: 45px;
            }

            .trending-title {
                font-size: 0.8rem;
            }

            .trending-meta {
                font-size: 0.7rem;
            }
        }

        .footer-column {
            display: flex;
            flex-direction: column;
            background: transparent;
        }

        .footer-brand {
            margin-bottom: 2rem;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .footer-logo-img {
            height: 40px;
            width: auto;
        }

        .footer-logo-text {
            display: flex;
            flex-direction: column;
        }

        .footer-brand-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            margin: 0;
        }

        .footer-brand-tagline {
            font-size: 0.875rem;
            color: #cbd5e1;
        }

        .footer-description {
            color: #e2e8f0;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .footer-contact {
            margin-bottom: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }

        .contact-item i {
            color: #3b82f6;
            width: 16px;
        }

        .contact-item a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-item a:hover {
            color: #3b82f6;
        }

        /* Newsletter, Social, Apps in Fourth Column */
        .stats-column .footer-newsletter {
            margin-bottom: 2rem;
        }

        .stats-column .footer-newsletter .newsletter-form {
            margin-bottom: 0;
        }

        .stats-column .footer-stats {
            margin-bottom: 2rem;
        }

        .stats-column .footer-social {
            margin-bottom: 2rem;
        }

        .stats-column .footer-social .social-links {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .stats-column .footer-apps .app-buttons {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .footer-social {
            margin-top: 1rem;
        }

        .social-title {
            font-size: 1rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .social-links {
            display: flex;
            gap: 0.75rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid #374151;
        }

        .social-link:hover {
            color: #ffffff;
            transform: translateY(-2px);
        }

        .social-link.facebook:hover { background: #1877f2; border-color: #1877f2; }
        .social-link.twitter:hover { background: #1da1f2; border-color: #1da1f2; }
        .social-link.youtube:hover { background: #ff0000; border-color: #ff0000; }
        .social-link.instagram:hover { background: #e4405f; border-color: #e4405f; }
        .social-link.linkedin:hover { background: #0077b5; border-color: #0077b5; }

        .footer-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 3rem;
            height: 2px;
            background: #3b82f6;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
            border: none;
        }

        .footer-links a {
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0;
            border: none;
        }

        .footer-links a:hover {
            color: #3b82f6;
            transform: translateX(5px);
        }

        .footer-links i {
            color: #6b7280;
            width: 16px;
        }

        .footer-links a:hover i {
            color: #3b82f6;
        }

        /* Newsletter Styles */
        .newsletter-description {
            color: #d1d5db;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .newsletter-form {
            margin-bottom: 2rem;
        }

        .newsletter-input-group {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .newsletter-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 0.75rem 1rem;
            color: #ffffff;
            outline: none;
        }

        .newsletter-input::placeholder {
            color: #9ca3af;
        }

        .newsletter-btn {
            background: #3b82f6;
            border: none;
            color: white;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .newsletter-btn:hover {
            background: #1e40af;
        }

        /* Footer Statistics */
        .footer-stats {
            margin-bottom: 2rem;
        }

        .stats-title {
            font-size: 1rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #d1d5db;
        }

        /* App Download Buttons */
        .footer-apps {
            margin-top: 1rem;
        }

        .apps-title {
            font-size: 1rem;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .app-buttons {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .app-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .app-btn img {
            width: 24px;
            height: 24px;
        }

        .app-info {
            display: flex;
            flex-direction: column;
        }

        .app-store {
            color: #ffffff;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .app-text {
            color: #9ca3af;
            font-size: 0.75rem;
        }

        .footer-bottom {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            padding: 1.5rem 0;
            border-top: 1px solid #3b82f6;
            width: 100%;
        }

        .footer-bottom-container {
            margin: 0 !important;
            padding: 0 !important;
            width: 100%;
        }

        .footer-bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
            padding: 0 2rem;
        }

        .footer-copyright {
            color: #9ca3af;
        }

        .footer-bottom-links {
            display: flex;
            gap: 2rem;
        }

        .footer-bottom-links a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-bottom-links a:hover {
            color: #3b82f6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-top-content {
                flex-direction: column;
                gap: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .main-nav {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .breaking-content {
                flex-direction: column;
                gap: 0.5rem;
            }

            .footer-bottom-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsMediaOrganization",
        "name": "NitiKotha",
        "url": "<?php echo e(url('/')); ?>",
        "logo": "<?php echo e(asset('frontend/assets/images/logo.png')); ?>",
        "sameAs": [
            "https://facebook.com/nitikotha",
            "https://twitter.com/nitikotha",
            "https://youtube.com/nitikotha"
        ]
    }
    </script>

    <!-- Premium Layout Fixes -->
    <style>
    /* Global Reset for Premium Design */
    html, body {
        margin: 0 !important;
        padding: 0 !important;
        background: #ffffff !important;
        width: 100% !important;
        height: auto !important;
        overflow-x: hidden !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        background: #ffffff !important;
        min-height: calc(100vh - 200px);
    }

    /* Seamless Layout Flow */
    .premium-header + .main-content,
    .modern-header + .main-content {
        margin-top: 0 !important;
        border-top: none !important;
    }

    /* Premium Button Styles */
    .btn, .action-btn {
        border-radius: 0.75rem !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        border: none !important;
        cursor: pointer !important;
    }

    .btn:hover, .action-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    /* Premium Card Styles */
    .card, .news-card, .post-card, .stat-card {
        border-radius: 1rem !important;
        border: none !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.3s ease !important;
    }

    .card:hover, .news-card:hover, .post-card:hover, .stat-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
    }

    /* Premium Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        color: #1f2937 !important;
        line-height: 1.3 !important;
    }

    p {
        color: #6b7280 !important;
        line-height: 1.6 !important;
    }

    /* Premium Links */
    a {
        color: #3b82f6 !important;
        text-decoration: none !important;
        transition: color 0.3s ease !important;
    }

    a:hover {
        color: #1e40af !important;
    }

    /* Premium Form Elements */
    input, textarea, select {
        border-radius: 0.75rem !important;
        border: 1px solid #e5e7eb !important;
        padding: 0.75rem 1rem !important;
        font-family: 'Inter', sans-serif !important;
        transition: all 0.3s ease !important;
    }

    input:focus, textarea:focus, select:focus {
        outline: none !important;
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    /* Premium Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    .slide-in {
        animation: slideInRight 0.6s ease-out;
    }

    /* Premium Scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    ::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }
        z-index: 999 !important;
    }

    /* Fix hero section spacing */
    .hero-section {
        margin-top: 0 !important;
        padding-top: 2rem !important;
    }

    /* Remove any default margins from all sections */
    section, div, header, main, footer {
        margin: 0 !important;
    }

    /* Specific fixes for layout elements */
    .container {
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    /* Fix any potential theme conflicts */
    .home.blog {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Override any WordPress or theme defaults */
    body.home,
    body.blog,
    body.home.blog {
        margin: 0 !important;
        padding: 0 !important;
        background: #ffffff !important;
    }

    /* Fix main website wrapper if it exists */
    .main_website {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }

    /* Force no gaps between major layout elements */
    .modern-header,
    .modern-breaking-news,
    .hero-section,
    .main-content-section {
        display: block !important;
        width: 100% !important;
        margin: 0 !important;
        border: none !important;
    }

    /* Ensure content is flush with header */
    .premium-header + .main-content,
    .modern-header + .main-content {
        margin-top: 0 !important;
        border-top: none !important;
    }
    </style>
</head>
<body>
    <!-- Header -->
    <?php echo $__env->make('frontend.body.header_ultra_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Categories Navigation -->
    <?php echo $__env->make('frontend.body.categories_nav_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Main Content Area -->
    <main class="main-content">
        <?php echo $__env->yieldContent('home'); ?>
    </main>
    
    <!-- Footer -->
    <?php echo $__env->make('frontend.body.footer_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- Search Modal -->
    <div class="search-modal" id="searchModal">
        <div class="search-modal-content">
            <div class="search-modal-header">
                <h3>Search News</h3>
                <button class="search-modal-close" id="searchModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-modal-body">
                <form class="search-form" action="<?php echo e(route('search')); ?>" method="GET">
                    <div class="search-input-group">
                        <input type="text" name="search" class="search-input" placeholder="Search for news..." autocomplete="off">
                        <button type="submit" class="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- CKEditor CDN -->
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

    <!-- JavaScript -->
    <script src="<?php echo e(asset('frontend/assets/js/modern-home.js')); ?>" defer></script>
    
    <!-- Create Post Form JavaScript -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image upload functionality
        const imageInput = document.getElementById('image');
        const uploadArea = document.querySelector('.image-upload-area');
        const uploadPlaceholder = document.querySelector('.upload-placeholder');
        const imagePreview = document.querySelector('.image-preview');
        const previewImg = document.getElementById('preview-img');
        const removeImageBtn = document.querySelector('.remove-image');

        if (imageInput) {
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.src = e.target.result;
                        uploadPlaceholder.style.display = 'none';
                        imagePreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Remove image functionality
            if (removeImageBtn) {
                removeImageBtn.addEventListener('click', function() {
                    imageInput.value = '';
                    uploadPlaceholder.style.display = 'flex';
                    imagePreview.style.display = 'none';
                });
            }

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#3b82f6';
                uploadArea.style.background = '#f8fafc';
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = 'transparent';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = 'transparent';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    imageInput.files = files;
                    imageInput.dispatchEvent(new Event('change'));
                }
            });
        }

        // Category and subcategory functionality
        const categorySelect = document.getElementById('category_id');
        const subcategorySelect = document.getElementById('subcategory_id');

        if (categorySelect && subcategorySelect) {
            categorySelect.addEventListener('change', function() {
                const categoryId = this.value;
                subcategorySelect.innerHTML = '<option value="">Loading subcategories...</option>';

                if (categoryId) {
                    // AJAX call to fetch subcategories
                    fetch(`/get-subcategories/${categoryId}`)
                        .then(response => response.json())
                        .then(data => {
                            subcategorySelect.innerHTML = '<option value="">Select a subcategory</option>';
                            data.forEach(subcategory => {
                                const option = document.createElement('option');
                                option.value = subcategory.id;
                                option.textContent = subcategory.subcategory_name;
                                subcategorySelect.appendChild(option);
                            });
                        })
                        .catch(error => {
                            console.error('Error fetching subcategories:', error);
                            subcategorySelect.innerHTML = '<option value="">Select a subcategory</option>';
                        });
                } else {
                    subcategorySelect.innerHTML = '<option value="">Select a subcategory</option>';
                }
            });
        }

        // Form validation
        const form = document.querySelector('.modern-post-form');
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Prevent default submission first

                const title = document.getElementById('news_title').value.trim();
                const category = document.getElementById('category_id').value;
                const image = document.getElementById('image');

                // Get CKEditor content
                let content = '';
                if (window.editor) {
                    content = window.editor.getData().trim();
                    // Update the hidden textarea with CKEditor content
                    document.getElementById('news_details').value = content;
                } else {
                    content = document.getElementById('news_details').value.trim();
                }

                // Validation
                if (!title) {
                    alert('Please enter a post title.');
                    document.getElementById('news_title').focus();
                    return false;
                }

                if (!category) {
                    alert('Please select a category.');
                    document.getElementById('category_id').focus();
                    return false;
                }

                if (!content || content === '<p>&nbsp;</p>' || content === '<p></p>') {
                    alert('Please enter post content.');
                    if (window.editor) {
                        window.editor.editing.view.focus();
                    } else {
                        document.getElementById('news_details').focus();
                    }
                    return false;
                }

                // Check image requirement (only for create, not edit)
                const isEditForm = form.action.includes('/posts/') && form.querySelector('input[name="_method"]');
                if (!isEditForm && (!image.files || !image.files[0])) {
                    alert('Please upload a featured image.');
                    return false;
                }

                // Show loading state
                const submitBtn = form.querySelector('.btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
                submitBtn.disabled = true;

                // Submit the form
                form.submit();
            });
        }

        // Initialize CKEditor
        const editorElement = document.getElementById('news_details');
        if (editorElement && typeof ClassicEditor !== 'undefined') {
            ClassicEditor
                .create(editorElement, {
                    toolbar: {
                        items: [
                            'heading', '|',
                            'bold', 'italic', 'link', '|',
                            'bulletedList', 'numberedList', '|',
                            'outdent', 'indent', '|',
                            'imageUpload', 'blockQuote', 'insertTable', '|',
                            'undo', 'redo'
                        ]
                    },
                    language: 'en',
                    image: {
                        toolbar: [
                            'imageTextAlternative',
                            'imageStyle:full',
                            'imageStyle:side'
                        ]
                    },
                    table: {
                        contentToolbar: [
                            'tableColumn',
                            'tableRow',
                            'mergeTableCells'
                        ]
                    },
                    simpleUpload: {
                        uploadUrl: '<?php echo e(route("subscriber.upload.image")); ?>',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        }
                    }
                })
                .then(editor => {
                    window.editor = editor;

                    // Style the editor
                    const editorElement = editor.ui.getEditableElement();
                    editorElement.style.minHeight = '300px';
                    editorElement.style.fontSize = '16px';
                    editorElement.style.lineHeight = '1.6';
                    editorElement.style.padding = '20px';

                    // Sync CKEditor content with textarea on change
                    editor.model.document.on('change:data', () => {
                        document.getElementById('news_details').value = editor.getData();
                    });

                    // Remove any validation attributes from the original textarea
                    const textarea = document.getElementById('news_details');
                    textarea.removeAttribute('required');
                    textarea.style.display = 'none'; // Hide the original textarea
                })
                .catch(error => {
                    console.error('CKEditor initialization error:', error);
                });
        }

        // Avatar upload preview
        const avatarInput = document.getElementById('avatar');
        if (avatarInput) {
            avatarInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const avatarImage = document.querySelector('.avatar-image');
                        const defaultAvatar = document.querySelector('.default-avatar');

                        if (avatarImage) {
                            avatarImage.src = e.target.result;
                        } else if (defaultAvatar) {
                            // Replace default avatar with image
                            defaultAvatar.innerHTML = `<img src="${e.target.result}" alt="Avatar Preview" class="avatar-image">`;
                        }
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // Profile form validation
        const profileForm = document.querySelector('.modern-profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', function(e) {
                const password = document.getElementById('password').value;
                const passwordConfirm = document.getElementById('password_confirmation').value;

                if (password && password !== passwordConfirm) {
                    e.preventDefault();
                    alert('Password confirmation does not match.');
                    document.getElementById('password_confirmation').focus();
                    return false;
                }

                if (password && password.length < 8) {
                    e.preventDefault();
                    alert('Password must be at least 8 characters long.');
                    document.getElementById('password').focus();
                    return false;
                }

                // Show loading state
                const submitBtn = profileForm.querySelector('.btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
                submitBtn.disabled = true;
            });
        }
    });
    </script>

    <!-- Performance Monitoring -->
    <script>
        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            if (loadTime > 100 && window.location.hostname === 'localhost') {
                console.log(`⚡ Modern Home Page loaded in ${loadTime.toFixed(2)}ms`);
            }
        });
    </script>

    <!-- Aggressive Smart Homepage JavaScript - Only for public homepage -->
    <?php if(!auth()->check() || !request()->is('subscriber/*')): ?>
    <script src="<?php echo e(asset('frontend/assets/js/aggressive-smart-homepage.js')); ?>?v=<?php echo e(time()); ?>" defer></script>
    <?php endif; ?>
</body>
</html>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/home_dashboard_modern.blade.php ENDPATH**/ ?>