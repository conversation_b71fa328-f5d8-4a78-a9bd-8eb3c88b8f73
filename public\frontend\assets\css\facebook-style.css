/* Facebook Style CSS - NitiKotha */

/* CSS Variables - Facebook Color Scheme */
:root {
    --fb-primary: #1877f2;
    --fb-primary-hover: #166fe5;
    --fb-secondary: #42a5f5;
    --fb-success: #42b883;
    --fb-warning: #ff9800;
    --fb-danger: #f44336;
    --fb-dark: #1c1e21;
    --fb-gray-100: #f0f2f5;
    --fb-gray-200: #e4e6ea;
    --fb-gray-300: #dadde1;
    --fb-gray-400: #bcc0c4;
    --fb-gray-500: #8a8d91;
    --fb-gray-600: #606770;
    --fb-gray-700: #444950;
    --fb-gray-800: #365899;
    --fb-white: #ffffff;
    --fb-black: #050505;
    --fb-text-primary: #1c1e21;
    --fb-text-secondary: #65676b;
    --fb-text-tertiary: #8a8d91;
    --fb-border: #dadde1;
    --fb-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --fb-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --fb-radius: 8px;
    --fb-radius-lg: 12px;
    --fb-transition: all 0.2s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--fb-gray-100);
    color: var(--fb-text-primary);
    line-height: 1.34;
    font-size: 14px;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: inherit;
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
}

img {
    max-width: 100%;
    height: auto;
}

/* Facebook Header */
.fb-header {
    background: var(--fb-white);
    border-bottom: 1px solid var(--fb-border);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 56px;
    box-shadow: var(--fb-shadow);
}

.fb-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header Left */
.fb-header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    max-width: 320px;
}

.fb-logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.fb-logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--fb-primary);
    display: none;
}

.fb-search-container {
    position: relative;
    background: var(--fb-gray-100);
    border-radius: 50px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 240px;
}

.fb-search-icon {
    color: var(--fb-text-secondary);
    font-size: 16px;
}

.fb-search-input {
    border: none;
    background: none;
    outline: none;
    font-size: 15px;
    color: var(--fb-text-primary);
    width: 100%;
}

.fb-search-input::placeholder {
    color: var(--fb-text-secondary);
}

/* Header Center */
.fb-header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 590px;
}

.fb-main-nav {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 112px;
    height: 56px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-secondary);
    font-size: 20px;
    transition: var(--fb-transition);
    position: relative;
}

.fb-nav-link:hover {
    background: var(--fb-gray-100);
    color: var(--fb-primary);
}

.fb-nav-link.active {
    color: var(--fb-primary);
}

.fb-nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--fb-primary);
    border-radius: 2px 2px 0 0;
}

/* Header Right */
.fb-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    max-width: 320px;
}

.fb-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--fb-gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fb-text-primary);
    font-size: 16px;
    transition: var(--fb-transition);
    position: relative;
}

.fb-action-icon:hover {
    background: var(--fb-gray-300);
}

.fb-notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--fb-danger);
    color: var(--fb-white);
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.fb-profile-menu {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-profile-menu:hover {
    background: var(--fb-gray-100);
}

.fb-profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* Auth Buttons */
.fb-auth-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--fb-transition);
    margin-left: 8px;
}

.fb-login-btn {
    background: var(--fb-gray-200);
    color: var(--fb-text-primary);
}

.fb-login-btn:hover {
    background: var(--fb-gray-300);
    color: var(--fb-text-primary);
}

.fb-register-btn {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-register-btn:hover {
    background: var(--fb-primary-hover);
    color: var(--fb-white);
}

/* Main Container */
.fb-main {
    margin-top: 56px;
    min-height: calc(100vh - 56px);
}

.fb-main-container {
    display: grid;
    grid-template-columns: 360px 1fr 320px;
    gap: 0;
    max-width: 1920px;
    margin: 0 auto;
    min-height: calc(100vh - 56px);
}

/* Left Sidebar */
.fb-left-sidebar {
    background: var(--fb-white);
    padding: 20px 16px;
    border-right: 1px solid var(--fb-border);
    height: calc(100vh - 56px);
    overflow-y: auto;
    position: sticky;
    top: 56px;
}

.fb-profile-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: var(--fb-radius);
    margin-bottom: 20px;
    transition: var(--fb-transition);
}

.fb-profile-card:hover {
    background: var(--fb-gray-100);
}

.fb-profile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
}

.fb-profile-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 2px;
}

.fb-profile-tagline {
    font-size: 13px;
    color: var(--fb-text-secondary);
}

/* Navigation Menu */
.fb-nav-menu {
    margin-bottom: 20px;
}

.fb-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-primary);
    font-size: 15px;
    font-weight: 500;
    transition: var(--fb-transition);
    margin-bottom: 4px;
}

.fb-nav-item:hover {
    background: var(--fb-gray-100);
}

.fb-nav-item.active {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-nav-item i {
    width: 20px;
    font-size: 16px;
}

/* Categories Section */
.fb-categories-section {
    margin-bottom: 20px;
}

.fb-section-title {
    font-size: 17px;
    font-weight: 600;
    color: var(--fb-text-secondary);
    margin-bottom: 12px;
    padding: 0 12px;
}

.fb-categories-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.fb-category-group {
    position: relative;
}

.fb-category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-primary);
    transition: var(--fb-transition);
    cursor: pointer;
    position: relative;
}

.fb-category-item:hover {
    background: var(--fb-gray-100);
}

.fb-main-category {
    font-weight: 600;
}

.fb-category-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.fb-category-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-category-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.fb-category-name {
    font-size: 15px;
    font-weight: inherit;
}

.fb-category-count {
    font-size: 12px;
    color: var(--fb-text-secondary);
    background: var(--fb-gray-200);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.fb-category-toggle {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--fb-text-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--fb-transition);
    z-index: 2;
}

.fb-category-toggle:hover {
    background: var(--fb-gray-200);
}

.fb-category-toggle.expanded {
    transform: rotate(180deg);
}

.fb-category-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 40px;
    bottom: 0;
    z-index: 1;
}

/* Subcategories */
.fb-subcategories {
    margin-left: 32px;
    margin-top: 4px;
    border-left: 2px solid var(--fb-gray-200);
    padding-left: 12px;
    display: none;
}

.fb-subcategories.expanded {
    display: block;
}

.fb-subcategory-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-secondary);
    text-decoration: none;
    transition: var(--fb-transition);
    font-size: 14px;
    margin-bottom: 2px;
}

.fb-subcategory-item:hover {
    background: var(--fb-gray-100);
    color: var(--fb-text-primary);
}

.fb-subcategory-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

.fb-subcategory-name {
    flex: 1;
    font-weight: 500;
}

.fb-subcategory-count {
    font-size: 11px;
    color: var(--fb-text-secondary);
    background: var(--fb-gray-200);
    padding: 1px 6px;
    border-radius: 8px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

/* Active States */
.fb-category-item.active {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-category-item.active .fb-category-name,
.fb-category-item.active .fb-category-icon {
    color: var(--fb-white);
}

.fb-category-item.active .fb-category-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--fb-white);
}

.fb-category-item.active .fb-category-toggle {
    color: var(--fb-white);
}

.fb-category-item.active .fb-category-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.fb-subcategory-item.active {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-subcategory-item.active .fb-subcategory-name,
.fb-subcategory-item.active .fb-subcategory-icon {
    color: var(--fb-white);
}

.fb-subcategory-item.active .fb-subcategory-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--fb-white);
}

/* Footer Links */
.fb-footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px;
    border-top: 1px solid var(--fb-border);
    margin-top: 20px;
}

.fb-footer-links a {
    font-size: 13px;
    color: var(--fb-text-secondary);
    transition: var(--fb-transition);
}

.fb-footer-links a:hover {
    color: var(--fb-primary);
}

/* Main Feed */
.fb-main-feed {
    padding: 20px 32px;
    max-width: 680px;
    margin: 0 auto;
}

/* Stories Container */
.fb-stories-container {
    margin-bottom: 20px;
}

.fb-stories-wrapper {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 8px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--fb-gray-400) var(--fb-gray-200);
}

.fb-stories-wrapper::-webkit-scrollbar {
    height: 6px;
}

.fb-stories-wrapper::-webkit-scrollbar-track {
    background: var(--fb-gray-200);
    border-radius: 3px;
}

.fb-stories-wrapper::-webkit-scrollbar-thumb {
    background: var(--fb-gray-400);
    border-radius: 3px;
}

.fb-stories-wrapper::-webkit-scrollbar-thumb:hover {
    background: var(--fb-gray-500);
}

.fb-story-card {
    position: relative;
    width: 112px;
    height: 200px;
    border-radius: var(--fb-radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: var(--fb-transition);
    flex-shrink: 0;
    min-width: 112px;
    background: var(--fb-gray-200);
}

.fb-story-card:hover {
    transform: scale(1.02);
}

.fb-story-image {
    width: 100%;
    height: 100%;
}

.fb-story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-story-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.1) 50%,
        rgba(0, 0, 0, 0.7) 100%
    );
}

.fb-story-content {
    position: absolute;
    bottom: 12px;
    left: 12px;
    right: 12px;
    color: var(--fb-white);
}

.fb-story-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 3px solid var(--fb-white);
    margin-bottom: 8px;
}

.fb-story-title {
    font-size: 13px;
    font-weight: 600;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.fb-story-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

/* Create Story */
.fb-create-story {
    background: var(--fb-white);
    border: 1px solid var(--fb-border);
}

.fb-create-story .fb-story-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--fb-white);
    color: var(--fb-text-primary);
    padding: 12px;
    text-align: center;
}

.fb-story-icon {
    width: 32px;
    height: 32px;
    background: var(--fb-primary);
    color: var(--fb-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-size: 16px;
}

.fb-story-text {
    font-size: 13px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

/* Enhanced Story Meta */
.fb-story-meta {
    margin-top: 4px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.fb-story-category {
    font-size: 10px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 8px;
    align-self: flex-start;
}

.fb-story-time {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Create Post Section */
.fb-create-post {
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
    padding: 16px;
    margin-bottom: 20px;
}

.fb-create-post-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.fb-create-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.fb-create-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-create-input {
    flex: 1;
}

.fb-create-input input {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: var(--fb-gray-100);
    border-radius: 50px;
    font-size: 16px;
    color: var(--fb-text-secondary);
    outline: none;
    cursor: pointer;
}

.fb-create-input input::placeholder {
    color: var(--fb-text-secondary);
}

.fb-create-post-actions {
    display: flex;
    align-items: center;
    justify-content: space-around;
    border-top: 1px solid var(--fb-border);
    padding-top: 12px;
}

.fb-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-secondary);
    font-size: 15px;
    font-weight: 600;
    transition: var(--fb-transition);
    flex: 1;
    justify-content: center;
}

.fb-action-btn:hover {
    background: var(--fb-gray-100);
}

.fb-action-btn i {
    font-size: 20px;
}

/* News Feed Posts */
.fb-news-feed {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.fb-post {
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
    overflow: hidden;
}

/* Post Header */
.fb-post-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 16px 0;
}

.fb-post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.fb-post-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-post-info {
    flex: 1;
}

.fb-post-author {
    font-size: 15px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 2px;
}

.fb-post-meta {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: var(--fb-text-secondary);
}

.fb-post-separator {
    margin: 0 2px;
}

.fb-post-options {
    padding: 8px;
}

.fb-options-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fb-text-secondary);
    transition: var(--fb-transition);
}

.fb-options-btn:hover {
    background: var(--fb-gray-100);
}

/* Post Content */
.fb-post-content {
    padding: 16px;
}

.fb-post-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--fb-text-primary);
    line-height: 1.3;
    margin-bottom: 8px;
}

.fb-post-title a {
    color: inherit;
    transition: var(--fb-transition);
}

.fb-post-title a:hover {
    color: var(--fb-primary);
}

.fb-post-excerpt {
    font-size: 15px;
    color: var(--fb-text-primary);
    line-height: 1.4;
}

/* Post Image */
.fb-post-image {
    position: relative;
    margin: 0 -1px;
    cursor: pointer;
}

.fb-post-image img {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    display: block;
}

.fb-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fb-white);
    font-size: 24px;
    opacity: 0;
    transition: var(--fb-transition);
}

.fb-post-image:hover .fb-image-overlay {
    background: rgba(0, 0, 0, 0.3);
    opacity: 1;
}

/* Post Stats */
.fb-post-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-bottom: 1px solid var(--fb-border);
}

.fb-post-reactions {
    display: flex;
    align-items: center;
    gap: 6px;
}

.fb-reaction-icons {
    display: flex;
    align-items: center;
    gap: -2px;
}

.fb-reaction {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: -2px;
    border: 1px solid var(--fb-white);
}

.fb-reaction-count {
    font-size: 15px;
    color: var(--fb-text-secondary);
    cursor: pointer;
}

.fb-reaction-count:hover {
    text-decoration: underline;
}

.fb-post-engagement {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    color: var(--fb-text-secondary);
}

.fb-comments-count,
.fb-shares-count {
    cursor: pointer;
}

.fb-comments-count:hover,
.fb-shares-count:hover {
    text-decoration: underline;
}

/* Post Actions */
.fb-post-actions {
    display: flex;
    align-items: center;
    padding: 4px 8px;
}

.fb-post-actions .fb-action-btn {
    flex: 1;
    justify-content: center;
    padding: 8px;
    margin: 0 4px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-secondary);
    font-size: 15px;
    font-weight: 600;
    transition: var(--fb-transition);
}

.fb-post-actions .fb-action-btn:hover {
    background: var(--fb-gray-100);
}

.fb-like-btn:hover {
    color: var(--fb-primary);
}

.fb-comment-btn:hover {
    color: var(--fb-success);
}

.fb-share-btn:hover {
    color: var(--fb-warning);
}

/* Right Sidebar */
.fb-right-sidebar {
    background: var(--fb-white);
    padding: 20px 16px;
    border-left: 1px solid var(--fb-border);
    height: calc(100vh - 56px);
    overflow-y: auto;
    position: sticky;
    top: 56px;
}

/* Sponsored Section */
.fb-sponsored-section {
    margin-bottom: 24px;
}

.fb-sponsored-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
    cursor: pointer;
}

.fb-sponsored-item:hover {
    background: var(--fb-gray-100);
}

.fb-sponsored-image {
    width: 48px;
    height: 48px;
    border-radius: var(--fb-radius);
    overflow: hidden;
}

.fb-sponsored-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-sponsored-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 2px;
}

.fb-sponsored-text {
    font-size: 13px;
    color: var(--fb-text-secondary);
    line-height: 1.3;
}

/* Trending Section */
.fb-trending-section {
    margin-bottom: 24px;
}

.fb-trending-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-trending-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
    cursor: pointer;
}

.fb-trending-item:hover {
    background: var(--fb-gray-100);
}

.fb-trending-rank {
    width: 24px;
    height: 24px;
    background: var(--fb-primary);
    color: var(--fb-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
}

.fb-trending-content {
    flex: 1;
}

.fb-trending-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--fb-text-primary);
    line-height: 1.3;
    margin-bottom: 2px;
}

.fb-trending-title a {
    color: inherit;
    transition: var(--fb-transition);
}

.fb-trending-title a:hover {
    color: var(--fb-primary);
}

.fb-trending-meta {
    font-size: 12px;
    color: var(--fb-text-secondary);
}

/* Contacts Section */
.fb-contacts-section {
    margin-bottom: 24px;
}

.fb-contacts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
    cursor: pointer;
}

.fb-contact-item:hover {
    background: var(--fb-gray-100);
}

.fb-contact-avatar {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.fb-contact-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-online-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: var(--fb-success);
    border: 2px solid var(--fb-white);
    border-radius: 50%;
}

.fb-contact-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--fb-text-primary);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .fb-main-container {
        grid-template-columns: 280px 1fr 280px;
    }

    .fb-left-sidebar,
    .fb-right-sidebar {
        padding: 16px 12px;
    }

    .fb-main-feed {
        padding: 16px 24px;
    }
}

@media (max-width: 1024px) {
    .fb-main-container {
        grid-template-columns: 1fr;
    }

    .fb-left-sidebar,
    .fb-right-sidebar {
        display: none;
    }

    .fb-main-feed {
        padding: 16px;
        max-width: 100%;
    }

    .fb-header-left {
        max-width: 200px;
    }

    .fb-search-container {
        min-width: 180px;
    }

    .fb-logo-text {
        display: none;
    }
}

@media (max-width: 768px) {
    .fb-header-container {
        padding: 0 12px;
    }

    .fb-header-center {
        display: none;
    }

    .fb-header-left {
        max-width: none;
        flex: 1;
    }

    .fb-search-container {
        min-width: 120px;
    }

    .fb-auth-btn {
        padding: 6px 12px;
        font-size: 13px;
        margin-left: 4px;
    }

    .fb-auth-btn span {
        display: none;
    }

    .fb-main-feed {
        padding: 12px;
    }

    .fb-stories-wrapper {
        gap: 6px;
    }

    .fb-story-card {
        width: 100px;
        height: 180px;
    }

    .fb-post-content {
        padding: 12px;
    }

    .fb-post-title {
        font-size: 16px;
    }

    .fb-post-excerpt {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .fb-header-container {
        padding: 0 8px;
    }

    .fb-search-container {
        min-width: 100px;
    }

    .fb-search-input {
        font-size: 14px;
    }

    .fb-main-feed {
        padding: 8px;
    }

    .fb-create-post,
    .fb-post {
        border-radius: var(--fb-radius);
    }

    .fb-create-post-actions {
        flex-direction: column;
        gap: 8px;
    }

    .fb-action-btn {
        justify-content: flex-start;
        padding: 12px 16px;
    }

    .fb-post-actions {
        padding: 8px 4px;
    }

    .fb-post-actions .fb-action-btn {
        font-size: 14px;
        padding: 12px 8px;
    }

    .fb-stories-wrapper {
        gap: 4px;
    }

    .fb-story-card {
        width: 90px;
        height: 160px;
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --fb-gray-100: #18191a;
        --fb-gray-200: #242526;
        --fb-gray-300: #3a3b3c;
        --fb-gray-400: #606770;
        --fb-white: #242526;
        --fb-text-primary: #e4e6ea;
        --fb-text-secondary: #b0b3b8;
        --fb-text-tertiary: #8a8d91;
        --fb-border: #3a3b3c;
    }

    body {
        background-color: var(--fb-gray-100);
    }

    .fb-header {
        background: var(--fb-gray-200);
        border-bottom-color: var(--fb-border);
    }

    .fb-search-container {
        background: var(--fb-gray-300);
    }

    .fb-action-icon {
        background: var(--fb-gray-300);
    }

    .fb-action-icon:hover {
        background: var(--fb-gray-400);
    }

    .fb-nav-item:hover {
        background: var(--fb-gray-300);
    }

    .fb-create-post,
    .fb-post,
    .fb-left-sidebar,
    .fb-right-sidebar {
        background: var(--fb-gray-200);
    }

    .fb-create-input input {
        background: var(--fb-gray-300);
        color: var(--fb-text-primary);
    }

    .fb-create-input input::placeholder {
        color: var(--fb-text-secondary);
    }
}

/* Animations and Transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fb-post {
    animation: fadeIn 0.3s ease-out;
}

/* Loading States */
.fb-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--fb-text-secondary);
}

.fb-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--fb-gray-300);
    border-top: 3px solid var(--fb-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.fb-hidden {
    display: none !important;
}

.fb-visible {
    display: block !important;
}

.fb-text-center {
    text-align: center;
}

.fb-text-left {
    text-align: left;
}

.fb-text-right {
    text-align: right;
}

.fb-mb-0 { margin-bottom: 0 !important; }
.fb-mb-1 { margin-bottom: 4px !important; }
.fb-mb-2 { margin-bottom: 8px !important; }
.fb-mb-3 { margin-bottom: 12px !important; }
.fb-mb-4 { margin-bottom: 16px !important; }
.fb-mb-5 { margin-bottom: 20px !important; }

/* Facebook-style Category Page */
.fb-category-header {
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.fb-category-cover {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.fb-category-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.7) 100%
    );
}

.fb-category-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    color: var(--fb-white);
}

.fb-category-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.fb-category-description {
    font-size: 16px;
    margin-bottom: 12px;
    opacity: 0.9;
}

.fb-category-stats {
    display: flex;
    gap: 16px;
    font-size: 14px;
    font-weight: 600;
}

.fb-category-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--fb-border);
}

.fb-follow-btn,
.fb-share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    font-weight: 600;
    transition: var(--fb-transition);
}

.fb-follow-btn {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-follow-btn:hover {
    background: var(--fb-primary-hover);
}

.fb-share-btn {
    background: var(--fb-gray-200);
    color: var(--fb-text-primary);
}

.fb-share-btn:hover {
    background: var(--fb-gray-300);
}

.fb-more-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--fb-gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fb-text-primary);
    transition: var(--fb-transition);
}

.fb-more-btn:hover {
    background: var(--fb-gray-300);
}

/* Filter Tabs */
.fb-filter-tabs {
    display: flex;
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
    padding: 8px;
    margin-bottom: 20px;
    gap: 4px;
}

.fb-filter-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    color: var(--fb-text-secondary);
    font-size: 15px;
    font-weight: 600;
    transition: var(--fb-transition);
    flex: 1;
    justify-content: center;
}

.fb-filter-tab:hover {
    background: var(--fb-gray-100);
    color: var(--fb-text-primary);
}

.fb-filter-tab.active {
    background: var(--fb-primary);
    color: var(--fb-white);
}

/* No Posts State */
.fb-no-posts {
    text-align: center;
    padding: 60px 20px;
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
}

.fb-no-posts-icon {
    font-size: 48px;
    color: var(--fb-gray-400);
    margin-bottom: 16px;
}

.fb-no-posts-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 8px;
}

.fb-no-posts-text {
    font-size: 15px;
    color: var(--fb-text-secondary);
    max-width: 400px;
    margin: 0 auto;
}

/* Pagination */
.fb-pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.fb-pagination .pagination {
    display: flex;
    gap: 8px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.fb-pagination .page-item {
    display: block;
}

.fb-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--fb-radius);
    background: var(--fb-white);
    color: var(--fb-text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--fb-transition);
    box-shadow: var(--fb-shadow);
}

.fb-pagination .page-link:hover {
    background: var(--fb-gray-100);
}

.fb-pagination .page-item.active .page-link {
    background: var(--fb-primary);
    color: var(--fb-white);
}

/* Popular Section */
.fb-popular-section,
.fb-related-categories,
.fb-suggested-section {
    margin-bottom: 24px;
}

.fb-popular-list,
.fb-related-list,
.fb-suggested-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fb-popular-item,
.fb-related-item,
.fb-suggested-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
    cursor: pointer;
}

.fb-popular-item:hover,
.fb-related-item:hover,
.fb-suggested-item:hover {
    background: var(--fb-gray-100);
}

.fb-popular-rank {
    width: 24px;
    height: 24px;
    background: var(--fb-primary);
    color: var(--fb-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
}

.fb-popular-image,
.fb-related-image,
.fb-related-icon,
.fb-suggested-image {
    width: 48px;
    height: 48px;
    border-radius: var(--fb-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-related-icon {
    background: var(--fb-gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.fb-popular-image img,
.fb-related-image img,
.fb-suggested-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-popular-content,
.fb-related-content,
.fb-related-info,
.fb-suggested-content {
    flex: 1;
    min-width: 0;
}

.fb-popular-title,
.fb-related-title,
.fb-related-name,
.fb-suggested-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--fb-text-primary);
    line-height: 1.3;
    margin-bottom: 2px;
}

.fb-popular-title a,
.fb-related-title a,
.fb-suggested-title a {
    color: inherit;
    text-decoration: none;
    transition: var(--fb-transition);
}

.fb-popular-title a:hover,
.fb-related-title a:hover,
.fb-suggested-title a:hover {
    color: var(--fb-primary);
}

.fb-popular-meta,
.fb-related-meta,
.fb-related-count,
.fb-suggested-meta {
    font-size: 12px;
    color: var(--fb-text-secondary);
}

/* Facebook-style News Details Page */
.fb-article-post {
    margin-bottom: 0;
}

.fb-article-title {
    padding: 16px 16px 0;
}

.fb-article-title h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--fb-text-primary);
    line-height: 1.2;
    margin: 0;
}

.fb-article-image {
    margin: 16px 0 0;
}

.fb-article-image img {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    display: block;
}

.fb-article-content {
    padding: 20px 16px;
    font-size: 16px;
    line-height: 1.6;
    color: var(--fb-text-primary);
}

.fb-article-content h2,
.fb-article-content h3,
.fb-article-content h4 {
    color: var(--fb-text-primary);
    margin: 24px 0 12px;
    font-weight: 600;
}

.fb-article-content h2 {
    font-size: 24px;
}

.fb-article-content h3 {
    font-size: 20px;
}

.fb-article-content h4 {
    font-size: 18px;
}

.fb-article-content p {
    margin-bottom: 16px;
}

.fb-article-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--fb-radius);
    margin: 16px 0;
}

.fb-article-content blockquote {
    border-left: 4px solid var(--fb-primary);
    padding-left: 16px;
    margin: 20px 0;
    font-style: italic;
    color: var(--fb-text-secondary);
}

.fb-article-tags {
    padding: 0 16px 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.fb-tag {
    background: var(--fb-gray-100);
    color: var(--fb-primary);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--fb-transition);
}

.fb-tag:hover {
    background: var(--fb-primary);
    color: var(--fb-white);
}

/* Comments Section */
.fb-comments-section {
    border-top: 1px solid var(--fb-border);
    padding: 16px;
}

.fb-write-comment {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.fb-comment-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.fb-comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-comment-input-wrapper {
    flex: 1;
    position: relative;
}

.fb-comment-input {
    width: 100%;
    padding: 8px 16px;
    border: none;
    background: var(--fb-gray-100);
    border-radius: 20px;
    font-size: 15px;
    color: var(--fb-text-primary);
    outline: none;
    transition: var(--fb-transition);
}

.fb-comment-input:focus {
    background: var(--fb-white);
    box-shadow: 0 0 0 2px var(--fb-primary);
}

.fb-comment-input::placeholder {
    color: var(--fb-text-secondary);
}

.fb-comment-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
}

.fb-comment-emoji,
.fb-comment-photo,
.fb-comment-gif {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fb-text-secondary);
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-comment-emoji:hover,
.fb-comment-photo:hover,
.fb-comment-gif:hover {
    background: var(--fb-gray-200);
}

/* Comments List */
.fb-comments-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fb-comment {
    display: flex;
    gap: 8px;
}

.fb-comment-content {
    flex: 1;
}

.fb-comment-bubble {
    background: var(--fb-gray-100);
    padding: 8px 12px;
    border-radius: 16px;
    margin-bottom: 4px;
}

.fb-comment-author {
    font-size: 13px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 2px;
}

.fb-comment-text {
    font-size: 15px;
    color: var(--fb-text-primary);
    line-height: 1.3;
    margin: 0;
}

.fb-comment-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 13px;
    color: var(--fb-text-secondary);
}

.fb-comment-like,
.fb-comment-reply {
    background: none;
    border: none;
    color: var(--fb-text-secondary);
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-comment-like:hover,
.fb-comment-reply:hover {
    color: var(--fb-text-primary);
}

.fb-comment-likes {
    font-size: 12px;
}

/* Load More Comments */
.fb-load-more-comments {
    text-align: center;
    margin-top: 16px;
}

.fb-load-more-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--fb-gray-100);
    color: var(--fb-text-secondary);
    border: none;
    border-radius: var(--fb-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-load-more-btn:hover {
    background: var(--fb-gray-200);
    color: var(--fb-text-primary);
}

/* Share Section */
.fb-share-section {
    margin-bottom: 24px;
}

.fb-share-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-share-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border: none;
    border-radius: var(--fb-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
    text-align: left;
}

.fb-share-button.facebook {
    background: #1877f2;
    color: var(--fb-white);
}

.fb-share-button.twitter {
    background: #1da1f2;
    color: var(--fb-white);
}

.fb-share-button.linkedin {
    background: #0077b5;
    color: var(--fb-white);
}

.fb-share-button.whatsapp {
    background: #25d366;
    color: var(--fb-white);
}

.fb-share-button.copy {
    background: var(--fb-gray-200);
    color: var(--fb-text-primary);
}

.fb-share-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--fb-shadow-lg);
}

/* Author Section */
.fb-author-section {
    margin-bottom: 24px;
}

.fb-author-card {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: var(--fb-gray-100);
    border-radius: var(--fb-radius-lg);
}

.fb-author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.fb-author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-author-info {
    flex: 1;
}

.fb-author-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--fb-text-primary);
    margin-bottom: 2px;
}

.fb-author-role {
    font-size: 13px;
    color: var(--fb-text-secondary);
    margin-bottom: 8px;
}

.fb-author-bio {
    font-size: 14px;
    color: var(--fb-text-primary);
    line-height: 1.4;
    margin-bottom: 12px;
}

.fb-follow-author-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: var(--fb-primary);
    color: var(--fb-white);
    border: none;
    border-radius: var(--fb-radius);
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-follow-author-btn:hover {
    background: var(--fb-primary-hover);
}

/* Breadcrumb */
.fb-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow);
    margin-bottom: 20px;
    font-size: 14px;
}

.fb-breadcrumb a {
    color: var(--fb-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--fb-transition);
}

.fb-breadcrumb a:hover {
    color: var(--fb-primary-hover);
}

.fb-breadcrumb i {
    color: var(--fb-text-secondary);
    font-size: 12px;
}

.fb-breadcrumb span {
    color: var(--fb-text-primary);
    font-weight: 600;
}

/* Active category item */
.fb-category-item.active {
    background: var(--fb-primary);
    color: var(--fb-white);
}

.fb-category-item.active .fb-category-icon,
.fb-category-item.active .fb-category-name,
.fb-category-item.active .fb-category-count {
    color: var(--fb-white);
}

/* Enhanced Button States */
.fb-action-btn.liked {
    color: var(--fb-primary) !important;
    background: rgba(24, 119, 242, 0.1);
}

.fb-action-btn.saved {
    color: var(--fb-primary) !important;
    background: rgba(24, 119, 242, 0.1);
}

.fb-comment-like[style*="color: var(--fb-primary)"] {
    font-weight: 600;
}

/* Share Modal */
.fb-share-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.2s ease;
}

.fb-share-modal-content {
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow-lg);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.fb-share-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--fb-border);
}

.fb-share-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-share-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--fb-gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--fb-text-secondary);
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-share-modal-close:hover {
    background: var(--fb-gray-300);
}

.fb-share-modal-body {
    padding: 20px;
}

.fb-share-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.fb-share-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border: none;
    background: var(--fb-gray-100);
    border-radius: var(--fb-radius);
    cursor: pointer;
    transition: var(--fb-transition);
    text-decoration: none;
    color: var(--fb-text-primary);
}

.fb-share-option:hover {
    background: var(--fb-gray-200);
    transform: translateY(-2px);
}

.fb-share-option i {
    font-size: 24px;
    color: var(--fb-primary);
}

.fb-share-option span {
    font-size: 13px;
    font-weight: 600;
}

/* Comment Replies */
.fb-comment-replies {
    margin-top: 12px;
    margin-left: 40px;
    border-left: 2px solid var(--fb-gray-200);
    padding-left: 12px;
}

.fb-comment-reply {
    margin-bottom: 8px;
}

.fb-comment-reply .fb-comment-avatar {
    width: 28px;
    height: 28px;
}

.fb-comment-reply .fb-comment-bubble {
    font-size: 14px;
}

.fb-comment-reply .fb-comment-meta {
    font-size: 12px;
}

/* No Comments State */
.fb-no-comments {
    text-align: center;
    padding: 40px 20px;
    color: var(--fb-text-secondary);
}

.fb-no-comments p {
    margin: 0;
    font-size: 15px;
}

/* Comment Submit Button */
.fb-comment-submit {
    background: var(--fb-primary);
    color: var(--fb-white);
    border: none;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-comment-submit:hover {
    background: var(--fb-primary-hover);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick Comment Modal */
.fb-comment-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.2s ease;
}

.fb-comment-modal-content {
    background: var(--fb-white);
    border-radius: var(--fb-radius-lg);
    box-shadow: var(--fb-shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.fb-comment-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--fb-border);
}

.fb-comment-modal-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--fb-text-primary);
    line-height: 1.3;
}

.fb-comment-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--fb-gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--fb-text-secondary);
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-comment-modal-close:hover {
    background: var(--fb-gray-300);
}

.fb-comment-modal-body {
    padding: 20px;
}

.fb-comment-input-group {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.fb-comment-input-group .fb-comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.fb-comment-input-group .fb-comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-comment-input-group .fb-comment-input-wrapper {
    flex: 1;
}

.fb-quick-comment-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--fb-border);
    border-radius: var(--fb-radius);
    font-size: 15px;
    color: var(--fb-text-primary);
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    outline: none;
    transition: var(--fb-transition);
}

.fb-quick-comment-input:focus {
    border-color: var(--fb-primary);
    box-shadow: 0 0 0 2px rgba(24, 119, 242, 0.2);
}

.fb-quick-comment-input::placeholder {
    color: var(--fb-text-secondary);
}

.fb-comment-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.fb-btn-secondary {
    padding: 8px 16px;
    border: 1px solid var(--fb-border);
    background: var(--fb-white);
    color: var(--fb-text-primary);
    border-radius: var(--fb-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-btn-secondary:hover {
    background: var(--fb-gray-100);
}

.fb-btn-primary {
    padding: 8px 16px;
    border: none;
    background: var(--fb-primary);
    color: var(--fb-white);
    border-radius: var(--fb-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--fb-transition);
}

.fb-btn-primary:hover {
    background: var(--fb-primary-hover);
}

.fb-btn-primary:disabled {
    background: var(--fb-gray-400);
    cursor: not-allowed;
}

/* Featured Posts Section */
.fb-featured-section {
    background: var(--fb-white);
    border-radius: var(--fb-radius);
    padding: 20px;
    box-shadow: var(--fb-shadow);
    margin-bottom: 20px;
}

.fb-featured-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fb-featured-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
}

.fb-featured-item:hover {
    background-color: var(--fb-gray-100);
}

.fb-featured-image {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: var(--fb-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-featured-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--fb-warning);
    color: var(--fb-white);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    border: 2px solid var(--fb-white);
}

.fb-featured-content {
    flex: 1;
}

.fb-featured-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-featured-title a {
    color: var(--fb-text-primary);
    transition: var(--fb-transition);
}

.fb-featured-title a:hover {
    color: var(--fb-primary);
}

.fb-featured-meta {
    font-size: 12px;
    color: var(--fb-text-secondary);
    margin: 0;
}

/* Pinned Posts Section */
.fb-pinned-section {
    background: var(--fb-white);
    border-radius: var(--fb-radius);
    padding: 20px;
    box-shadow: var(--fb-shadow);
    margin-bottom: 20px;
}

.fb-pinned-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fb-pinned-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 10px;
    border-radius: var(--fb-radius);
    transition: var(--fb-transition);
}

.fb-pinned-item:hover {
    background-color: var(--fb-gray-100);
}

.fb-pinned-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2px;
}

.fb-pinned-content {
    flex: 1;
}

.fb-pinned-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-pinned-title a {
    color: var(--fb-text-primary);
    transition: var(--fb-transition);
}

.fb-pinned-title a:hover {
    color: var(--fb-primary);
}

.fb-pinned-meta {
    font-size: 12px;
    color: var(--fb-text-secondary);
    margin: 0;
}

/* Load More Section */
.fb-load-more-container {
    padding: 30px 20px;
    background: var(--fb-white);
    border-radius: var(--fb-radius);
    box-shadow: var(--fb-shadow);
    margin: 20px 0;
}

.fb-loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.fb-loading-spinner p {
    color: var(--fb-text-secondary);
    margin: 0;
    font-size: 14px;
}

#load-more-btn {
    background: var(--fb-primary);
    border: none;
    color: var(--fb-white);
    padding: 12px 24px;
    border-radius: var(--fb-radius);
    font-weight: 600;
    font-size: 14px;
    transition: var(--fb-transition);
    cursor: pointer;
}

#load-more-btn:hover {
    background: var(--fb-primary-hover);
    transform: translateY(-1px);
}

#load-more-btn:active {
    transform: translateY(0);
}

/* Enhanced Trending Section */
.fb-trending-section .fb-section-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.fb-trending-item:hover {
    background-color: var(--fb-gray-100);
    border-radius: var(--fb-radius);
}

.fb-trending-meta {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Sponsored Ads Styles */
.fb-sponsored-ad {
    background: var(--fb-white);
    border-radius: var(--fb-radius);
    box-shadow: var(--fb-shadow);
    margin-bottom: 20px;
    overflow: hidden;
    transition: var(--fb-transition);
}

.fb-sponsored-ad:hover {
    box-shadow: var(--fb-shadow-lg);
}

.fb-sponsored-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--fb-gray-100);
    border-bottom: 1px solid var(--fb-border);
}

.fb-sponsored-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--fb-text-secondary);
    font-weight: 600;
}

.fb-premium-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--fb-warning);
    color: var(--fb-white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* Native Ad Styles */
.fb-native-ad {
    padding: 16px;
}

.fb-native-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.fb-sponsor-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.fb-sponsor-info h6 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-sponsor-label {
    font-size: 12px;
    color: var(--fb-text-secondary);
}

.fb-native-image {
    margin-bottom: 12px;
    border-radius: var(--fb-radius);
    overflow: hidden;
}

.fb-native-image img {
    width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
}

.fb-native-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--fb-text-primary);
    line-height: 1.3;
}

.fb-native-description {
    font-size: 14px;
    color: var(--fb-text-secondary);
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.fb-native-cta {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: var(--fb-primary);
    color: var(--fb-white);
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: var(--fb-transition);
}

.fb-native-cta:hover {
    background: var(--fb-primary-hover);
    color: var(--fb-white);
    text-decoration: none;
}

/* Display Ad Styles */
.fb-display-ad {
    position: relative;
    overflow: hidden;
}

.fb-display-ad a {
    display: block;
    text-decoration: none;
}

.fb-display-image {
    width: 100%;
    height: auto;
    max-height: 250px;
    object-fit: cover;
    transition: var(--fb-transition);
}

.fb-display-ad:hover .fb-display-image {
    transform: scale(1.02);
}

.fb-display-text {
    padding: 20px;
    text-align: center;
}

.fb-display-text h5 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-display-text p {
    margin: 0;
    color: var(--fb-text-secondary);
    font-size: 14px;
}

/* Video Ad Styles */
.fb-video-ad {
    padding: 16px;
}

.fb-video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.fb-video-header h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-video-container {
    margin-bottom: 12px;
    border-radius: var(--fb-radius);
    overflow: hidden;
}

.fb-video-container video {
    width: 100%;
    height: auto;
    max-height: 200px;
}

.fb-video-placeholder {
    position: relative;
    margin-bottom: 12px;
    border-radius: var(--fb-radius);
    overflow: hidden;
    cursor: pointer;
}

.fb-video-placeholder img {
    width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
}

.fb-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: var(--fb-white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.fb-video-content p {
    margin: 0 0 12px 0;
    color: var(--fb-text-secondary);
    font-size: 14px;
}

.fb-video-cta {
    background: var(--fb-danger);
    color: var(--fb-white);
    padding: 8px 16px;
    border-radius: var(--fb-radius);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: var(--fb-transition);
}

.fb-video-cta:hover {
    background: #d32f2f;
    color: var(--fb-white);
    text-decoration: none;
}

/* Carousel Ad Styles */
.fb-carousel-ad {
    padding: 16px;
}

.fb-carousel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.fb-carousel-header h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-carousel-container {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 8px;
}

.fb-carousel-item {
    flex: 0 0 200px;
    background: var(--fb-gray-100);
    border-radius: var(--fb-radius);
    overflow: hidden;
}

.fb-carousel-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.fb-carousel-content {
    padding: 12px;
}

.fb-carousel-content h6 {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--fb-text-primary);
}

.fb-carousel-content p {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: var(--fb-text-secondary);
    line-height: 1.3;
}

.fb-carousel-content a {
    color: var(--fb-primary);
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
}

.fb-carousel-content a:hover {
    text-decoration: underline;
}

/* Ad Actions */
.fb-ad-actions {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    background: var(--fb-gray-100);
    border-top: 1px solid var(--fb-border);
}

.fb-ad-action {
    background: none;
    border: none;
    color: var(--fb-text-secondary);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--fb-transition);
    display: flex;
    align-items: center;
    gap: 4px;
}

.fb-ad-action:hover {
    background: var(--fb-gray-200);
    color: var(--fb-text-primary);
}

/* Position-specific styles */
.fb-sponsored-ad-top {
    margin: 20px 0;
}

.fb-sponsored-ad-middle {
    margin: 30px 0;
    border: 2px solid var(--fb-primary);
}

.fb-sponsored-ad-bottom {
    margin: 20px 0 0 0;
}

.fb-sponsored-ad-sidebar {
    margin-bottom: 20px;
    max-width: 100%;
}

.fb-sponsored-ad-sidebar .fb-native-image img,
.fb-sponsored-ad-sidebar .fb-display-image {
    max-height: 150px;
}

/* Modern Enhancements */
.fb-post {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.fb-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.fb-action-btn:hover {
    background: linear-gradient(135deg, var(--fb-gray-100), var(--fb-gray-200));
}

.fb-story-card:hover {
    transform: scale(1.05);
}

.fb-category-item:hover {
    background: linear-gradient(135deg, var(--fb-primary), var(--fb-secondary));
    color: var(--fb-white);
}

.fb-trending-item:hover {
    background: linear-gradient(135deg, var(--fb-gray-100), var(--fb-gray-200));
}

/* Smooth Animations */
* {
    scroll-behavior: smooth;
}

.fb-post,
.fb-story-card,
.fb-category-item,
.fb-trending-item,
.fb-featured-item,
.fb-pinned-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading States */
.fb-loading {
    position: relative;
    overflow: hidden;
}

.fb-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .fb-container {
        max-width: 100%;
        padding: 0 15px;
    }

    .fb-main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .fb-sidebar-left,
    .fb-sidebar-right {
        display: none;
    }

    .fb-sponsored-ad-sidebar {
        display: block;
        margin: 20px 0;
    }
}

@media (max-width: 768px) {
    .fb-header-container {
        padding: 0 10px;
    }

    .fb-header-search {
        display: none;
    }

    .fb-nav-item span {
        display: none;
    }

    .fb-post {
        margin: 0 -10px 15px;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .fb-story-carousel {
        padding: 0 10px;
    }

    .fb-story-card {
        min-width: 100px;
        height: 160px;
    }

    .fb-featured-item,
    .fb-pinned-item {
        padding: 8px;
    }

    .fb-featured-image {
        width: 50px;
        height: 50px;
    }

    .fb-sponsored-ad-middle {
        margin: 20px -10px;
        border-radius: 0;
    }

    .fb-carousel-container {
        gap: 8px;
    }

    .fb-carousel-item {
        flex: 0 0 150px;
    }

    .fb-native-ad,
    .fb-video-ad,
    .fb-carousel-ad {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .fb-header-logo span {
        display: none;
    }

    .fb-create-post-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .fb-post-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .fb-action-btn {
        flex: 1;
        min-width: calc(50% - 2.5px);
    }

    .fb-featured-section,
    .fb-pinned-section {
        padding: 15px;
    }

    .fb-load-more-container {
        padding: 20px 15px;
    }

    .fb-story-card {
        min-width: 80px;
        height: 140px;
    }

    .fb-story-title {
        font-size: 11px;
    }

    .fb-post-title {
        font-size: 16px;
    }

    .fb-native-title {
        font-size: 15px;
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --fb-gray-100: #18191a;
        --fb-gray-200: #242526;
        --fb-white: #242526;
        --fb-text-primary: #e4e6ea;
        --fb-text-secondary: #b0b3b8;
        --fb-border: #3a3b3c;
    }

    .fb-post,
    .fb-sponsored-ad {
        background: var(--fb-gray-200);
        border-color: var(--fb-border);
    }
}

/* Print Styles */
@media print {
    .fb-header,
    .fb-sidebar-left,
    .fb-sidebar-right,
    .fb-sponsored-ad,
    .fb-create-post,
    .fb-story-carousel {
        display: none !important;
    }

    .fb-main-content {
        grid-template-columns: 1fr !important;
    }

    .fb-post {
        break-inside: avoid;
        margin-bottom: 20px;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
