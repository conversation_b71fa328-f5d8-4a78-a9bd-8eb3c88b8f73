@extends('admin.admin_management_dashboard')
@section('admin')

<!-- Fix JavaScript Library Conflicts - Same approach as Post Management -->
<script>
// Check if Waves is defined, if not, define a dummy to prevent errors
if (typeof Waves === 'undefined') {
    window.Waves = {
        init: function() {},
        attach: function() {},
        ripple: function() {}
    };
}

// Check if feather is defined, if not, define a dummy to prevent errors
if (typeof feather === 'undefined') {
    window.feather = {
        replace: function() {},
        icons: {}
    };
}

// Ensure jQuery is loaded before initializing subcategory management
function initializeSubcategoryManagement() {
    if (typeof $ === 'undefined') {
        console.log('Waiting for jQuery to load...');
        setTimeout(initializeSubcategoryManagement, 100);
        return;
    }

    $(document).ready(function() {
        console.log('=== Subcategory Management System Starting ===');
        console.log('jQuery loaded:', typeof $ !== 'undefined');
        console.log('jQuery version:', $.fn.jquery);
        console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
        console.log('Toastr available:', typeof toastr !== 'undefined');

        // Initialize subcategory functionality
        initializeSubcategoryActions();
        initializeSubcategoryFilters();
        initializeSubcategoryOrderControls();

        console.log('=== Subcategory Management System Initialized Successfully ===');
    });
}

// Initialize when page loads
initializeSubcategoryManagement();
</script>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="{{ route('add.subcategory') }}" class="btn btn-primary waves-effect waves-light">
                                <i class="mdi mdi-plus"></i> Add SubCategory
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">SubCategory Management</h4>
                    <p class="page-title-desc">Manage subcategories with drag-and-drop ordering and status controls</p>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-primary">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-format-list-bulleted font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Total SubCategories</h6>
                                <b>{{ $subcategories->count() }}</b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-success">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-check-circle font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Active SubCategories</h6>
                                <b>{{ $subcategories->count() }}</b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-warning">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-star font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Featured SubCategories</h6>
                                <b>0</b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-info">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-folder-multiple font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Parent Categories</h6>
                                <b>{{ $subcategories->pluck('category_id')->unique()->count() }}</b>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage SubCategories Display Order & Status</h4>
                        <p class="card-title-desc">Drag and drop to reorder subcategories, or use the action buttons to manage status.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="search-box">
                                    <div class="position-relative">
                                        <input type="text" class="form-control" id="searchSubcategories" placeholder="Search subcategories...">
                                        <i class="mdi mdi-magnify search-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                    <option value="featured">Featured Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterCategory">
                                    <option value="">All Categories</option>
                                    @foreach($subcategories->pluck('category')->unique('id') as $category)
                                        <option value="{{ $category->id }}">{{ $category->category_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover sortable-table" id="subcategoriesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="80">Order</th>
                                        <th>SubCategory</th>
                                        <th>Parent Category</th>
                                        <th>Status</th>
                                        <th width="200">Order Controls</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-subcategories">
                                    @foreach($subcategories as $key => $subcategory)
                                    <tr data-subcategory-id="{{ $subcategory->id }}" data-order="{{ $key }}"
                                        data-category-id="{{ $subcategory->category_id }}"
                                        class="subcategory-row">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2 order-display">{{ $subcategory->display_order ?? $key }}</span>
                                                <input type="number" class="form-control form-control-sm order-input"
                                                       value="{{ $subcategory->display_order ?? $key }}"
                                                       min="0"
                                                       max="999"
                                                       style="width: 60px; display: none;">
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <h6 class="mb-1">{{ $subcategory->subcategory_name }}</h6>
                                                <small class="text-muted">{{ $subcategory->subcategory_slug }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $subcategory->category->category_name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">Active</span>
                                        </td>

                                        <!-- Order Controls -->
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary move-up"
                                                        data-subcategory-id="{{ $subcategory->id }}"
                                                        title="Move Up">
                                                    <i class="mdi mdi-arrow-up"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary move-down"
                                                        data-subcategory-id="{{ $subcategory->id }}"
                                                        title="Move Down">
                                                    <i class="mdi mdi-arrow-down"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary edit-order"
                                                        data-subcategory-id="{{ $subcategory->id }}"
                                                        title="Edit Order">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-success save-order"
                                                        data-subcategory-id="{{ $subcategory->id }}"
                                                        title="Save Order"
                                                        style="display: none;">
                                                    <i class="mdi mdi-check"></i>
                                                </button>
                                            </div>
                                        </td>

                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- Edit Button -->
                                                <a href="{{ route('edit.subcategory', $subcategory->id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>

                                                <!-- Delete Button -->
                                                <a href="{{ route('delete.subcategory', $subcategory->id) }}"
                                                   class="btn btn-sm btn-outline-danger" title="Delete" id="delete">
                                                    <i class="mdi mdi-delete"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        
    </div> <!-- container -->
</div> <!-- content -->

<script>
$(document).ready(function() {
    console.log('Subcategory Management Ready');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');
    
    // Initialize subcategory functionality
    initializeSubcategorySortable();
    initializeSubcategoryActions();
    initializeSubcategoryFilters();
    
    function initializeSubcategorySortable() {
        console.log('Initializing subcategory sortable...');
        
        var $table = $("#sortable-subcategories");
        console.log('Subcategory table found:', $table.length);
        
        if ($table.length === 0) {
            console.error('Subcategory sortable table not found');
            return;
        }
        
        try {
            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr",
                axis: "y",
                cursor: "move",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Subcategory drag started');
                    ui.placeholder.height(ui.item.height());
                },
                update: function(event, ui) {
                    console.log('Subcategory order updated');
                    updateSubcategoryOrder();
                },
                stop: function(event, ui) {
                    console.log('Subcategory drag stopped');
                }
            });
            
            console.log('Subcategory sortable initialized successfully');
            
        } catch (error) {
            console.error('Subcategory sortable initialization error:', error);
        }
    }

    // Update subcategory order after drag and drop
    function updateSubcategoryOrder() {
        var subcategories = [];
        $("#sortable-subcategories tr").each(function(index) {
            var subcategoryId = $(this).data('subcategory-id');
            if (subcategoryId) {
                subcategories.push({
                    id: subcategoryId,
                    order: index
                });
                // Update the order badge
                $(this).find('.order-badge').text(index);
            }
        });

        console.log('Updating order for subcategories:', subcategories);

        // For now, just show success message without backend update
        toastr.success('Subcategory order updated (demo mode)');
    }

    function initializeSubcategoryActions() {
        console.log('Subcategory actions initialized (basic mode)');
    }

    function initializeSubcategoryFilters() {
        console.log('Initializing subcategory filters...');

        // Search functionality
        $('#searchSubcategories').off('input').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();

            $('#subcategoriesTable tbody tr').each(function() {
                var subcategoryName = $(this).find('h6').text().toLowerCase();
                var categoryName = $(this).find('.badge.bg-primary').text().toLowerCase();

                if (searchTerm === '' || subcategoryName.includes(searchTerm) ||
                    categoryName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Category filter functionality
        $('#filterCategory').off('change').on('change', function() {
            var categoryFilter = $(this).val();

            $('#subcategoriesTable tbody tr').each(function() {
                var $row = $(this);
                var show = true;

                // Category filter
                if (categoryFilter) {
                    var subcategoryCategoryId = $row.data('category-id');
                    show = show && (subcategoryCategoryId == categoryFilter);
                }

                if (show) {
                    $row.show();
                } else {
                    $row.hide();
                }
            });
        });

        console.log('Subcategory filters initialized');
    }

    // Initialize subcategory order controls
    function initializeSubcategoryOrderControls() {
        console.log('Initializing subcategory order controls...');

        // ===== SUBCATEGORY ORDER CONTROLS =====

        // Move Up
        $(document).on('click', '.move-up', function(e) {
            e.preventDefault();
            console.log('Move up clicked for subcategory:', $(this).data('subcategory-id'));
            const subcategoryId = $(this).data('subcategory-id');
            const $row = $(this).closest('tr');
            const currentOrder = parseInt($row.find('.order-display').text());
            const newOrder = Math.max(0, currentOrder - 1);

            updateSubcategoryOrder(subcategoryId, newOrder, $row);
        });

        // Move Down
        $(document).on('click', '.move-down', function(e) {
            e.preventDefault();
            console.log('Move down clicked for subcategory:', $(this).data('subcategory-id'));
            const subcategoryId = $(this).data('subcategory-id');
            const $row = $(this).closest('tr');
            const currentOrder = parseInt($row.find('.order-display').text());
            const newOrder = currentOrder + 1;

            updateSubcategoryOrder(subcategoryId, newOrder, $row);
        });

    // Edit Order
    $(document).on('click', '.edit-order', function(e) {
        e.preventDefault();
        const $row = $(this).closest('tr');
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $(this);
        const $saveBtn = $row.find('.save-order');

        $orderDisplay.hide();
        $orderInput.show().focus();
        $editBtn.hide();
        $saveBtn.show();
    });

    // Save Order
    $(document).on('click', '.save-order', function(e) {
        e.preventDefault();
        const subcategoryId = $(this).data('subcategory-id');
        const $row = $(this).closest('tr');
        const newOrder = parseInt($row.find('.order-input').val());

        updateSubcategoryOrder(subcategoryId, newOrder, $row);
    });

    // Enter key to save
    $(document).on('keypress', '.order-input', function(e) {
        if (e.which === 13) {
            $(this).closest('tr').find('.save-order').click();
        }
    });

    function updateSubcategoryOrder(subcategoryId, newOrder, $row) {
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        // Show loading
        $orderDisplay.text('...').removeClass('bg-secondary').addClass('bg-warning');

        $.ajax({
            url: '{{ route("subcategories.update-order") }}',
            method: 'POST',
            data: {
                subcategory_id: subcategoryId,
                order: newOrder,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $orderDisplay.text(newOrder).removeClass('bg-warning').addClass('bg-secondary');
                    $row.data('order', newOrder);

                    // Reset edit mode
                    $orderDisplay.show();
                    $orderInput.hide();
                    $editBtn.show();
                    $saveBtn.hide();

                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message || 'Subcategory order updated successfully!');
                    }
                } else {
                    resetSubcategoryOrderEdit($row);
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message || 'Failed to update subcategory order');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Subcategory order update error:', xhr.responseText);
                resetSubcategoryOrderEdit($row);

                let errorMessage = 'Failed to update subcategory order';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                }
            }
        });
    }

    function resetSubcategoryOrderEdit($row) {
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        $orderDisplay.removeClass('bg-warning').addClass('bg-secondary').show();
        $orderInput.hide();
        $editBtn.show();
        $saveBtn.hide();
    }

        console.log('Subcategory order management initialized');
    }

});
</script>

@endsection
