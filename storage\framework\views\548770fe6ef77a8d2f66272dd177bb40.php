
<?php $__env->startSection('admin'); ?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<style type="text/css">
    .form-check-label {
        text-transform: capitalize;
    }

    .permission-group-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .permission-group-card:hover {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-color: #727cf5;
    }

    .permission-group-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.35rem 0.35rem 0 0;
        position: relative;
    }

    .permission-group-body {
        padding: 1rem;
        background: #f8f9fa;
    }

    .permission-item {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .permission-item:hover {
        border-color: #727cf5;
        background: #f1f3ff;
    }

    .permission-item.selected {
        background: #e7f3ff;
        border-color: #727cf5;
    }

    .permission-search {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        padding: 1rem;
        border-bottom: 1px solid #e3e6f0;
        margin-bottom: 1rem;
    }

    .group-toggle {
        position: absolute;
        top: 50%;
        right: 1rem;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .group-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .permission-stats {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .stats-item {
        text-align: center;
        padding: 0.5rem;
    }

    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #727cf5;
    }

    .stats-label {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .form-check-input:checked {
        background-color: #727cf5;
        border-color: #727cf5;
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .role-selector {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .permission-badge {
        margin-left: auto;
    }

    .permission-badge .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .form-check-input:indeterminate {
        background-color: #ffc107;
        border-color: #ffc107;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
    }

    .permission-group-card.collapsed .permission-group-body {
        display: none;
    }

    @media (max-width: 768px) {
        .permission-item {
            margin-bottom: 0.75rem;
        }

        .permission-group-header h5 {
            font-size: 1rem;
        }

        .stats-item {
            margin-bottom: 1rem;
        }

        .btn-group.w-100 .btn {
            font-size: 0.875rem;
        }
    }
</style>

<div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">
                        
                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                                            <li class="breadcrumb-item"><a href="<?php echo e(route('all.roles.permission')); ?>">Roles & Permissions</a></li>
                                            <li class="breadcrumb-item active">Assign Role Permissions</li>
                                        </ol>
                                    </div>
                                     <h4 class="page-title">
                                        <i class="mdi mdi-shield-link-variant me-2"></i>
                                        Assign Role Permissions
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- end page title -->

                        <!-- Role Selector -->
                        <div class="row">
                            <div class="col-12">
                                <div class="role-selector">
                                    <form id="myForm" method="post" action="<?php echo e(route('role.permission.store')); ?>">
                                        <?php echo csrf_field(); ?>

                                        <div class="row align-items-end">
                                            <div class="col-md-6">
                                                <label for="role-select" class="form-label">
                                                    <i class="mdi mdi-account-group me-1"></i>
                                                    Select Role
                                                </label>
                                                <select name="role_id" class="form-select" id="role-select" required>
                                                    <option value="">Choose a role to assign permissions...</option>
                                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($role->id); ?>"><?php echo e($role->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <button type="button" class="btn btn-outline-primary" id="load-permissions">
                                                    <i class="mdi mdi-reload me-1"></i>
                                                    Load Current Permissions
                                                </button>
                                            </div>
                                        </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Search -->
                        <div class="permission-search">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="mdi mdi-magnify"></i>
                                        </span>
                                        <input type="text" class="form-control" id="permission-search" placeholder="Search permissions...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group w-100" role="group">
                                        <button type="button" class="btn btn-outline-success" id="select-all">
                                            <i class="mdi mdi-check-all me-1"></i>Select All
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" id="deselect-all">
                                            <i class="mdi mdi-close-box-multiple me-1"></i>Deselect All
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Statistics -->
                        <div class="permission-stats">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stats-item">
                                        <div class="stats-number" id="total-permissions"><?php echo e($permissions->count()); ?></div>
                                        <div class="stats-label">Total Permissions</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-item">
                                        <div class="stats-number" id="selected-permissions">0</div>
                                        <div class="stats-label">Selected</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-item">
                                        <div class="stats-number" id="total-groups"><?php echo e($permission_groups->count()); ?></div>
                                        <div class="stats-label">Permission Groups</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-item">
                                        <div class="stats-number" id="active-groups">0</div>
                                        <div class="stats-label">Active Groups</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permission Groups -->
                        <div class="row">
                            <div class="col-12">
                                <?php $__currentLoopData = $permission_groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $permissions = App\Models\User::getpermissionByGroupName($group->group_name);
                                        // Sanitize group ID for CSS selectors - remove special characters
                                        $groupId = preg_replace('/[^a-zA-Z0-9_-]/', '_', strtolower($group->group_name));
                                        $groupId = preg_replace('/_+/', '_', $groupId); // Replace multiple underscores with single
                                        $groupId = trim($groupId, '_'); // Remove leading/trailing underscores
                                    ?>

                                    <div class="permission-group-card" data-group="<?php echo e($groupId); ?>">
                                        <div class="permission-group-header">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div>
                                                    <h5 class="mb-0">
                                                        <i class="mdi mdi-folder-key me-2"></i>
                                                        <?php echo e($group->group_name); ?>

                                                    </h5>
                                                    <small class="opacity-75"><?php echo e($permissions->count()); ?> permissions</small>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="form-check form-check-primary me-3">
                                                        <input class="form-check-input group-checkbox" type="checkbox"
                                                               id="group_<?php echo e($groupId); ?>" data-group="<?php echo e($groupId); ?>">
                                                        <label class="form-check-label text-white" for="group_<?php echo e($groupId); ?>">
                                                            Select All
                                                        </label>
                                                    </div>
                                                    <button type="button" class="group-toggle" data-bs-toggle="collapse"
                                                            data-bs-target="#collapse_<?php echo e($groupId); ?>"
                                                            aria-expanded="true"
                                                            aria-controls="collapse_<?php echo e($groupId); ?>">
                                                        <i class="mdi mdi-chevron-down"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="collapse show" id="collapse_<?php echo e($groupId); ?>">
                                            <div class="permission-group-body">
                                                <div class="row">
                                                    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="col-md-6 col-lg-4 mb-2">
                                                            <div class="permission-item">
                                                                <div class="d-flex align-items-center">
                                                                    <div class="form-check form-check-primary mb-0">
                                                                        <input class="form-check-input permission-checkbox"
                                                                               name="permission[]"
                                                                               type="checkbox"
                                                                               value="<?php echo e($permission->id); ?>"
                                                                               id="permission_<?php echo e($permission->id); ?>"
                                                                               data-group="<?php echo e($groupId); ?>"
                                                                               data-permission="<?php echo e($permission->name); ?>">
                                                                        <label class="form-check-label" for="permission_<?php echo e($permission->id); ?>">
                                                                            <?php echo e(ucwords(str_replace('.', ' ', $permission->name))); ?>

                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="permission-badge">
                                                                    <?php if(str_contains($permission->name, 'create') || str_contains($permission->name, 'add')): ?>
                                                                        <span class="badge bg-success">Create</span>
                                                                    <?php elseif(str_contains($permission->name, 'edit') || str_contains($permission->name, 'update')): ?>
                                                                        <span class="badge bg-warning">Edit</span>
                                                                    <?php elseif(str_contains($permission->name, 'delete')): ?>
                                                                        <span class="badge bg-danger">Delete</span>
                                                                    <?php elseif(str_contains($permission->name, 'view') || str_contains($permission->name, 'menu')): ?>
                                                                        <span class="badge bg-info">View</span>
                                                                    <?php else: ?>
                                                                        <span class="badge bg-secondary">Action</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- Submit Section -->
                        <div class="row">
                            <div class="col-12">
                                <div class="text-center py-4">
                                    <button type="submit" class="btn btn-modern btn-lg" id="submit-permissions">
                                        <i class="mdi mdi-content-save me-2"></i>
                                        Assign Permissions to Role
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-3" onclick="window.history.back()">
                                        <i class="mdi mdi-arrow-left me-2"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>

                                        </form>

                    </div> <!-- container -->

                </div> <!-- content -->

<script type="text/javascript">
$(document).ready(function() {
    // Permission search functionality
    $('#permission-search').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.permission-item').each(function() {
            const permissionName = $(this).find('label').text().toLowerCase();
            if (permissionName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
        updateStats();
    });

    // Select all permissions
    $('#select-all').click(function() {
        $('.permission-checkbox:visible').prop('checked', true);
        $('.group-checkbox').prop('checked', true);
        updateStats();
        updateGroupStates();
    });

    // Deselect all permissions
    $('#deselect-all').click(function() {
        $('.permission-checkbox').prop('checked', false);
        $('.group-checkbox').prop('checked', false);
        updateStats();
        updateGroupStates();
    });

    // Group checkbox functionality
    $('.group-checkbox').change(function() {
        try {
            const groupId = $(this).data('group');
            const sanitizedGroupId = groupId.replace(/[^a-zA-Z0-9_-]/g, '_');
            const isChecked = $(this).is(':checked');
            $(`.permission-checkbox[data-group="${sanitizedGroupId}"]`).prop('checked', isChecked);
            updateStats();
        } catch (error) {
            console.warn('Error in group checkbox change:', error);
        }
    });

    // Individual permission checkbox functionality
    $('.permission-checkbox').change(function() {
        const groupId = $(this).data('group');
        updateGroupCheckbox(groupId);
        updateStats();

        // Add visual feedback
        if ($(this).is(':checked')) {
            $(this).closest('.permission-item').addClass('selected');
        } else {
            $(this).closest('.permission-item').removeClass('selected');
        }
    });

    // Update group checkbox based on individual permissions
    function updateGroupCheckbox(groupId) {
        try {
            // Sanitize groupId to ensure it's a valid CSS selector
            const sanitizedGroupId = groupId.replace(/[^a-zA-Z0-9_-]/g, '_');

            const totalInGroup = $(`.permission-checkbox[data-group="${sanitizedGroupId}"]`).length;
            const checkedInGroup = $(`.permission-checkbox[data-group="${sanitizedGroupId}"]:checked`).length;

            const groupCheckbox = $(`#group_${sanitizedGroupId}`);
            if (groupCheckbox.length > 0) {
                if (checkedInGroup === 0) {
                    groupCheckbox.prop('checked', false).prop('indeterminate', false);
                } else if (checkedInGroup === totalInGroup) {
                    groupCheckbox.prop('checked', true).prop('indeterminate', false);
                } else {
                    groupCheckbox.prop('checked', false).prop('indeterminate', true);
                }
            }
        } catch (error) {
            console.warn('Error updating group checkbox for groupId:', groupId, error);
        }
    }

    // Update all group checkboxes
    function updateGroupStates() {
        $('.group-checkbox').each(function() {
            const groupId = $(this).data('group');
            updateGroupCheckbox(groupId);
        });
    }

    // Update statistics
    function updateStats() {
        const totalPermissions = $('.permission-checkbox').length;
        const selectedPermissions = $('.permission-checkbox:checked').length;
        const totalGroups = $('.permission-group-card').length;
        const activeGroups = $('.group-checkbox:checked').length;

        $('#total-permissions').text(totalPermissions);
        $('#selected-permissions').text(selectedPermissions);
        $('#total-groups').text(totalGroups);
        $('#active-groups').text(activeGroups);
    }

    // Load current permissions for selected role
    $('#load-permissions').click(function() {
        const roleId = $('#role-select').val();
        if (!roleId) {
            alert('Please select a role first');
            return;
        }

        const button = $(this);
        const originalText = button.html();
        button.html('<i class="mdi mdi-loading mdi-spin me-1"></i>Loading...').prop('disabled', true);

        $.ajax({
            url: `/role/${roleId}/permissions`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    // Clear all checkboxes first
                    $('.permission-checkbox').prop('checked', false);
                    $('.group-checkbox').prop('checked', false);

                    // Check the permissions for this role
                    response.permissions.forEach(function(permissionId) {
                        $(`#permission_${permissionId}`).prop('checked', true);
                    });

                    // Update group checkboxes
                    updateGroupStates();
                    updateStats();

                    alert(`Loaded ${response.permissions.length} permissions for role: ${response.role_name}`);
                } else {
                    alert('Error loading permissions: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Error loading permissions. Please try again.');
                console.error('Error:', xhr);
            },
            complete: function() {
                button.html(originalText).prop('disabled', false);
            }
        });
    });

    // Form validation
    $('#myForm').submit(function(e) {
        const roleId = $('#role-select').val();
        const selectedPermissions = $('.permission-checkbox:checked').length;

        if (!roleId) {
            e.preventDefault();
            alert('Please select a role');
            $('#role-select').focus();
            return false;
        }

        if (selectedPermissions === 0) {
            e.preventDefault();
            if (confirm('No permissions selected. Do you want to remove all permissions from this role?')) {
                return true;
            }
            return false;
        }

        return true;
    });

    // Initialize stats
    updateStats();
    updateGroupStates();

    // Handle Bootstrap collapse events for icon rotation
    $('.collapse').on('show.bs.collapse', function() {
        const collapseId = $(this).attr('id');
        const toggle = $(`[data-bs-target="#${collapseId}"]`);
        const icon = toggle.find('i');
        icon.removeClass('mdi-chevron-down').addClass('mdi-chevron-up');
        toggle.attr('aria-expanded', 'true');
    });

    $('.collapse').on('hide.bs.collapse', function() {
        const collapseId = $(this).attr('id');
        const toggle = $(`[data-bs-target="#${collapseId}"]`);
        const icon = toggle.find('i');
        icon.removeClass('mdi-chevron-up').addClass('mdi-chevron-down');
        toggle.attr('aria-expanded', 'false');
    });
});
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/pages/roles/add_roles_permission.blade.php ENDPATH**/ ?>