 
@php
    $id = Auth::user()->id;
    $userid = App\Models\User::find($id);
    $status = $userid->status;
    $isAdmin = Auth::user()->hasRole('admin') || Auth::user()->hasRole('super-admin') || Auth::user()->email === '<EMAIL>';
@endphp

 <div class="left-side-menu">

                <div class="h-100" data-simplebar>

                    <!-- User box -->
                   

                    <!--- Sidemenu -->
                    <div id="sidebar-menu">

                        <ul id="side-menu" class="sidebar-menu">

                            <!-- <li class="menu-title">Navigation</li> -->
                 

     <li>
        <a href="{{ route('admin.dashboard') }}">
             <i class="mdi mdi-view-dashboard text-primary"></i>
            <span> Dashboard </span>
        </a>
    </li>


      @if($status == 'active') 


                            <li class="menu-title mt-2">Menu</li>

          
          @if(Auth::user()->can('category.menu'))                   
        <li>
            <a href="#sidebarEcommerce" data-bs-toggle="collapse">
                <i class="mdi mdi-folder-multiple text-success"></i>
                <span> Category </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarEcommerce">
                <ul class="nav-second-level">
                     @if(Auth::user()->can('category.list'))   
                    <li>
                        <a href="{{ route('all.category') }}">All Category</a>
                    </li>
                     @endif
                     @if(Auth::user()->can('category.add'))   
                    <li>
                        <a href="{{ route('add.category') }}">Add Category</a>
                    </li>
                     @endif
                    
                </ul>
            </div>
        </li>
            @endif


 @if(Auth::user()->can('subcategory.menu'))
         <li>
            <a href="#sidebarEcommerce1" data-bs-toggle="collapse">
                <i class="mdi mdi-folder-outline text-info"></i>
                <span> SubCategory </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarEcommerce1">
                <ul class="nav-second-level">
                     @if(Auth::user()->can('subcategory.list'))
                    <li>
                  <a href="{{ route('all.subcategory') }}">All SubCategory</a>
                    </li>
                     @endif
                     @if(Auth::user()->can('subcategory.add'))
                    <li>
                 <a href="{{ route('add.subcategory') }}">Add SubCategory</a>
                    </li>
                     @endif
                    
                </ul>
            </div>
        </li>
 @endif

 @if(Auth::user()->can('news.menu'))
         <li>
            <a href="#newspost" data-bs-toggle="collapse">
                <i class="mdi mdi-newspaper text-primary"></i>
                <span> News Post Setting </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="newspost">
                <ul class="nav-second-level">
                    @if(Auth::user()->can('news.list'))
                    <li>
                  <a href="{{ route('all.news.post') }}">All News Post</a>
                    </li>
                    @endif
                     @if(Auth::user()->can('news.add'))
                    <li>
                 <a href="{{ route('add.news.post') }}">Add News Post</a>
                    </li>
                    @endif
                     @if(Auth::user()->can('news.list'))
                    <li>
                 <a href="{{ route('admin.posts.manage') }}">Manage Posts</a>
                    </li>
                    @endif
                </ul>
            </div>
        </li>
@endif

 @if(Auth::user()->can('post.approval.view'))
         <li>
            <a href="#postapproval" data-bs-toggle="collapse">
                <i class="mdi mdi-check-circle text-success"></i>
                <span> Post Approval </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="postapproval">
                <ul class="nav-second-level">
                    <li>
                  <a href="{{ route('admin.posts.pending') }}">Pending Posts</a>
                    </li>
                    <li>
                 <a href="{{ route('admin.posts.approved') }}">Approved Posts</a>
                    </li>
                    <li>
                 <a href="{{ route('admin.posts.rejected') }}">Rejected Posts</a>
                    </li>
                </ul>
            </div>
        </li>
 @endif


 @if(Auth::user()->can('banner.menu'))
            <li>
            <a href="#banner" data-bs-toggle="collapse">
                <i class="mdi mdi-image-multiple text-warning"></i>
                <span> Banner Setting </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="banner">
                <ul class="nav-second-level">

                    <li>
                  <a href="{{ route('all.banners') }}">All Banner</a>
                    </li>
                  
                    
                </ul>
            </div>
        </li>
@endif



 @if(Auth::user()->can('photo.menu'))
            <li>
                <a href="#sidebarEmail" data-bs-toggle="collapse">
                    <i class="mdi mdi-camera text-info"></i>
                    <span> Photo Setting </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="sidebarEmail">
                    <ul class="nav-second-level">
                        <li>
                            <a href="{{ route('all.photo.gallery') }}">Photo Gallery</a>
                        </li>
                        
                    </ul>
                </div>
            </li>
@endif


 @if(Auth::user()->can('video.menu'))
             <li>
                <a href="#video" data-bs-toggle="collapse">
                    <i class="mdi mdi-video text-danger"></i>
                    <span> Video Setting </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="video">
                    <ul class="nav-second-level">
                        <li>
                            <a href="{{ route('all.video.gallery') }}">Video Gallery</a>
                        </li>
                        
                    </ul>
                </div>
            </li>
@endif


 @if(Auth::user()->can('live.menu'))
             <li>
                <a href="#live" data-bs-toggle="collapse">
                    <i class="mdi mdi-television text-primary"></i>
                    <span> Live Tv Setting </span>
                    <span class="menu-arrow"></span>
                </a>
                <div class="collapse" id="live">
                    <ul class="nav-second-level">
                        <li>
                            <a href="{{ route('update.live.tv') }}">Update Live TV</a>
                        </li>
                        
                    </ul>
                </div>
            </li>
@endif


 @if(Auth::user()->can('review.menu'))
     <li>
        <a href="#review" data-bs-toggle="collapse">
            <i class="mdi mdi-star-circle text-warning"></i>
            <span> Review Setting </span>
            <span class="menu-arrow"></span>
        </a>
        <div class="collapse" id="review">
            <ul class="nav-second-level">
                <li>
                    <a href="{{ route('pending.review') }}">Pending Review</a>
                </li>

                <li>
                    <a href="{{ route('approve.review') }}">Approve Review</a>
                </li>
                
            </ul>
        </div>
    </li>
@endif


@if(Auth::user()->can('seo.menu'))
      <li>
        <a href="#seo" data-bs-toggle="collapse">
            <i class="mdi mdi-search-web text-success"></i>
            <span> Seo Setting </span>
            <span class="menu-arrow"></span>
        </a>
        <div class="collapse" id="seo">
            <ul class="nav-second-level">
                <li>
                    <a href="{{ route('seo.setting') }}">Update Seo </a>
                </li>
            </ul>
        </div>
    </li>
        @endif
                            
 
 

                            <li class="menu-title mt-2">Setting</li>


@if(true)

        <li>
            <a href="#sidebarAuth" data-bs-toggle="collapse">
                <i class="mdi mdi-account-cog text-primary"></i>
                <span> Setting Admin User  </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarAuth">
                <ul class="nav-second-level">
                    <li>
                        <a href="{{ route('all.admin') }}">All Admin</a>
                    </li>

                     <li>
                        <a href="{{ route('add.admin') }}">Add Admin </a>
                    </li>

                    <li>
                        <a href="{{ route('admin.site-settings.index') }}">Site Settings</a>
                    </li>

                </ul>
            </div>
        </li>
 @endif 


 @if(true)
        <li>
            <a href="#sidebarExpages" data-bs-toggle="collapse">
                <i class="mdi mdi-shield-account text-warning"></i>
                <span> Roles And Permission </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarExpages">
                <ul class="nav-second-level">
                    <li>
                        <a href="{{ route('all.permission') }}">
                            <i class="mdi mdi-key-variant"></i> All Permission
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('add.permission') }}">
                            <i class="mdi mdi-key-plus"></i> Add Permission
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('all.roles') }}">
                            <i class="mdi mdi-account-group"></i> All Roles
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('add.roles') }}">
                            <i class="mdi mdi-account-plus"></i> Add Role
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('add.roles.permission') }}">
                            <i class="mdi mdi-shield-link-variant"></i> Assign Roles Permission
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('all.roles.permission') }}">
                            <i class="mdi mdi-shield-check"></i> All Roles Permission
                        </a>
                    </li>
                </ul>
            </div>
        </li>

@endif

 <!-- Subscriber Management -->
 @if(true)
        <li>
            <a href="#sidebarSubscribers" data-bs-toggle="collapse">
                <i class="mdi mdi-account-group text-info"></i>
                <span> Subscribers </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarSubscribers">
                <ul class="nav-second-level">
                    <li>
                        <a href="{{ route('admin.subscribers.index') }}">All Subscribers</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.subscribers.create') }}">Add New Subscriber</a>
                    </li>
                </ul>
            </div>
        </li>

        <!-- Comment Management -->
        <li>
            <a href="{{ route('admin.comment-management') }}">
                <i class="mdi mdi-comment-multiple text-primary"></i>
                <span> Comment Management </span>
            </a>
        </li>

        <li>
            <a href="#sidebarAdvertisements" data-bs-toggle="collapse">
                <i class="mdi mdi-image-multiple text-warning"></i>
                <span> Advertisements</span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarAdvertisements">
                <ul class="nav-second-level">
                    <li>
                        <a href="{{ route('admin.advertisements.index') }}">All Advertisements</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.advertisements.create') }}">Add Advertisement</a>
                    </li>
                </ul>
            </div>
        </li>

        <li>
            <a href="#sidebarSponsoredAds" data-bs-toggle="collapse">
                <i class="mdi mdi-star-circle text-warning"></i>
                <span> Sponsored Ads </span>
                <span class="menu-arrow"></span>
            </a>
            <div class="collapse" id="sidebarSponsoredAds">
                <ul class="nav-second-level">
                    <li>
                        <a href="{{ route('admin.sponsored-ads.index') }}">All Sponsored Ads</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.sponsored-ads.create') }}">Add Sponsored Ad</a>
                    </li>
                </ul>
            </div>
        </li>
 @endif





   @else

   @endif
                            
 
                        </ul>

                    </div>
                    <!-- End Sidebar -->

                    <div class="clearfix"></div>

                </div>
                <!-- Sidebar -left -->

            </div>