@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="{{ route('admin.sponsored-ads.create') }}" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Add Sponsored Ad
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Sponsored Ads Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-star-circle text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['total'] }}</h5>
                                <p class="text-muted mb-0">Total Sponsored Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['active'] }}</h5>
                                <p class="text-muted mb-0">Active Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-crown text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['premium'] }}</h5>
                                <p class="text-muted mb-0">Premium Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-newspaper text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['native'] }}</h5>
                                <p class="text-muted mb-0">Native Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Sponsored Ads</h4>
                        <p class="card-title-desc">Drag and drop to reorder sponsored ads, or use the action buttons to manage ads.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchSponsoredAds" placeholder="Search sponsored ads...">
                                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterFormat">
                                    <option value="">All Formats</option>
                                    <option value="native">Native</option>
                                    <option value="display">Display</option>
                                    <option value="video">Video</option>
                                    <option value="carousel">Carousel</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterPlacement">
                                    <option value="">All Placements</option>
                                    <option value="homepage_top">Homepage Top</option>
                                    <option value="homepage_middle">Homepage Middle</option>
                                    <option value="homepage_bottom">Homepage Bottom</option>
                                    <option value="category_top">Category Top</option>
                                    <option value="article_top">Article Top</option>
                                    <option value="sidebar">Sidebar</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                    <option value="premium">Premium Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="sponsoredAdsTable">
                                <thead>
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Sponsored Ad</th>
                                        <th>Sponsor</th>
                                        <th>Format</th>
                                        <th>Placement</th>
                                        <th>Status</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-sponsored-ads">
                                    @foreach($sponsoredAds as $ad)
                                    <tr data-ad-id="{{ $ad->id }}" data-order="{{ $ad->display_order }}" 
                                        class="sponsored-ad-row {{ $ad->is_active ? '' : 'table-secondary' }} {{ $ad->is_premium ? 'table-warning' : '' }}">
                                        <td>
                                            <span class="badge bg-secondary order-badge">{{ $ad->display_order }}</span>
                                            <i class="mdi mdi-drag-vertical text-muted ms-2" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($ad->image)
                                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" 
                                                     class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                                @endif
                                                <div>
                                                    <h6 class="mb-1">{{ Str::limit($ad->title, 40) }}</h6>
                                                    <small class="text-muted">{{ $ad->created_at->format('M d, Y') }}</small>
                                                    @if($ad->is_premium)
                                                        <span class="badge bg-warning ms-2">
                                                            <i class="mdi mdi-crown"></i> Premium
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($ad->sponsor_logo)
                                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" 
                                                     class="rounded me-2" width="30" height="30" style="object-fit: cover;">
                                                @endif
                                                <span>{{ $ad->sponsor_name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ ucfirst($ad->ad_format) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $ad->placement)) }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle" type="checkbox" 
                                                           data-ad-id="{{ $ad->id }}" {{ $ad->is_active ? 'checked' : '' }}>
                                                    <label class="form-check-label">
                                                        {{ $ad->is_active ? 'Active' : 'Inactive' }}
                                                    </label>
                                                </div>
                                                <button class="btn btn-sm {{ $ad->is_premium ? 'btn-warning' : 'btn-outline-warning' }} premium-btn" 
                                                        data-ad-id="{{ $ad->id }}" title="{{ $ad->is_premium ? 'Remove Premium' : 'Mark Premium' }}">
                                                    <i class="mdi mdi-crown"></i> {{ $ad->is_premium ? 'Premium' : 'Standard' }}
                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column performance-metrics" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored">
                                                <small><strong>Views:</strong> <span class="view-count">{{ number_format($ad->view_count) }}</span></small>
                                                <small><strong>Clicks:</strong> <span class="click-count">{{ number_format($ad->click_count) }}</span></small>
                                                @if($ad->view_count > 0)
                                                <small><strong>CTR:</strong> <span class="ctr-rate">{{ $ad->click_through_rate }}%</span></small>
                                                @endif
                                                @if($ad->budget)
                                                <small><strong>Budget:</strong> $<span class="budget">{{ number_format($ad->budget, 2) }}</span></small>
                                                @endif
                                                @php
                                                    $performance = $ad->getPerformanceMetrics();
                                                    $statusClass = match($performance['status']) {
                                                        'excellent' => 'success',
                                                        'good' => 'primary',
                                                        'average' => 'warning',
                                                        'poor' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                @endphp
                                                <small>
                                                    <span class="badge bg-{{ $statusClass }} performance-badge">
                                                        Score: <span class="performance-score">{{ $performance['performance_score'] }}</span>
                                                    </span>
                                                </small>
                                                <div class="performance-refresh mt-1">
                                                    <button class="btn btn-xs btn-outline-secondary refresh-performance" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored">
                                                        <i class="mdi mdi-refresh"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- View Button -->
                                                <a href="{{ route('admin.sponsored-ads.show', $ad->id) }}" class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('admin.sponsored-ads.edit', $ad->id) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                                
                                                <!-- Delete Button -->
                                                <form method="POST" action="{{ route('admin.sponsored-ads.destroy', $ad->id) }}" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this sponsored ad?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Showing {{ $sponsoredAds->firstItem() }} to {{ $sponsoredAds->lastItem() }} of {{ $sponsoredAds->total() }} sponsored ads
                            </div>
                            <div>
                                {{ $sponsoredAds->links() }}
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Sponsored Ads Management Specific Styles */
.sponsored-ad-row {
    transition: all 0.2s ease;
    cursor: default;
}

.sponsored-ad-row:hover {
    background-color: #f8f9fa !important;
}

.sponsored-ad-row.ui-sortable-helper {
    background-color: #fff !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
    border: 2px solid #28a745 !important;
    opacity: 0.9 !important;
    transform: rotate(1deg);
    z-index: 1000 !important;
}

.sponsored-ad-row.table-secondary {
    opacity: 0.6;
    background-color: #f8f9fa !important;
}

.sponsored-ad-row.table-warning {
    background-color: #fff3cd !important;
}

.ui-state-highlight {
    height: 60px !important;
    background-color: #d4edda !important;
    border: 2px dashed #28a745 !important;
    border-radius: 4px;
    margin: 2px 0;
}

.order-badge {
    min-width: 35px;
    display: inline-block;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.order-badge.updating {
    background-color: #ffc107 !important;
    color: #000 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

#sortable-sponsored-ads .mdi-drag-vertical {
    opacity: 0.3;
    transition: all 0.2s ease;
    cursor: grab;
    font-size: 18px;
    color: #6c757d;
}

#sortable-sponsored-ads .mdi-drag-vertical:active {
    cursor: grabbing;
}

#sortable-sponsored-ads tr:hover .mdi-drag-vertical {
    opacity: 1;
    color: #28a745;
}

.drag-handle-container {
    position: relative;
    display: inline-block;
}

.drag-tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
    z-index: 1000;
}

.drag-handle-container:hover .drag-tooltip {
    opacity: 1;
}

/* Performance metrics styling */
.performance-updated {
    background-color: #d4edda !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

<script>
// Wait for core libraries to be ready
$(document).ready(function() {
    // Check if jQuery is available
    if (typeof $ === 'undefined' || typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded! Sponsored ads management cannot initialize.');

        // Try to wait for jQuery to load
        var checkJQuery = function() {
            if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
                console.log('jQuery loaded, initializing sponsored ads management...');
                initializeSponsoredAdManagement();
            } else {
                console.warn('Still waiting for jQuery...');
                setTimeout(checkJQuery, 500);
            }
        };

        setTimeout(checkJQuery, 1000);
        return;
    }

    console.log('=== Sponsored Ads Management Initialization ===');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');
    console.log('Toastr available:', typeof toastr !== 'undefined');

    // Initialize immediately if jQuery is available
    initializeSponsoredAdManagement();
});

// Also listen for the custom event from master template
$(document).on('coreLibrariesReady', function() {
    console.log('Core libraries ready event received');
    if (typeof window.sponsoredAdManagementInitialized === 'undefined') {
        window.sponsoredAdManagementInitialized = true;
        initializeSponsoredAdManagement();
    }
});

function initializeSponsoredAdManagement() {
        console.log('Initializing Sponsored Ad Management...');

        // Initialize toastr if available
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "3000",
                "preventDuplicates": true
            };
        }

        try {
            // Initialize search functionality
            initializeSponsoredAdSearchFilters();

            // Initialize drag and drop functionality
            initializeSponsoredAdSortable();

            // Initialize status toggles
            initializeSponsoredAdStatusToggles();

            // Initialize performance tracking
            initializeSponsoredAdPerformanceTracking();

            console.log('Sponsored Ad Management initialized successfully');
        } catch (error) {
            console.error('Error initializing Sponsored Ad Management:', error);
        }
    }

    // Search and Filter Functionality
    function initializeSponsoredAdSearchFilters() {
        console.log('Initializing sponsored ad search filters...');

        // Real-time search
        $('#searchSponsoredAds').on('input', function() {
            filterSponsoredAds();
        });

        // Status filter
        $('#filterStatus').on('change', function() {
            filterSponsoredAds();
        });

        // Format filter
        $('#filterFormat').on('change', function() {
            filterSponsoredAds();
        });

        // Combined filter function
        function filterSponsoredAds() {
            var searchTerm = $('#searchSponsoredAds').val().toLowerCase();
            var statusFilter = $('#filterStatus').val();
            var formatFilter = $('#filterFormat').val();

            var visibleCount = 0;

            $('#sortable-sponsored-ads tr').each(function() {
                var $row = $(this);
                var title = $row.find('h6').text().toLowerCase();
                var sponsor = $row.find('.text-info').text().toLowerCase();
                var format = $row.find('.badge').eq(1).text().toLowerCase();
                var isActive = !$row.hasClass('table-secondary');

                // Search match
                var searchMatch = searchTerm === '' ||
                                title.includes(searchTerm) ||
                                sponsor.includes(searchTerm) ||
                                format.includes(searchTerm);

                // Status match
                var statusMatch = statusFilter === '' ||
                                (statusFilter === 'active' && isActive) ||
                                (statusFilter === 'inactive' && !isActive);

                // Format match
                var formatMatch = formatFilter === '' || format.includes(formatFilter.toLowerCase());

                if (searchMatch && statusMatch && formatMatch) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            // Update results count
            updateSponsoredAdResultsCount(visibleCount);
        }

        function updateSponsoredAdResultsCount(count) {
            var $resultsInfo = $('.sponsored-results-info');
            if ($resultsInfo.length === 0) {
                $('#sponsoredAdsTable').before('<div class="sponsored-results-info mb-2"><small class="text-muted">Showing <span class="results-count">' + count + '</span> sponsored ads</small></div>');
            } else {
                $resultsInfo.find('.results-count').text(count);
            }
        }
    }

    function initializeSponsoredAdSortable() {
        console.log('Initializing sponsored ad sortable...');

        var $table = $("#sortable-sponsored-ads");
        console.log('Sponsored ad table found:', $table.length);

        if ($table.length === 0) {
            console.error('Sponsored ad sortable table not found');
            return;
        }

        try {
            // Destroy existing sortable if it exists
            if ($table.hasClass('ui-sortable')) {
                $table.sortable('destroy');
            }

            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr",
                axis: "y",
                cursor: "grabbing",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Sponsored ad drag started');
                    ui.placeholder.height(ui.item.height());
                    ui.item.addClass('drag-indicator');
                },
                update: function(event, ui) {
                    console.log('Sponsored ad order updated');
                    updateSponsoredAdOrder();
                },
                stop: function(event, ui) {
                    console.log('Sponsored ad drag stopped');
                    ui.item.removeClass('drag-indicator');
                }
            });

            console.log('Sponsored ad sortable initialized successfully');

        } catch (error) {
            console.error('Sponsored ad sortable initialization error:', error);
        }
    }

    // Update sponsored ad order after drag and drop
    function updateSponsoredAdOrder() {
        var ads = [];
        var hasChanges = false;

        $("#sortable-sponsored-ads tr").each(function(index) {
            var adId = $(this).data('ad-id');
            var currentOrder = $(this).data('order');

            if (adId) {
                ads.push({
                    id: adId,
                    order: index
                });

                // Check if order actually changed
                if (currentOrder !== index) {
                    hasChanges = true;
                }

                // Update the order badge with new index
                $(this).find('.order-badge').text(index);
                $(this).data('order', index); // Update data attribute
            }
        });

        if (!hasChanges) {
            console.log('No order changes detected');
            return;
        }

        console.log('Updating order for sponsored ads:', ads);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route("admin.sponsored-ads.update-order") }}',
            method: 'POST',
            data: {
                sponsored_ads: ads,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                $('.order-badge').addClass('updating');
                $('#sortable-sponsored-ads').addClass('updating');
            },
            success: function(response) {
                console.log('Order update response:', response);

                if (response.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message || 'Sponsored ad order updated successfully!');
                    }

                    // Update visual feedback
                    $('.order-badge').removeClass('updating');
                    $('#sortable-sponsored-ads').removeClass('updating');

                    // Flash success indication
                    $('.order-badge').addClass('bg-success').removeClass('bg-secondary');
                    setTimeout(function() {
                        $('.order-badge').addClass('bg-secondary').removeClass('bg-success');
                    }, 1000);
                } else {
                    throw new Error(response.message || 'Unknown error occurred');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);

                var errorMessage = 'Failed to update sponsored ad order';
                try {
                    var response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                } else {
                    alert(errorMessage);
                }

                // Reset visual state
                $('.order-badge').removeClass('updating');
                $('#sortable-sponsored-ads').removeClass('updating');

                // Reload to restore original order
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        });
    }

    // Status toggle functionality
    $(document).on('change', '.status-toggle', function() {
        var adId = $(this).data('ad-id');
        var isActive = $(this).is(':checked');
        var label = $(this).next('label');
        var toggle = $(this);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route("admin.sponsored-ads.toggle-status", ":id") }}'.replace(':id', adId),
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                toggle.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    label.text(response.is_active ? 'Active' : 'Inactive');
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                }
                toggle.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('Status toggle error:', xhr.responseText);
                // Revert the toggle
                toggle.prop('checked', !isActive);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to toggle sponsored ad status');
                } else {
                    alert('Failed to toggle sponsored ad status');
                }
                toggle.prop('disabled', false);
            }
        });
    });

    // Premium toggle functionality
    $(document).on('click', '.premium-btn', function(e) {
        e.preventDefault();
        var adId = $(this).data('ad-id');
        var btn = $(this);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route("admin.sponsored-ads.toggle-premium", ":id") }}'.replace(':id', adId),
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                btn.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Update button appearance
                    if (response.is_premium) {
                        btn.removeClass('btn-outline-warning').addClass('btn-warning');
                        btn.attr('title', 'Remove Premium');
                        btn.html('<i class="mdi mdi-crown"></i> Premium');
                    } else {
                        btn.removeClass('btn-warning').addClass('btn-outline-warning');
                        btn.attr('title', 'Mark Premium');
                        btn.html('<i class="mdi mdi-crown"></i> Standard');
                    }

                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                }
                btn.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('Premium toggle error:', xhr.responseText);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to toggle premium status');
                } else {
                    alert('Failed to toggle premium status');
                }
                btn.prop('disabled', false);
            }
        });
    });

    // Search functionality
    $('#searchSponsoredAds').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('#sponsoredAdsTable tbody tr').each(function() {
            var title = $(this).find('h6').text().toLowerCase();
            var sponsor = $(this).find('td:nth-child(3)').text().toLowerCase();

            if (title.includes(searchTerm) || sponsor.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Filter functionality
    $('#filterFormat, #filterPlacement, #filterStatus').change(function() {
        var formatFilter = $('#filterFormat').val();
        var placementFilter = $('#filterPlacement').val();
        var statusFilter = $('#filterStatus').val();

        $('#sponsoredAdsTable tbody tr').each(function() {
            var show = true;

            if (formatFilter) {
                var adFormat = $(this).find('.badge.bg-primary').text().toLowerCase();
                show = show && adFormat.includes(formatFilter);
            }

            if (placementFilter) {
                var placement = $(this).find('.badge.bg-info').text().toLowerCase().replace(/\s+/g, '_');
                show = show && placement.includes(placementFilter);
            }

            if (statusFilter) {
                var isActive = $(this).find('.status-toggle').is(':checked');
                var isPremium = $(this).hasClass('table-warning');

                if (statusFilter === 'active') {
                    show = show && isActive;
                } else if (statusFilter === 'inactive') {
                    show = show && !isActive;
                } else if (statusFilter === 'premium') {
                    show = show && isPremium;
                }
            }

            if (show) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Real-time performance tracking for sponsored ads
    initializeSponsoredAdPerformanceTracking();

    function initializeSponsoredAdPerformanceTracking() {
        // Auto-refresh performance every 30 seconds
        setInterval(function() {
            refreshAllSponsoredAdPerformanceMetrics();
        }, 30000);

        // Manual refresh button click
        $(document).on('click', '.refresh-performance', function() {
            var adId = $(this).data('ad-id');
            var adType = $(this).data('ad-type');
            refreshSponsoredAdPerformanceMetrics(adId, adType);
        });
    }

    function refreshAllSponsoredAdPerformanceMetrics() {
        $('.performance-metrics').each(function() {
            var adId = $(this).data('ad-id');
            var adType = $(this).data('ad-type');
            refreshSponsoredAdPerformanceMetrics(adId, adType);
        });
    }

    function refreshSponsoredAdPerformanceMetrics(adId, adType) {
        var endpoint = '/api/sponsored-ads/performance/' + adId;

        $.ajax({
            url: endpoint,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateSponsoredAdPerformanceDisplay(adId, response.performance);
                }
            },
            error: function(xhr, status, error) {
                console.error('Sponsored ad performance refresh error:', error);
            }
        });
    }

    function updateSponsoredAdPerformanceDisplay(adId, performance) {
        var container = $('.performance-metrics[data-ad-id="' + adId + '"]');

        // Update metrics
        container.find('.view-count').text(performance.views.toLocaleString());
        container.find('.click-count').text(performance.clicks.toLocaleString());
        container.find('.ctr-rate').text(performance.ctr + '%');
        container.find('.performance-score').text(performance.performance_score);

        // Update budget utilization if available
        if (performance.budget_utilization !== undefined) {
            container.find('.budget-utilization').text(performance.budget_utilization + '%');
        }

        // Update badge color based on status
        var badge = container.find('.performance-badge');
        badge.removeClass('bg-success bg-primary bg-warning bg-danger bg-secondary');

        var statusClass = getSponsoredAdStatusClass(performance.status);
        badge.addClass('bg-' + statusClass);

        // Add flash effect
        container.addClass('performance-updated');
        setTimeout(function() {
            container.removeClass('performance-updated');
        }, 1000);
    }

    function getSponsoredAdStatusClass(status) {
        switch(status) {
            case 'excellent': return 'success';
            case 'good': return 'primary';
            case 'average': return 'warning';
            case 'poor': return 'danger';
            default: return 'secondary';
        }
    }
} // End of main if statement
</script>

<!-- Add CSS for performance updates -->
<style>
.performance-updated {
    background-color: #e8f5e8 !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

@endsection
