@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="{{ route('admin.sponsored-ads.create') }}" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Add Sponsored Ad
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Sponsored Ads Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-star-circle text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['total'] }}</h5>
                                <p class="text-muted mb-0">Total Sponsored Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['active'] }}</h5>
                                <p class="text-muted mb-0">Active Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-crown text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['premium'] }}</h5>
                                <p class="text-muted mb-0">Premium Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-newspaper text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['native'] }}</h5>
                                <p class="text-muted mb-0">Native Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Sponsored Ads</h4>
                        <p class="card-title-desc">Drag and drop to reorder sponsored ads, or use the action buttons to manage ads.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchSponsoredAds" placeholder="Search sponsored ads...">
                                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterFormat">
                                    <option value="">All Formats</option>
                                    <option value="native">Native</option>
                                    <option value="display">Display</option>
                                    <option value="video">Video</option>
                                    <option value="carousel">Carousel</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterPlacement">
                                    <option value="">All Placements</option>
                                    <option value="homepage_top">Homepage Top</option>
                                    <option value="homepage_middle">Homepage Middle</option>
                                    <option value="homepage_bottom">Homepage Bottom</option>
                                    <option value="category_top">Category Top</option>
                                    <option value="article_top">Article Top</option>
                                    <option value="sidebar">Sidebar</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                    <option value="premium">Premium Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="sponsoredAdsTable">
                                <thead>
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Sponsored Ad</th>
                                        <th>Sponsor</th>
                                        <th>Format</th>
                                        <th>Placement</th>
                                        <th>Status</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-sponsored-ads">
                                    @foreach($sponsoredAds as $ad)
                                    <tr data-ad-id="{{ $ad->id }}" data-order="{{ $ad->display_order }}" 
                                        class="sponsored-ad-row {{ $ad->is_active ? '' : 'table-secondary' }} {{ $ad->is_premium ? 'table-warning' : '' }}">
                                        <td>
                                            <span class="badge bg-secondary order-badge">{{ $ad->display_order }}</span>
                                            <i class="mdi mdi-drag-vertical text-muted ms-2" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($ad->image)
                                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" 
                                                     class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                                @endif
                                                <div>
                                                    <h6 class="mb-1">{{ Str::limit($ad->title, 40) }}</h6>
                                                    <small class="text-muted">{{ $ad->created_at->format('M d, Y') }}</small>
                                                    @if($ad->is_premium)
                                                        <span class="badge bg-warning ms-2">
                                                            <i class="mdi mdi-crown"></i> Premium
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($ad->sponsor_logo)
                                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" 
                                                     class="rounded me-2" width="30" height="30" style="object-fit: cover;">
                                                @endif
                                                <span>{{ $ad->sponsor_name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ ucfirst($ad->ad_format) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $ad->placement)) }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle" type="checkbox" 
                                                           data-ad-id="{{ $ad->id }}" {{ $ad->is_active ? 'checked' : '' }}>
                                                    <label class="form-check-label">
                                                        {{ $ad->is_active ? 'Active' : 'Inactive' }}
                                                    </label>
                                                </div>
                                                <button class="btn btn-sm {{ $ad->is_premium ? 'btn-warning' : 'btn-outline-warning' }} premium-btn" 
                                                        data-ad-id="{{ $ad->id }}" title="{{ $ad->is_premium ? 'Remove Premium' : 'Mark Premium' }}">
                                                    <i class="mdi mdi-crown"></i> {{ $ad->is_premium ? 'Premium' : 'Standard' }}
                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column performance-metrics" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored">
                                                <small><strong>Views:</strong> <span class="view-count">{{ number_format($ad->view_count) }}</span></small>
                                                <small><strong>Clicks:</strong> <span class="click-count">{{ number_format($ad->click_count) }}</span></small>
                                                @if($ad->view_count > 0)
                                                <small><strong>CTR:</strong> <span class="ctr-rate">{{ $ad->click_through_rate }}%</span></small>
                                                @endif
                                                @if($ad->budget)
                                                <small><strong>Budget:</strong> $<span class="budget">{{ number_format($ad->budget, 2) }}</span></small>
                                                @endif
                                                @php
                                                    $performance = $ad->getPerformanceMetrics();
                                                    $statusClass = match($performance['status']) {
                                                        'excellent' => 'success',
                                                        'good' => 'primary',
                                                        'average' => 'warning',
                                                        'poor' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                @endphp
                                                <small>
                                                    <span class="badge bg-{{ $statusClass }} performance-badge">
                                                        Score: <span class="performance-score">{{ $performance['performance_score'] }}</span>
                                                    </span>
                                                </small>
                                                <div class="performance-refresh mt-1">
                                                    <button class="btn btn-xs btn-outline-secondary refresh-performance" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored">
                                                        <i class="mdi mdi-refresh"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- View Button -->
                                                <a href="{{ route('admin.sponsored-ads.show', $ad->id) }}" class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('admin.sponsored-ads.edit', $ad->id) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                                
                                                <!-- Delete Button -->
                                                <form method="POST" action="{{ route('admin.sponsored-ads.destroy', $ad->id) }}" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this sponsored ad?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Showing {{ $sponsoredAds->firstItem() }} to {{ $sponsoredAds->lastItem() }} of {{ $sponsoredAds->total() }} sponsored ads
                            </div>
                            <div>
                                {{ $sponsoredAds->links() }}
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Sponsored Ads Management Specific Styles */
.sponsored-ad-row {
    transition: all 0.2s ease;
    cursor: default;
}

.sponsored-ad-row:hover {
    background-color: #f8f9fa !important;
}

.sponsored-ad-row.ui-sortable-helper {
    background-color: #fff !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
    border: 2px solid #28a745 !important;
    opacity: 0.9 !important;
    transform: rotate(1deg);
    z-index: 1000 !important;
}

.sponsored-ad-row.table-secondary {
    opacity: 0.6;
    background-color: #f8f9fa !important;
}

.sponsored-ad-row.table-warning {
    background-color: #fff3cd !important;
}

.ui-state-highlight {
    height: 60px !important;
    background-color: #d4edda !important;
    border: 2px dashed #28a745 !important;
    border-radius: 4px;
    margin: 2px 0;
}

.order-badge {
    min-width: 35px;
    display: inline-block;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.order-badge.updating {
    background-color: #ffc107 !important;
    color: #000 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

#sortable-sponsored-ads .mdi-drag-vertical {
    opacity: 0.3;
    transition: all 0.2s ease;
    cursor: grab;
    font-size: 18px;
    color: #6c757d;
}

#sortable-sponsored-ads .mdi-drag-vertical:active {
    cursor: grabbing;
}

#sortable-sponsored-ads tr:hover .mdi-drag-vertical {
    opacity: 1;
    color: #28a745;
}

.drag-handle-container {
    position: relative;
    display: inline-block;
}

.drag-tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
    z-index: 1000;
}

.drag-handle-container:hover .drag-tooltip {
    opacity: 1;
}

/* Performance metrics styling */
.performance-updated {
    background-color: #d4edda !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

<script>
// Sponsored Ads Management - Complete Clean Implementation
(function() {
    'use strict';

    // Wait for jQuery to be available
    function waitForJQuery(callback) {
        if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
            callback();
        } else {
            setTimeout(function() { waitForJQuery(callback); }, 100);
        }
    }

    // Initialize when ready
    waitForJQuery(function() {
        $(document).ready(function() {
            console.log('=== Sponsored Ads Management Initialization ===');
            console.log('jQuery version:', $.fn.jquery);

            // Initialize toastr
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    "closeButton": true,
                    "progressBar": true,
                    "positionClass": "toast-top-right",
                    "timeOut": "3000",
                    "preventDuplicates": true
                };
            }

            // Initialize all features
            initializeSponsoredAdSearchFilters();
            initializeSponsoredAdDragAndDrop();
            initializeSponsoredAdStatusToggles();
            initializeSponsoredAdPerformanceTracking();

            console.log('Sponsored Ads Management initialized successfully');
        });
    });

    // Search and Filter Functionality
    function initializeSponsoredAdSearchFilters() {
        console.log('Initializing sponsored ad search filters...');

        $('#searchSponsoredAds').on('input', filterSponsoredAds);
        $('#filterStatus').on('change', filterSponsoredAds);
        $('#filterFormat').on('change', filterSponsoredAds);
        $('#filterPlacement').on('change', filterSponsoredAds);

        function filterSponsoredAds() {
            var searchTerm = $('#searchSponsoredAds').val().toLowerCase();
            var statusFilter = $('#filterStatus').val();
            var formatFilter = $('#filterFormat').val();
            var placementFilter = $('#filterPlacement').val();
            var visibleCount = 0;

            console.log('Filtering sponsored ads with:', { searchTerm, statusFilter, formatFilter, placementFilter });

            $('#sortable-sponsored-ads tr').each(function() {
                var $row = $(this);

                // Get text content from correct columns
                var title = $row.find('td').eq(1).find('h6').text().toLowerCase();
                var sponsor = $row.find('td').eq(1).find('span').text().toLowerCase();
                var format = $row.find('td').eq(2).find('.badge').text().toLowerCase();
                var placement = $row.find('td').eq(3).find('.badge').text().toLowerCase();
                var isActive = !$row.hasClass('table-secondary');

                console.log('Row data:', { title, sponsor, format, placement, isActive });

                // Search match - search in title, sponsor, format, and placement
                var searchMatch = searchTerm === '' ||
                                title.includes(searchTerm) ||
                                sponsor.includes(searchTerm) ||
                                format.includes(searchTerm) ||
                                placement.includes(searchTerm);

                // Status match
                var statusMatch = statusFilter === '' ||
                                (statusFilter === 'active' && isActive) ||
                                (statusFilter === 'inactive' && !isActive);

                // Format match
                var formatMatch = formatFilter === '' || format.includes(formatFilter.toLowerCase());

                // Placement match
                var placementMatch = placementFilter === '' || placement.replace(/\s+/g, '_').toLowerCase().includes(placementFilter.toLowerCase());

                if (searchMatch && statusMatch && formatMatch && placementMatch) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            console.log('Visible sponsored ads:', visibleCount);
            updateSponsoredAdResultsCount(visibleCount);
        }

        function updateSponsoredAdResultsCount(count) {
            var $resultsInfo = $('.sponsored-results-info');
            if ($resultsInfo.length === 0) {
                $('#sponsoredAdsTable').before('<div class="sponsored-results-info mb-2"><small class="text-muted">Showing <span class="results-count">' + count + '</span> sponsored ads</small></div>');
            } else {
                $resultsInfo.find('.results-count').text(count);
            }
        }
    }

    // Drag and Drop Functionality
    function initializeSponsoredAdDragAndDrop() {
        console.log('Initializing sponsored ad drag and drop...');

        if (typeof $.ui === 'undefined' || typeof $.ui.sortable === 'undefined') {
            console.warn('jQuery UI not available, loading dynamically...');
            var script = document.createElement('script');
            script.src = 'https://code.jquery.com/ui/1.13.2/jquery-ui.min.js';
            script.onload = function() {
                setTimeout(initializeSponsoredAdDragAndDrop, 100);
            };
            document.head.appendChild(script);
            return;
        }

        var $table = $('#sortable-sponsored-ads');
        if ($table.length === 0) {
            console.warn('Sortable sponsored ads table not found');
            return;
        }

        try {
            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr:visible",
                axis: "y",
                cursor: "grabbing",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                opacity: 0.8,
                distance: 5,
                update: function(event, ui) {
                    var order = [];
                    $table.find('tr:visible').each(function(index) {
                        var adId = $(this).data('ad-id');
                        if (adId) {
                            order.push({
                                id: adId,
                                order: index + 1
                            });
                            $(this).find('.order-badge').text(index + 1);
                        }
                    });

                    updateSponsoredAdOrder(order);
                }
            });

            console.log('Sponsored ad drag and drop initialized successfully');
        } catch (error) {
            console.error('Sponsored ad drag and drop initialization error:', error);
        }
    }

    function updateSponsoredAdOrder(order) {
        console.log('Updating sponsored ad order:', order);

        $.ajax({
            url: '{{ route("admin.sponsored-ads.update-order") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                sponsored_ads: order
            },
            beforeSend: function() {
                console.log('Sending sponsored ad order update request...');
            },
            success: function(response) {
                console.log('Sponsored ad order update response:', response);

                if (response.success) {
                    toastr.success('Sponsored ad order updated successfully!');
                } else {
                    toastr.error('Failed to update sponsored ad order');
                    setTimeout(function() { location.reload(); }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Sponsored ad order update error:', xhr.responseText);
                toastr.error('Failed to update sponsored ad order');
                setTimeout(function() { location.reload(); }, 2000);
            }
        });
    }

    // Status Toggle Functionality
    function initializeSponsoredAdStatusToggles() {
        console.log('Initializing sponsored ad status toggles...');

        $(document).on('change', '.status-toggle', function() {
            var $toggle = $(this);
            var adId = $toggle.data('ad-id');
            var isActive = $toggle.is(':checked');

            $toggle.prop('disabled', true);

            $.ajax({
                url: '/admin/sponsored-ads/' + adId + '/toggle-status',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    is_active: isActive
                },
                success: function(response) {
                    if (response.success) {
                        var $row = $toggle.closest('tr');
                        if (isActive) {
                            $row.removeClass('table-secondary');
                            toastr.success('Sponsored ad activated successfully!');
                        } else {
                            $row.addClass('table-secondary');
                            toastr.success('Sponsored ad deactivated successfully!');
                        }
                    } else {
                        $toggle.prop('checked', !isActive);
                        toastr.error(response.message || 'Failed to update status');
                    }
                },
                error: function(xhr) {
                    $toggle.prop('checked', !isActive);
                    toastr.error('Failed to update sponsored ad status');
                },
                complete: function() {
                    $toggle.prop('disabled', false);
                }
            });
        });
    }

    // Performance Tracking Functionality
    function initializeSponsoredAdPerformanceTracking() {
        console.log('Initializing sponsored ad performance tracking...');

        setInterval(function() {
            refreshSponsoredAdPerformanceStats();
        }, 30000);

        $(document).on('click', '.refresh-performance', function() {
            refreshSponsoredAdPerformanceStats();
        });
    }

    function refreshSponsoredAdPerformanceStats() {
        $.ajax({
            url: '{{ route("admin.sponsored-ads.performance-stats") }}',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updateSponsoredAdPerformanceDisplay(response.data);
                }
            },
            error: function() {
                console.error('Failed to refresh sponsored ad performance stats');
            }
        });
    }

    function updateSponsoredAdPerformanceDisplay(data) {
        $('.total-sponsored-ads').text(data.total || 0);
        $('.active-sponsored-ads').text(data.active || 0);
        $('.total-sponsored-views').text(data.total_views || 0);
        $('.total-sponsored-clicks').text(data.total_clicks || 0);
    }

})(); // End of IIFE
</script>

<style>
.ui-state-highlight {
    height: 60px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
}

.drag-handle {
    cursor: grab;
}

.drag-handle:active {
    cursor: grabbing;
}

.refresh-performance {
    opacity: 0.7;
    transition: opacity 0.3s;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

@endsection