<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Site Identity
            [
                'key' => 'site_name',
                'value' => 'NitiKotha',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Your Trusted News Source',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Tagline',
                'description' => 'A short description of your website',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'key' => 'site_logo',
                'value' => 'backend/assets/images/logo-light.png',
                'type' => 'image',
                'group' => 'branding',
                'label' => 'Site Logo',
                'description' => 'Main logo for the website (recommended: 200x50px)',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'key' => 'site_logo_dark',
                'value' => 'backend/assets/images/logo-dark.png',
                'type' => 'image',
                'group' => 'branding',
                'label' => 'Dark Logo',
                'description' => 'Dark version of the logo for light backgrounds',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'key' => 'site_logo_sm',
                'value' => 'backend/assets/images/logo-sm.png',
                'type' => 'image',
                'group' => 'branding',
                'label' => 'Small Logo',
                'description' => 'Small logo for mobile/compact view (recommended: 40x40px)',
                'sort_order' => 3,
                'is_active' => true
            ],
            [
                'key' => 'site_favicon',
                'value' => 'backend/assets/images/favicon.ico',
                'type' => 'image',
                'group' => 'branding',
                'label' => 'Site Favicon',
                'description' => 'Favicon for the website (recommended: 32x32px)',
                'sort_order' => 4,
                'is_active' => true
            ],
            
            // Contact Information
            [
                'key' => 'contact_phone',
                'value' => '01736990123',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'key' => 'contact_address',
                'value' => 'Road -15, House -18, sector -12, Uttara Dhaka',
                'type' => 'textarea',
                'group' => 'contact',
                'label' => 'Contact Address',
                'description' => 'Physical address of the organization',
                'sort_order' => 3,
                'is_active' => true
            ],
            
            // Social Media
            [
                'key' => 'social_facebook',
                'value' => 'https://facebook.com/nitikotha',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Facebook page URL',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'key' => 'social_twitter',
                'value' => 'https://twitter.com/nitikotha',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Twitter profile URL',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'key' => 'social_youtube',
                'value' => 'https://youtube.com/nitikotha',
                'type' => 'url',
                'group' => 'social',
                'label' => 'YouTube URL',
                'description' => 'YouTube channel URL',
                'sort_order' => 3,
                'is_active' => true
            ],
            
            // SEO Settings
            [
                'key' => 'meta_description',
                'value' => 'NitiKotha - Your trusted source for latest news and updates',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Meta Description',
                'description' => 'Default meta description for SEO',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'key' => 'meta_keywords',
                'value' => 'news, bangladesh, politics, sports, technology, entertainment',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Meta Keywords',
                'description' => 'Default meta keywords for SEO',
                'sort_order' => 2,
                'is_active' => true
            ]
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
