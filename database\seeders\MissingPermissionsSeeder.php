<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class MissingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define missing permissions with their group names
        $missingPermissions = [
            // Comment Management
            [
                'name' => 'comment.menu',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.view',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.approve',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.reject',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.delete',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.bulk.action',
                'group_name' => 'Comment Management'
            ],
            [
                'name' => 'comment.filter',
                'group_name' => 'Comment Management'
            ],

            // Advertisement Management
            [
                'name' => 'advertisement.menu',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.view',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.create',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.edit',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.update',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.delete',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.status.toggle',
                'group_name' => 'Advertisement Management'
            ],
            [
                'name' => 'advertisement.analytics',
                'group_name' => 'Advertisement Management'
            ],

            // Sponsored Ads Management
            [
                'name' => 'sponsored.ads.menu',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.view',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.create',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.edit',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.update',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.delete',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.status.toggle',
                'group_name' => 'Sponsored Ads Management'
            ],
            [
                'name' => 'sponsored.ads.analytics',
                'group_name' => 'Sponsored Ads Management'
            ],

            // Additional Admin Permissions
            [
                'name' => 'admin.settings.view',
                'group_name' => 'Admin Settings'
            ],
            [
                'name' => 'admin.settings.update',
                'group_name' => 'Admin Settings'
            ],
            [
                'name' => 'admin.backup.create',
                'group_name' => 'Admin Settings'
            ],
            [
                'name' => 'admin.backup.restore',
                'group_name' => 'Admin Settings'
            ],

            // User Management (if missing)
            [
                'name' => 'user.management.menu',
                'group_name' => 'User Management'
            ],
            [
                'name' => 'user.management.view',
                'group_name' => 'User Management'
            ],
            [
                'name' => 'user.management.create',
                'group_name' => 'User Management'
            ],
            [
                'name' => 'user.management.edit',
                'group_name' => 'User Management'
            ],
            [
                'name' => 'user.management.delete',
                'group_name' => 'User Management'
            ],

            // Subscriber Management (if missing)
            [
                'name' => 'subscriber.management.menu',
                'group_name' => 'Subscriber Management'
            ],
            [
                'name' => 'subscriber.management.view',
                'group_name' => 'Subscriber Management'
            ],
            [
                'name' => 'subscriber.management.create',
                'group_name' => 'Subscriber Management'
            ],
            [
                'name' => 'subscriber.management.edit',
                'group_name' => 'Subscriber Management'
            ],
            [
                'name' => 'subscriber.management.delete',
                'group_name' => 'Subscriber Management'
            ],
            [
                'name' => 'subscriber.status.toggle',
                'group_name' => 'Subscriber Management'
            ],
        ];

        // Create permissions
        foreach ($missingPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                ['group_name' => $permissionData['group_name']]
            );
        }

        // Assign all new permissions to SuperAdmin role
        $superAdminRole = Role::where('name', 'SuperAdmin')->first();
        if ($superAdminRole) {
            $permissionNames = collect($missingPermissions)->pluck('name')->toArray();
            $superAdminRole->givePermissionTo($permissionNames);
        }

        // Also assign to admin role if it exists
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $permissionNames = collect($missingPermissions)->pluck('name')->toArray();
            $adminRole->givePermissionTo($permissionNames);
        }

        $this->command->info('Missing permissions created and assigned successfully!');
    }
}
