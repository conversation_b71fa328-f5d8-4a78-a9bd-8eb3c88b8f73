<?php
$categories = App\Models\Category::orderBy('category_name','ASC')->get();
$cdate = new DateTime();
?>

<header class="premium-header">
    <!-- Header Top Bar -->
    <div class="header-top">
        <div class="container">
            <div class="header-top-content">
                <div class="header-left">
                    <div class="header-date">
                        <i class="fas fa-calendar-alt"></i>
                        <span><?php echo e($cdate->format('l, F j, Y')); ?></span>
                    </div>
                    <div class="weather-info">
                        <i class="fas fa-cloud-sun"></i>
                        <span>Dhaka 28°C</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="language-selector">
                        <select class="changeLang">
                            <option value="en" <?php echo e(session()->get('locale') == 'en' ? 'selected' : ''); ?>>English</option>
                            <option value="bn" <?php echo e(session()->get('locale') == 'bn' ? 'selected' : ''); ?>>বাংলা</option>
                            <option value="hi" <?php echo e(session()->get('locale') == 'hi' ? 'selected' : ''); ?>>हिंदी</option>
                            <option value="fr" <?php echo e(session()->get('locale') == 'fr' ? 'selected' : ''); ?>>Français</option>
                        </select>
                    </div>
                    <div class="header-social">
                        <a href="#" class="social-link facebook" title="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link twitter" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link youtube" title="YouTube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link instagram" title="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Header Main -->
    <div class="header-main">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="header-logo">
                    <a href="<?php echo e(url('/')); ?>" class="logo">
                        <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="NitiKotha" class="logo-image">
                        <div class="logo-text">
                            <span class="logo-title">NitiKotha</span>
                            <span class="logo-tagline">Your Trusted News Source</span>
                        </div>
                    </a>
                </div>
                
                <!-- Search Bar -->
                <div class="header-search">
                    <form class="search-form" action="<?php echo e(route('search')); ?>" method="GET">
                        <div class="search-input-group">
                            <input type="text" name="search" class="search-input" placeholder="Search news..." autocomplete="off">
                            <button type="submit" class="search-button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <div class="live-indicator">
                        <span class="live-dot"></span>
                        <span class="live-text">LIVE</span>
                    </div>

                    <?php if(auth()->guard()->check()): ?>
                        <?php if(Auth::user()->role == 'subscriber'): ?>
                            <a href="<?php echo e(route('subscriber.dashboard')); ?>" class="auth-btn">
                                <i class="fas fa-user-circle"></i>
                                <span>Dashboard</span>
                            </a>
                        <?php elseif(Auth::user()->role == 'admin'): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="auth-btn">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Admin</span>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('user.logout')); ?>" class="auth-btn logout">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="login-btn">
                            <i class="fas fa-user"></i>
                            <span>Login</span>
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="auth-btn register">
                            <i class="fas fa-user-plus"></i>
                            <span>Register</span>
                        </a>
                    <?php endif; ?>

                    <button class="menu-toggle" id="menuToggle">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</header>
    
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/body/header_ultra_modern.blade.php ENDPATH**/ ?>