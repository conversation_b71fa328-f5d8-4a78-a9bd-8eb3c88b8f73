
<?php if($ad->isCurrentlyActive()): ?>
<div class="fb-sponsored-ad fb-sponsored-ad-<?php echo e($position); ?>" data-ad-id="<?php echo e($ad->id); ?>">
    <div class="fb-sponsored-header">
        <span class="fb-sponsored-label">
            <i class="fas fa-bullhorn"></i>
            Sponsored
        </span>
        <?php if($ad->is_premium): ?>
            <span class="fb-premium-badge">
                <i class="fas fa-crown"></i>
                Premium
            </span>
        <?php endif; ?>
    </div>

    <?php if($ad->ad_format === 'native'): ?>
        
        <div class="fb-native-ad">
            <div class="fb-native-header">
                <?php if($ad->sponsor_logo): ?>
                    <img src="<?php echo e(asset($ad->sponsor_logo)); ?>" alt="<?php echo e($ad->sponsor_name); ?>" class="fb-sponsor-logo">
                <?php endif; ?>
                <div class="fb-sponsor-info">
                    <h6 class="fb-sponsor-name"><?php echo e($ad->sponsor_name); ?></h6>
                    <span class="fb-sponsor-label">Sponsored</span>
                </div>
            </div>
            
            <?php if($ad->image): ?>
                <div class="fb-native-image">
                    <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" loading="lazy">
                </div>
            <?php endif; ?>
            
            <div class="fb-native-content">
                <h5 class="fb-native-title"><?php echo e($ad->title); ?></h5>
                <p class="fb-native-description"><?php echo e(Str::limit($ad->content, 120)); ?></p>
                
                <?php if($ad->link_url): ?>
                    <a href="<?php echo e($ad->link_url); ?>" target="_blank" class="fb-native-cta" onclick="trackAdClick(<?php echo e($ad->id); ?>)">
                        Learn More
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif($ad->ad_format === 'display'): ?>
        
        <div class="fb-display-ad">
            <?php if($ad->link_url): ?>
                <a href="<?php echo e($ad->link_url); ?>" target="_blank" onclick="trackAdClick(<?php echo e($ad->id); ?>)">
            <?php endif; ?>
            
            <?php if($ad->image): ?>
                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-display-image">
            <?php else: ?>
                <div class="fb-display-text">
                    <h5><?php echo e($ad->title); ?></h5>
                    <p><?php echo e($ad->content); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if($ad->link_url): ?>
                </a>
            <?php endif; ?>
        </div>

    <?php elseif($ad->ad_format === 'video'): ?>
        
        <div class="fb-video-ad">
            <div class="fb-video-header">
                <h6><?php echo e($ad->title); ?></h6>
                <span class="fb-sponsored-label">Sponsored</span>
            </div>
            
            <?php if($ad->video_url): ?>
                <div class="fb-video-container">
                    <video controls poster="<?php echo e($ad->image ? asset($ad->image) : ''); ?>">
                        <source src="<?php echo e($ad->video_url); ?>" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            <?php elseif($ad->image): ?>
                <div class="fb-video-placeholder">
                    <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>">
                    <div class="fb-play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="fb-video-content">
                <p><?php echo e(Str::limit($ad->content, 100)); ?></p>
                <?php if($ad->link_url): ?>
                    <a href="<?php echo e($ad->link_url); ?>" target="_blank" class="fb-video-cta" onclick="trackAdClick(<?php echo e($ad->id); ?>)">
                        Watch Now
                    </a>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif($ad->ad_format === 'carousel'): ?>
        
        <div class="fb-carousel-ad">
            <div class="fb-carousel-header">
                <h6><?php echo e($ad->title); ?></h6>
                <span class="fb-sponsored-label">Sponsored</span>
            </div>
            
            <div class="fb-carousel-container">
                <div class="fb-carousel-item">
                    <?php if($ad->image): ?>
                        <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>">
                    <?php endif; ?>
                    <div class="fb-carousel-content">
                        <h6><?php echo e($ad->title); ?></h6>
                        <p><?php echo e(Str::limit($ad->content, 80)); ?></p>
                        <?php if($ad->link_url): ?>
                            <a href="<?php echo e($ad->link_url); ?>" target="_blank" onclick="trackAdClick(<?php echo e($ad->id); ?>)">
                                Learn More
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    
    <div class="fb-ad-actions">
        <button class="fb-ad-action" onclick="hideAd(<?php echo e($ad->id); ?>)">
            <i class="fas fa-times"></i>
            Hide Ad
        </button>
        <button class="fb-ad-action" onclick="reportAd(<?php echo e($ad->id); ?>)">
            <i class="fas fa-flag"></i>
            Report
        </button>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track ad view when it comes into viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                trackAdView(<?php echo e($ad->id); ?>);
                observer.unobserve(entry.target);
            }
        });
    });
    
    const adElement = document.querySelector('[data-ad-id="<?php echo e($ad->id); ?>"]');
    if (adElement) {
        observer.observe(adElement);
    }
});
</script>
<?php endif; ?>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/partials/sponsored_ad.blade.php ENDPATH**/ ?>