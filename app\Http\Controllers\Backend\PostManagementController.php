<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\NewsPost;
use App\Models\Category;

class PostManagementController extends Controller
{
    /**
     * Display post management dashboard
     */
    public function index()
    {
        // Get posts with optimized relationships
        $posts = NewsPost::with(['category', 'subcategory', 'user', 'pinnedBy'])
                        ->where('approval_status', 'approved')
                        ->ordered()
                        ->get();

        // Get statistics
        $pinnedCount = NewsPost::pinned()->where('approval_status', 'approved')->count();
        $featuredCount = NewsPost::featured()->where('approval_status', 'approved')->count();
        $trendingCount = NewsPost::trending()->where('approval_status', 'approved')->count();
        $totalPosts = NewsPost::where('approval_status', 'approved')->count();

        // Get categories for filtering
        $categories = Category::where('is_active', true)
                             ->orderBy('category_name')
                             ->get();

        return view('backend.post_management.index', compact(
            'posts',
            'pinnedCount',
            'featuredCount',
            'trendingCount',
            'totalPosts',
            'categories'
        ));
    }

    /**
     * Toggle pin status of a post
     */
    public function togglePin($id)
    {
        try {
            $post = NewsPost::findOrFail($id);

            // Check if post is approved
            if ($post->approval_status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only approved posts can be pinned.'
                ], 400);
            }

            $post->togglePin(auth()->id());

            // Refresh the model to get updated values
            $post->refresh();

            return response()->json([
                'success' => true,
                'is_pinned' => $post->is_pinned,
                'message' => $post->is_pinned ? 'Post pinned successfully!' : 'Post unpinned successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle pin status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle featured status of a post
     */
    public function toggleFeatured($id)
    {
        try {
            $post = NewsPost::findOrFail($id);

            // Check if post is approved
            if ($post->approval_status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only approved posts can be featured.'
                ], 400);
            }

            $post->update(['is_featured' => !$post->is_featured]);
            $post->refresh();

            return response()->json([
                'success' => true,
                'is_featured' => $post->is_featured,
                'message' => $post->is_featured ? 'Post marked as featured!' : 'Post removed from featured!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle featured status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle trending status of a post
     */
    public function toggleTrending($id)
    {
        try {
            $post = NewsPost::findOrFail($id);

            // Check if post is approved
            if ($post->approval_status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only approved posts can be trending.'
                ], 400);
            }

            $post->update(['is_trending' => !$post->is_trending]);
            $post->refresh();

            return response()->json([
                'success' => true,
                'is_trending' => $post->is_trending,
                'message' => $post->is_trending ? 'Post marked as trending!' : 'Post removed from trending!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle trending status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update display order of posts
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'posts' => 'required|array',
            'posts.*.id' => 'required|exists:news_posts,id',
            'posts.*.order' => 'required|integer|min:0'
        ]);

        foreach ($request->posts as $postData) {
            NewsPost::where('id', $postData['id'])
                   ->update(['display_order' => $postData['order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Post order updated successfully!'
        ]);
    }

    /**
     * Bulk actions for posts
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:pin,unpin,feature,unfeature,trending,untrending',
            'post_ids' => 'required|array',
            'post_ids.*' => 'exists:news_posts,id'
        ]);

        $posts = NewsPost::whereIn('id', $request->post_ids);

        switch ($request->action) {
            case 'pin':
                $posts->update([
                    'is_pinned' => true,
                    'pinned_at' => now(),
                    'pinned_by' => auth()->id()
                ]);
                $message = 'Posts pinned successfully!';
                break;

            case 'unpin':
                $posts->update([
                    'is_pinned' => false,
                    'pinned_at' => null,
                    'pinned_by' => null
                ]);
                $message = 'Posts unpinned successfully!';
                break;

            case 'feature':
                $posts->update(['is_featured' => true]);
                $message = 'Posts marked as featured!';
                break;

            case 'unfeature':
                $posts->update(['is_featured' => false]);
                $message = 'Posts removed from featured!';
                break;

            case 'trending':
                $posts->update(['is_trending' => true]);
                $message = 'Posts marked as trending!';
                break;

            case 'untrending':
                $posts->update(['is_trending' => false]);
                $message = 'Posts removed from trending!';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Test AJAX functionality
     */
    public function testAjax()
    {
        return response()->json([
            'success' => true,
            'message' => 'AJAX is working!',
            'timestamp' => now()
        ]);
    }
}
