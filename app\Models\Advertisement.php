<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Advertisement extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'target_pages' => 'array',
        'target_categories' => 'array',
    ];

    /**
     * Check if advertisement is currently active
     */
    public function isCurrentlyActive()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->toDateString();

        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Increment view count
     */
    public function incrementViews()
    {
        $this->increment('view_count');
    }

    /**
     * Increment click count
     */
    public function incrementClicks()
    {
        $this->increment('click_count');
        $this->updatePerformanceScore();
    }

    /**
     * Calculate and update performance score
     */
    public function updatePerformanceScore()
    {
        $views = $this->view_count ?? 0;
        $clicks = $this->click_count ?? 0;

        // Calculate CTR (Click Through Rate)
        $ctr = $views > 0 ? ($clicks / $views) * 100 : 0;

        // Calculate performance score (0-100)
        $performanceScore = min(100, ($ctr * 10) + ($views / 100));

        $this->update(['performance_score' => round($performanceScore, 2)]);
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics()
    {
        $views = $this->view_count ?? 0;
        $clicks = $this->click_count ?? 0;
        $ctr = $views > 0 ? ($clicks / $views) * 100 : 0;

        return [
            'views' => $views,
            'clicks' => $clicks,
            'ctr' => round($ctr, 2),
            'performance_score' => $this->performance_score ?? 0,
            'status' => $this->getPerformanceStatus()
        ];
    }

    /**
     * Get performance status based on metrics
     */
    public function getPerformanceStatus()
    {
        $score = $this->performance_score ?? 0;

        if ($score >= 80) return 'excellent';
        if ($score >= 60) return 'good';
        if ($score >= 40) return 'average';
        if ($score >= 20) return 'poor';
        return 'very_poor';
    }

    /**
     * Scope for active advertisements
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for advertisements by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope for advertisements by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('ad_type', $type);
    }

    /**
     * Scope for ordered advertisements
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc')->orderBy('created_at', 'desc');
    }
}
