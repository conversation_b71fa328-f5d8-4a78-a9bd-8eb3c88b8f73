<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.sponsored-ads.create')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Add Sponsored Ad
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Sponsored Ads Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-star-circle text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['total']); ?></h5>
                                <p class="text-muted mb-0">Total Sponsored Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['active']); ?></h5>
                                <p class="text-muted mb-0">Active Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-crown text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['premium']); ?></h5>
                                <p class="text-muted mb-0">Premium Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-newspaper text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['native']); ?></h5>
                                <p class="text-muted mb-0">Native Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Sponsored Ads</h4>
                        <p class="card-title-desc">Drag and drop to reorder sponsored ads, or use the action buttons to manage ads.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchSponsoredAds" placeholder="Search sponsored ads...">
                                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterFormat">
                                    <option value="">All Formats</option>
                                    <option value="native">Native</option>
                                    <option value="display">Display</option>
                                    <option value="video">Video</option>
                                    <option value="carousel">Carousel</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterPlacement">
                                    <option value="">All Placements</option>
                                    <option value="homepage_top">Homepage Top</option>
                                    <option value="homepage_middle">Homepage Middle</option>
                                    <option value="homepage_bottom">Homepage Bottom</option>
                                    <option value="category_top">Category Top</option>
                                    <option value="article_top">Article Top</option>
                                    <option value="sidebar">Sidebar</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                    <option value="premium">Premium Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="sponsoredAdsTable">
                                <thead>
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Sponsored Ad</th>
                                        <th>Sponsor</th>
                                        <th>Format</th>
                                        <th>Placement</th>
                                        <th>Status</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-sponsored-ads">
                                    <?php $__currentLoopData = $sponsoredAds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-ad-id="<?php echo e($ad->id); ?>" data-order="<?php echo e($ad->display_order); ?>" 
                                        class="sponsored-ad-row <?php echo e($ad->is_active ? '' : 'table-secondary'); ?> <?php echo e($ad->is_premium ? 'table-warning' : ''); ?>">
                                        <td>
                                            <span class="badge bg-secondary order-badge"><?php echo e($ad->display_order); ?></span>
                                            <i class="mdi mdi-drag-vertical text-muted ms-2" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($ad->image): ?>
                                                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" 
                                                     class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e(Str::limit($ad->title, 40)); ?></h6>
                                                    <small class="text-muted"><?php echo e($ad->created_at->format('M d, Y')); ?></small>
                                                    <?php if($ad->is_premium): ?>
                                                        <span class="badge bg-warning ms-2">
                                                            <i class="mdi mdi-crown"></i> Premium
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($ad->sponsor_logo): ?>
                                                <img src="<?php echo e(asset($ad->sponsor_logo)); ?>" alt="<?php echo e($ad->sponsor_name); ?>" 
                                                     class="rounded me-2" width="30" height="30" style="object-fit: cover;">
                                                <?php endif; ?>
                                                <span><?php echo e($ad->sponsor_name); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e(ucfirst($ad->ad_format)); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' ', $ad->placement))); ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input status-toggle" type="checkbox" 
                                                           data-ad-id="<?php echo e($ad->id); ?>" <?php echo e($ad->is_active ? 'checked' : ''); ?>>
                                                    <label class="form-check-label">
                                                        <?php echo e($ad->is_active ? 'Active' : 'Inactive'); ?>

                                                    </label>
                                                </div>
                                                <button class="btn btn-sm <?php echo e($ad->is_premium ? 'btn-warning' : 'btn-outline-warning'); ?> premium-btn" 
                                                        data-ad-id="<?php echo e($ad->id); ?>" title="<?php echo e($ad->is_premium ? 'Remove Premium' : 'Mark Premium'); ?>">
                                                    <i class="mdi mdi-crown"></i> <?php echo e($ad->is_premium ? 'Premium' : 'Standard'); ?>

                                                </button>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <small><strong>Views:</strong> <?php echo e(number_format($ad->view_count)); ?></small>
                                                <small><strong>Clicks:</strong> <?php echo e(number_format($ad->click_count)); ?></small>
                                                <?php if($ad->view_count > 0): ?>
                                                <small><strong>CTR:</strong> <?php echo e($ad->click_through_rate); ?>%</small>
                                                <?php endif; ?>
                                                <?php if($ad->budget): ?>
                                                <small><strong>Budget:</strong> $<?php echo e(number_format($ad->budget, 2)); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- View Button -->
                                                <a href="<?php echo e(route('admin.sponsored-ads.show', $ad->id)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                
                                                <!-- Edit Button -->
                                                <a href="<?php echo e(route('admin.sponsored-ads.edit', $ad->id)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                                
                                                <!-- Delete Button -->
                                                <form method="POST" action="<?php echo e(route('admin.sponsored-ads.destroy', $ad->id)); ?>" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this sponsored ad?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Showing <?php echo e($sponsoredAds->firstItem()); ?> to <?php echo e($sponsoredAds->lastItem()); ?> of <?php echo e($sponsoredAds->total()); ?> sponsored ads
                            </div>
                            <div>
                                <?php echo e($sponsoredAds->links()); ?>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sponsored-ad-row.ui-sortable-helper {
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sponsored-ad-row.table-secondary {
    opacity: 0.7;
}

.sponsored-ad-row.table-warning {
    background-color: #fff3cd !important;
}

.order-badge {
    min-width: 30px;
    display: inline-block;
    text-align: center;
}

#sortable-sponsored-ads .mdi-drag-vertical {
    opacity: 0.5;
    transition: opacity 0.2s;
}

#sortable-sponsored-ads tr:hover .mdi-drag-vertical {
    opacity: 1;
}
</style>

<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">

<script>
$(document).ready(function() {
    // Initialize toastr if not already initialized
    if (typeof toastr !== 'undefined') {
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };
    }

    // Make table rows sortable
    $("#sortable-sponsored-ads").sortable({
        handle: ".mdi-drag-vertical",
        helper: function(e, tr) {
            var $originals = tr.children();
            var $helper = tr.clone();
            $helper.children().each(function(index) {
                $(this).width($originals.eq(index).width());
            });
            return $helper;
        },
        update: function(event, ui) {
            updateSponsoredAdOrder();
        },
        placeholder: "ui-state-highlight",
        cursor: "move"
    });

    // Update sponsored ad order after drag and drop
    function updateSponsoredAdOrder() {
        var ads = [];
        $("#sortable-sponsored-ads tr").each(function(index) {
            var adId = $(this).data('ad-id');
            if (adId) {
                ads.push({
                    id: adId,
                    order: index
                });
                // Update the order badge
                $(this).find('.order-badge').text(index);
            }
        });

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '<?php echo e(route("admin.sponsored-ads.update-order")); ?>',
            method: 'POST',
            data: {
                sponsored_ads: ads,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            beforeSend: function() {
                $('.order-badge').addClass('bg-warning').removeClass('bg-secondary');
            },
            success: function(response) {
                if (response.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                    $('.order-badge').addClass('bg-secondary').removeClass('bg-warning');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to update sponsored ad order: ' + error);
                } else {
                    alert('Failed to update sponsored ad order');
                }
                location.reload(); // Reload to restore original order
            }
        });
    }

    // Status toggle functionality
    $(document).on('change', '.status-toggle', function() {
        var adId = $(this).data('ad-id');
        var isActive = $(this).is(':checked');
        var label = $(this).next('label');
        var toggle = $(this);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '<?php echo e(route("admin.sponsored-ads.toggle-status", ":id")); ?>'.replace(':id', adId),
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            beforeSend: function() {
                toggle.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    label.text(response.is_active ? 'Active' : 'Inactive');
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                }
                toggle.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('Status toggle error:', xhr.responseText);
                // Revert the toggle
                toggle.prop('checked', !isActive);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to toggle sponsored ad status');
                } else {
                    alert('Failed to toggle sponsored ad status');
                }
                toggle.prop('disabled', false);
            }
        });
    });

    // Premium toggle functionality
    $(document).on('click', '.premium-btn', function(e) {
        e.preventDefault();
        var adId = $(this).data('ad-id');
        var btn = $(this);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '<?php echo e(route("admin.sponsored-ads.toggle-premium", ":id")); ?>'.replace(':id', adId),
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            beforeSend: function() {
                btn.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    // Update button appearance
                    if (response.is_premium) {
                        btn.removeClass('btn-outline-warning').addClass('btn-warning');
                        btn.attr('title', 'Remove Premium');
                        btn.html('<i class="mdi mdi-crown"></i> Premium');
                    } else {
                        btn.removeClass('btn-warning').addClass('btn-outline-warning');
                        btn.attr('title', 'Mark Premium');
                        btn.html('<i class="mdi mdi-crown"></i> Standard');
                    }

                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                }
                btn.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('Premium toggle error:', xhr.responseText);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to toggle premium status');
                } else {
                    alert('Failed to toggle premium status');
                }
                btn.prop('disabled', false);
            }
        });
    });

    // Search functionality
    $('#searchSponsoredAds').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('#sponsoredAdsTable tbody tr').each(function() {
            var title = $(this).find('h6').text().toLowerCase();
            var sponsor = $(this).find('td:nth-child(3)').text().toLowerCase();

            if (title.includes(searchTerm) || sponsor.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Filter functionality
    $('#filterFormat, #filterPlacement, #filterStatus').change(function() {
        var formatFilter = $('#filterFormat').val();
        var placementFilter = $('#filterPlacement').val();
        var statusFilter = $('#filterStatus').val();

        $('#sponsoredAdsTable tbody tr').each(function() {
            var show = true;

            if (formatFilter) {
                var adFormat = $(this).find('.badge.bg-primary').text().toLowerCase();
                show = show && adFormat.includes(formatFilter);
            }

            if (placementFilter) {
                var placement = $(this).find('.badge.bg-info').text().toLowerCase().replace(/\s+/g, '_');
                show = show && placement.includes(placementFilter);
            }

            if (statusFilter) {
                var isActive = $(this).find('.status-toggle').is(':checked');
                var isPremium = $(this).hasClass('table-warning');

                if (statusFilter === 'active') {
                    show = show && isActive;
                } else if (statusFilter === 'inactive') {
                    show = show && !isActive;
                } else if (statusFilter === 'premium') {
                    show = show && isPremium;
                }
            }

            if (show) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/sponsored_ads/index.blade.php ENDPATH**/ ?>