@extends('frontend.layout_facebook_style')

@section('title')
{{ $category->category_name }} - <PERSON><PERSON><PERSON><PERSON><PERSON> (Facebook Style)
@endsection

@section('content')

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                @php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                @endphp
                <img src="{{ $logoPath }}" alt="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}">
            </div>
            <div class="fb-profile-info">
                <h3 class="fb-profile-name">NitiKotha</h3>
                <p class="fb-profile-tagline">Premium News Portal</p>
            </div>
        </div>

        <!-- Quick Navigation -->
        <ul class="fb-nav-links">
            <li><a href="{{ url('/v2') }}">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a></li>
            <li><a href="#" class="active">
                <i class="fas fa-fire"></i>
                <span>{{ $category->category_name }}</span>
            </a></li>
            <li><a href="#">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a></li>
            <li><a href="#">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a></li>
            <li><a href="#">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a></li>
        </ul>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                @if(isset($categories))
                    @foreach($categories as $cat)
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category {{ $cat->id == $category->id ? 'active' : '' }}" data-category-id="{{ $cat->id }}">
                            <div class="fb-category-left">
                                <div class="fb-category-icon">{{ $cat->category_icon }}</div>
                                <span class="fb-category-name">{{ $cat->category_name }}</span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count">{{ $cat->news_posts_count ?? 0 }}</span>
                                @if($cat->subcategories && $cat->subcategories->count() > 0)
                                    <button class="fb-category-toggle" data-target="subcats-{{ $cat->id }}">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="{{ url('v2/news/category/'.$cat->id.'/'.$cat->category_slug) }}" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        @if($cat->subcategories && $cat->subcategories->count() > 0)
                        <div class="fb-subcategories {{ $cat->id == $category->id ? 'expanded' : '' }}" id="subcats-{{ $cat->id }}">
                            @foreach($cat->subcategories as $subcategory)
                            <a href="{{ url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug) }}" class="fb-subcategory-item">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name">{{ $subcategory->subcategory_name }}</span>
                                <span class="fb-subcategory-count">{{ $subcategory->news_posts_count ?? 0 }}</span>
                            </a>
                            @endforeach
                        </div>
                        @endif
                    </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- Category Header -->
        <div class="fb-category-header">
            <div class="fb-category-cover">
                @php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                @endphp
                <img src="{{ asset('frontend/assets/images/category-cover.jpg') }}" alt="{{ $category->category_name }}" onerror="this.src='{{ $logoPath }}'">
                <div class="fb-category-overlay"></div>
                <div class="fb-category-info">
                    <h1 class="fb-category-title">{{ $category->category_name }}</h1>
                    <p class="fb-category-description">Latest news and updates from {{ $category->category_name }}</p>
                    <div class="fb-category-stats">
                        <span class="fb-category-posts">{{ $catnews->total() }} Posts</span>
                        <span class="fb-category-followers">{{ rand(100, 5000) }} Followers</span>
                    </div>
                </div>
            </div>
            
            <!-- Category Actions -->
            <div class="fb-category-actions">
                <button class="fb-follow-btn">
                    <i class="fas fa-plus"></i>
                    <span>Follow</span>
                </button>
                <button class="fb-share-btn">
                    <i class="fas fa-share"></i>
                    <span>Share</span>
                </button>
                <button class="fb-more-btn">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="fb-filter-tabs">
            <button class="fb-filter-tab active" data-filter="all">
                <i class="fas fa-newspaper"></i>
                <span>All Posts</span>
            </button>
            <button class="fb-filter-tab" data-filter="recent">
                <i class="fas fa-clock"></i>
                <span>Recent</span>
            </button>
            <button class="fb-filter-tab" data-filter="popular">
                <i class="fas fa-fire"></i>
                <span>Popular</span>
            </button>
            <button class="fb-filter-tab" data-filter="trending">
                <i class="fas fa-trending-up"></i>
                <span>Trending</span>
            </button>
        </div>

        <!-- Top Category Advertisements -->
        @if(isset($sponsoredAds['category_top']) && $sponsoredAds['category_top']->count() > 0)
            @foreach($sponsoredAds['category_top'] as $ad)
                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'top'])
            @endforeach
        @endif

        <!-- Regular Banner Advertisements (Top) -->
        @if(isset($advertisements))
            @foreach($advertisements->where('ad_type', 'banner')->where('position', 'top') as $ad)
                @include('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'top'])
            @endforeach
        @endif

        <!-- News Feed Posts -->
        <div class="fb-news-feed">
            @if($catnews->count() > 0)
                @foreach($catnews as $index => $news)

                    <!-- Insert Middle Advertisement after 3rd post -->
                    @if($index == 3)
                        @if(isset($sponsoredAds['category_middle']) && $sponsoredAds['category_middle']->count() > 0)
                            @foreach($sponsoredAds['category_middle'] as $ad)
                                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'middle'])
                            @endforeach
                        @endif

                        <!-- Regular Inline Advertisements (Middle) -->
                        @if(isset($advertisements))
                            @foreach($advertisements->where('ad_type', 'inline')->where('position', 'middle') as $ad)
                                @include('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'middle'])
                            @endforeach
                        @endif
                    @endif
                <article class="fb-post">
                    <!-- Post Header -->
                    <div class="fb-post-header">
                        <div class="fb-post-avatar">
                            @php
                                $photoPath = 'upload/no_image.jpg';
                                if (!empty($news->user->photo)) {
                                    // Check if photo already contains path (starts with 'upload/')
                                    if (str_starts_with($news->user->photo, 'upload/')) {
                                        // Photo already contains full path (new format)
                                        $photoPath = $news->user->photo;
                                    } else {
                                        // Photo contains only filename (old format) - assume admin
                                        $photoPath = 'upload/admin_images/' . $news->user->photo;
                                    }
                                }
                            @endphp
                            <img src="{{ url($photoPath) }}" alt="{{ $news->user->name }}">
                        </div>
                        <div class="fb-post-info">
                            <h4 class="fb-post-author">{{ $news->user->name ?? 'NitiKotha' }}</h4>
                            <div class="fb-post-meta">
                                <span class="fb-post-time">{{ $news->created_at->diffForHumans() }}</span>
                                <span class="fb-post-separator">·</span>
                                <span class="fb-post-category">{{ $news->category->category_name }}</span>
                                <span class="fb-post-separator">·</span>
                                <i class="fas fa-globe-americas"></i>
                            </div>
                        </div>
                        <div class="fb-post-options">
                            <button class="fb-options-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="fb-post-content">
                        <h3 class="fb-post-title">
                            <a href="{{ url('v2/news/details/'.$news->id.'/'.$news->news_title_slug) }}">
                                {{ $news->news_title }}
                            </a>
                        </h3>
                        <p class="fb-post-excerpt">
                            {{ Str::limit(strip_tags($news->news_details), 200) }}
                        </p>
                    </div>

                    <!-- Post Image -->
                    @if($news->image)
                    <div class="fb-post-image">
                        <img src="{{ asset($news->image) }}" alt="{{ $news->news_title }}">
                        <a href="{{ url('v2/news/details/'.$news->id.'/'.$news->news_title_slug) }}" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    @endif

                    <!-- Post Stats -->
                    <div class="fb-post-stats">
                        <div class="fb-post-reactions">
                            <div class="fb-reaction-icons">
                                <span class="fb-reaction like">👍</span>
                                <span class="fb-reaction love">❤️</span>
                                <span class="fb-reaction wow">😮</span>
                            </div>
                            <span class="fb-reaction-count" id="likes-count-{{ $news->id }}">{{ $news->likes_count ?? 0 }}</span>
                        </div>
                        <div class="fb-post-engagement">
                            <span class="fb-comments-count" id="comments-count-{{ $news->id }}">{{ $news->comments_count ?? 0 }} comments</span>
                            <span class="fb-shares-count" id="shares-count-{{ $news->id }}">{{ $news->shares_count ?? 0 }} shares</span>
                        </div>
                    </div>

                    <!-- Post Actions -->
                    <div class="fb-post-actions">
                        <button class="fb-action-btn fb-like-btn {{ $news->user_liked ? 'liked' : '' }}" data-news-id="{{ $news->id }}">
                            <i class="fas fa-thumbs-up"></i>
                            <span>{{ $news->user_liked ? 'Liked' : 'Like' }}</span>
                        </button>
                        <button class="fb-action-btn fb-comment-btn" data-news-id="{{ $news->id }}">
                            <i class="fas fa-comment"></i>
                            <span>Comment</span>
                        </button>
                        <button class="fb-action-btn fb-share-btn" data-news-id="{{ $news->id }}">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                        <button class="fb-action-btn fb-save-btn {{ $news->user_saved ? 'saved' : '' }}" data-news-id="{{ $news->id }}">
                            <i class="fas fa-bookmark"></i>
                            <span>{{ $news->user_saved ? 'Saved' : 'Save' }}</span>
                        </button>
                    </div>
                </article>
                @endforeach
            @else
                <div class="fb-no-posts">
                    <div class="fb-no-posts-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3 class="fb-no-posts-title">No posts yet</h3>
                    <p class="fb-no-posts-text">There are no posts in this category yet. Check back later for updates!</p>
                </div>
            @endif
        </div>

        <!-- Pagination -->
        @if($catnews->hasPages())
        <div class="fb-pagination">
            {{ $catnews->links() }}
        </div>
        @endif

        <!-- Bottom Category Advertisements -->
        @if(isset($sponsoredAds['category_bottom']) && $sponsoredAds['category_bottom']->count() > 0)
            @foreach($sponsoredAds['category_bottom'] as $ad)
                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'bottom'])
            @endforeach
        @endif

        <!-- Regular Banner Advertisements (Bottom) -->
        @if(isset($advertisements))
            @foreach($advertisements->where('ad_type', 'banner')->where('position', 'bottom') as $ad)
                @include('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'bottom'])
            @endforeach
        @endif
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Sidebar Advertisements -->
        @if(isset($sponsoredAds['sidebar']) && $sponsoredAds['sidebar']->count() > 0)
            @foreach($sponsoredAds['sidebar'] as $ad)
                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'sidebar'])
            @endforeach
        @endif

        <!-- Regular Sidebar Advertisements -->
        @if(isset($advertisements))
            @foreach($advertisements->where('ad_type', 'sidebar')->where('position', 'right') as $ad)
                @include('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'sidebar'])
            @endforeach
        @endif

        <!-- Popular in Category -->
        <div class="fb-popular-section">
            <h4 class="fb-section-title">Popular in {{ $category->category_name }}</h4>
            <div class="fb-popular-list">
                @if(isset($newspopular))
                    @foreach($newspopular->take(5) as $index => $popular)
                    <div class="fb-popular-item">
                        <div class="fb-popular-rank">{{ $index + 1 }}</div>
                        <div class="fb-popular-image">
                            <img src="{{ asset($popular->image) }}" alt="{{ $popular->news_title }}">
                        </div>
                        <div class="fb-popular-content">
                            <h5 class="fb-popular-title">
                                <a href="{{ url('v2/news/details/'.$popular->id.'/'.$popular->news_title_slug) }}">
                                    {{ Str::limit($popular->news_title, 60) }}
                                </a>
                            </h5>
                            <p class="fb-popular-meta">{{ $popular->view_count ?? 0 }} views</p>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Related Categories -->
        <div class="fb-related-categories">
            <h4 class="fb-section-title">Related Categories</h4>
            <div class="fb-related-list">
                @if(isset($categories))
                    @foreach($categories->where('id', '!=', $category->id)->take(6) as $related)
                    <a href="{{ url('v2/news/category/'.$related->id.'/'.$related->category_slug) }}" class="fb-related-item">
                        <div class="fb-related-icon">{{ $related->category_icon }}</div>
                        <div class="fb-related-info">
                            <h5 class="fb-related-name">{{ $related->category_name }}</h5>
                            <p class="fb-related-count">{{ $related->news_posts_count ?? 0 }} posts</p>
                        </div>
                    </a>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Suggested for You -->
        <div class="fb-suggested-section">
            <h4 class="fb-section-title">Suggested for You</h4>
            <div class="fb-suggested-list">
                @if(isset($newnewspost))
                    @foreach($newnewspost->take(4) as $suggested)
                    <div class="fb-suggested-item">
                        <div class="fb-suggested-image">
                            <img src="{{ asset($suggested->image) }}" alt="{{ $suggested->news_title }}">
                        </div>
                        <div class="fb-suggested-content">
                            <h5 class="fb-suggested-title">
                                <a href="{{ url('v2/news/details/'.$suggested->id.'/'.$suggested->news_title_slug) }}">
                                    {{ Str::limit($suggested->news_title, 50) }}
                                </a>
                            </h5>
                            <p class="fb-suggested-meta">{{ $suggested->created_at->format('M d') }}</p>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>

@endsection
