<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active">Comment Management</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Comment Management</h4>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="card widget-flat">
                    <div class="card-body">
                        <div class="float-end">
                            <i class="mdi mdi-comment-multiple widget-icon bg-primary"></i>
                        </div>
                        <h5 class="text-muted font-weight-normal mt-0" title="Total Comments">Total Comments</h5>
                        <h3 class="mt-3 mb-3"><?php echo e($totalComments); ?></h3>
                        <p class="mb-0 text-muted">
                            <span class="text-nowrap">All time comments</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card widget-flat">
                    <div class="card-body">
                        <div class="float-end">
                            <i class="mdi mdi-clock-outline widget-icon bg-warning"></i>
                        </div>
                        <h5 class="text-muted font-weight-normal mt-0" title="Pending Approval">Pending Approval</h5>
                        <h3 class="mt-3 mb-3"><?php echo e($pendingComments); ?></h3>
                        <p class="mb-0 text-muted">
                            <span class="text-nowrap">Awaiting moderation</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card widget-flat">
                    <div class="card-body">
                        <div class="float-end">
                            <i class="mdi mdi-check-circle widget-icon bg-success"></i>
                        </div>
                        <h5 class="text-muted font-weight-normal mt-0" title="Approved">Approved</h5>
                        <h3 class="mt-3 mb-3"><?php echo e($approvedComments); ?></h3>
                        <p class="mb-0 text-muted">
                            <span class="text-nowrap">Published comments</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card widget-flat">
                    <div class="card-body">
                        <div class="float-end">
                            <i class="mdi mdi-close-circle widget-icon bg-danger"></i>
                        </div>
                        <h5 class="text-muted font-weight-normal mt-0" title="Rejected">Rejected</h5>
                        <h3 class="mt-3 mb-3"><?php echo e($totalComments - $approvedComments); ?></h3>
                        <p class="mb-0 text-muted">
                            <span class="text-nowrap">Rejected comments</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">Filter Comments</h4>
                        <form method="GET" action="<?php echo e(route('admin.comment-management')); ?>">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="">All Comments</option>
                                            <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                            <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Search</label>
                                        <input type="text" name="search" class="form-control" placeholder="Search comments, users, or posts..." value="<?php echo e(request('search')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary w-100">Filter</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="select-all">
                                    <label class="form-check-label" for="select-all">Select All</label>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                                        <i class="mdi mdi-check"></i> Approve Selected
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('reject')">
                                        <i class="mdi mdi-close"></i> Reject Selected
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                                        <i class="mdi mdi-delete"></i> Delete Selected
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comments Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title">Comments List</h4>
                        <div class="table-responsive">
                            <table class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input">
                                </th>
                                <th>Comment</th>
                                <th>Author</th>
                                <th>Post</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="comment-checkbox" value="<?php echo e($comment->id); ?>">
                                </td>
                                <td>
                                    <div class="comment-preview">
                                        <?php echo e(Str::limit($comment->comment, 100)); ?>

                                    </div>
                                </td>
                                <td>
                                    <?php if($comment->user): ?>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo e(asset($comment->user->photo ?? 'upload/no_image.jpg')); ?>" 
                                                 alt="<?php echo e($comment->user->name); ?>" 
                                                 class="rounded-circle mg-r-10" 
                                                 width="30" height="30">
                                            <div>
                                                <strong><?php echo e($comment->user->name); ?></strong><br>
                                                <small class="text-muted"><?php echo e($comment->user->email); ?></small>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div>
                                            <strong><?php echo e($comment->guest_name ?? 'Anonymous'); ?></strong><br>
                                            <small class="text-muted"><?php echo e($comment->guest_email ?? 'No email'); ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($comment->newsPost): ?>
                                        <a href="<?php echo e(url('v2/news/details/'.$comment->newsPost->id.'/'.$comment->newsPost->news_title_slug)); ?>" 
                                           target="_blank" class="text-primary">
                                            <?php echo e(Str::limit($comment->newsPost->news_title, 50)); ?>

                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Post not found</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($comment->is_approved): ?>
                                        <span class="badge badge-success">Approved</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Pending</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo e($comment->created_at->format('M d, Y')); ?><br><?php echo e($comment->created_at->format('h:i A')); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if(!$comment->is_approved): ?>
                                            <button type="button" class="btn btn-success" onclick="approveComment(<?php echo e($comment->id); ?>)">
                                                <i class="fa fa-check"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-warning" onclick="rejectComment(<?php echo e($comment->id); ?>)">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-info" onclick="viewComment(<?php echo e($comment->id); ?>)">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteComment(<?php echo e($comment->id); ?>)">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="icon ion-ios-chatboxes-outline tx-60"></i>
                                        <p class="mg-t-10">No comments found</p>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
                        <?php if($comments->hasPages()): ?>
                        <div class="d-flex justify-content-end mt-3">
                            <?php echo e($comments->links()); ?>

                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- container -->
</div>
<!-- content -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.comment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Additional select all functionality can be added here if needed

// Individual comment actions
function approveComment(id) {
    if (confirm('Are you sure you want to approve this comment?')) {
        fetch(`/admin/comment-management/${id}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function rejectComment(id) {
    if (confirm('Are you sure you want to reject this comment?')) {
        fetch(`/admin/comment-management/${id}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function deleteComment(id) {
    if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
        fetch(`/admin/comment-management/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

// Bulk actions
function bulkAction(action) {
    const selectedComments = Array.from(document.querySelectorAll('.comment-checkbox:checked')).map(cb => cb.value);
    
    if (selectedComments.length === 0) {
        alert('Please select at least one comment');
        return;
    }

    const actionText = action === 'approve' ? 'approve' : action === 'reject' ? 'reject' : 'delete';
    if (confirm(`Are you sure you want to ${actionText} ${selectedComments.length} comment(s)?`)) {
        fetch('/admin/comment-management/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                comment_ids: selectedComments
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function viewComment(id) {
    window.open(`/admin/comment-management/${id}/show`, '_blank');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/comment_management/index.blade.php ENDPATH**/ ?>