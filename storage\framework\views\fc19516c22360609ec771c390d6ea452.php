<?php $__env->startSection('body-content'); ?>
<div class="br-pagetitle">
    <i class="icon ion-ios-home-outline"></i>
    <div>
        <h4>Comment Management</h4>
        <p class="mg-b-0">Manage and moderate user comments</p>
    </div>
</div>

<div class="br-pagebody">
    <div class="br-section-wrapper">
        
        <!-- Stats Cards -->
        <div class="row mg-b-25">
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-chatboxes-outline tx-primary"></i>
                        <div class="media-body">
                            <h6>Total Comments</h6>
                            <h4 class="tx-primary"><?php echo e($totalComments); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-clock-outline tx-warning"></i>
                        <div class="media-body">
                            <h6>Pending Approval</h6>
                            <h4 class="tx-warning"><?php echo e($pendingComments); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-checkmark-outline tx-success"></i>
                        <div class="media-body">
                            <h6>Approved</h6>
                            <h4 class="tx-success"><?php echo e($approvedComments); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-close-outline tx-danger"></i>
                        <div class="media-body">
                            <h6>Rejected</h6>
                            <h4 class="tx-danger"><?php echo e($totalComments - $approvedComments); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mg-b-25">
            <div class="card-header">
                <h6 class="card-title mg-b-0">Filter Comments</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.comment-management')); ?>">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" class="form-control">
                                    <option value="">All Comments</option>
                                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                                    <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Search comments, users, or posts..." value="<?php echo e(request('search')); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">Filter</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="card mg-b-25">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-group mg-b-0">
                            <label class="ckbox">
                                <input type="checkbox" id="select-all">
                                <span>Select All</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                                <i class="fa fa-check"></i> Approve Selected
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('reject')">
                                <i class="fa fa-times"></i> Reject Selected
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                                <i class="fa fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comments Table -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mg-b-0">Comments List</h6>
            </div>
            <div class="card-body pd-0">
                <div class="table-responsive">
                    <table class="table table-striped mg-b-0">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="select-all-header">
                                </th>
                                <th>Comment</th>
                                <th>Author</th>
                                <th>Post</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="comment-checkbox" value="<?php echo e($comment->id); ?>">
                                </td>
                                <td>
                                    <div class="comment-preview">
                                        <?php echo e(Str::limit($comment->comment, 100)); ?>

                                    </div>
                                </td>
                                <td>
                                    <?php if($comment->user): ?>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo e(asset($comment->user->photo ?? 'upload/no_image.jpg')); ?>" 
                                                 alt="<?php echo e($comment->user->name); ?>" 
                                                 class="rounded-circle mg-r-10" 
                                                 width="30" height="30">
                                            <div>
                                                <strong><?php echo e($comment->user->name); ?></strong><br>
                                                <small class="text-muted"><?php echo e($comment->user->email); ?></small>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div>
                                            <strong><?php echo e($comment->guest_name ?? 'Anonymous'); ?></strong><br>
                                            <small class="text-muted"><?php echo e($comment->guest_email ?? 'No email'); ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($comment->newsPost): ?>
                                        <a href="<?php echo e(url('v2/news/details/'.$comment->newsPost->id.'/'.$comment->newsPost->news_title_slug)); ?>" 
                                           target="_blank" class="text-primary">
                                            <?php echo e(Str::limit($comment->newsPost->news_title, 50)); ?>

                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Post not found</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($comment->is_approved): ?>
                                        <span class="badge badge-success">Approved</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Pending</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo e($comment->created_at->format('M d, Y')); ?><br><?php echo e($comment->created_at->format('h:i A')); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if(!$comment->is_approved): ?>
                                            <button type="button" class="btn btn-success" onclick="approveComment(<?php echo e($comment->id); ?>)">
                                                <i class="fa fa-check"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-warning" onclick="rejectComment(<?php echo e($comment->id); ?>)">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-info" onclick="viewComment(<?php echo e($comment->id); ?>)">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteComment(<?php echo e($comment->id); ?>)">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="icon ion-ios-chatboxes-outline tx-60"></i>
                                        <p class="mg-t-10">No comments found</p>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if($comments->hasPages()): ?>
            <div class="card-footer">
                <?php echo e($comments->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.comment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('select-all-header').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.comment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Individual comment actions
function approveComment(id) {
    if (confirm('Are you sure you want to approve this comment?')) {
        fetch(`/admin/comment-management/${id}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function rejectComment(id) {
    if (confirm('Are you sure you want to reject this comment?')) {
        fetch(`/admin/comment-management/${id}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function deleteComment(id) {
    if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
        fetch(`/admin/comment-management/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

// Bulk actions
function bulkAction(action) {
    const selectedComments = Array.from(document.querySelectorAll('.comment-checkbox:checked')).map(cb => cb.value);
    
    if (selectedComments.length === 0) {
        alert('Please select at least one comment');
        return;
    }

    const actionText = action === 'approve' ? 'approve' : action === 'reject' ? 'reject' : 'delete';
    if (confirm(`Are you sure you want to ${actionText} ${selectedComments.length} comment(s)?`)) {
        fetch('/admin/comment-management/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                comment_ids: selectedComments
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function viewComment(id) {
    window.open(`/admin/comment-management/${id}/show`, '_blank');
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layout.template', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/comment_management/index.blade.php ENDPATH**/ ?>