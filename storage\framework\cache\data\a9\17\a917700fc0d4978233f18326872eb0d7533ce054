1752573644O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{s:8:"branding";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:5;s:3:"key";s:9:"site_logo";s:5:"value";s:45:"upload/site_settings/site_logo_1752525494.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:9:"Site Logo";s:11:"description";s:49:"Main logo for the website (recommended: 200x50px)";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 20:38:14";}s:11:" * original";a:11:{s:2:"id";i:5;s:3:"key";s:9:"site_logo";s:5:"value";s:45:"upload/site_settings/site_logo_1752525494.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:9:"Site Logo";s:11:"description";s:49:"Main logo for the website (recommended: 200x50px)";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 20:38:14";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:18;s:3:"key";s:14:"site_logo_dark";s:5:"value";s:35:"backend/assets/images/logo-dark.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:9:"Dark Logo";s:11:"description";s:46:"Dark version of the logo for light backgrounds";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 19:28:05";s:10:"updated_at";s:19:"2025-07-14 19:28:05";}s:11:" * original";a:11:{s:2:"id";i:18;s:3:"key";s:14:"site_logo_dark";s:5:"value";s:35:"backend/assets/images/logo-dark.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:9:"Dark Logo";s:11:"description";s:46:"Dark version of the logo for light backgrounds";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 19:28:05";s:10:"updated_at";s:19:"2025-07-14 19:28:05";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:15;s:3:"key";s:12:"site_logo_sm";s:5:"value";s:48:"upload/site_settings/site_logo_sm_1752570043.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:10:"Small Logo";s:11:"description";s:57:"Small logo for mobile/compact view (recommended: 40x40px)";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-15 09:00:43";}s:11:" * original";a:11:{s:2:"id";i:15;s:3:"key";s:12:"site_logo_sm";s:5:"value";s:48:"upload/site_settings/site_logo_sm_1752570043.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:10:"Small Logo";s:11:"description";s:57:"Small logo for mobile/compact view (recommended: 40x40px)";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-15 09:00:43";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:6;s:3:"key";s:12:"site_favicon";s:5:"value";s:48:"upload/site_settings/site_favicon_1752570043.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:12:"Site Favicon";s:11:"description";s:46:"Favicon for the website (recommended: 32x32px)";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-15 09:00:43";}s:11:" * original";a:11:{s:2:"id";i:6;s:3:"key";s:12:"site_favicon";s:5:"value";s:48:"upload/site_settings/site_favicon_1752570043.png";s:4:"type";s:5:"image";s:5:"group";s:8:"branding";s:5:"label";s:12:"Site Favicon";s:11:"description";s:46:"Favicon for the website (recommended: 32x32px)";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-15 09:00:43";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:7:"contact";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:3:{i:0;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:8;s:3:"key";s:13:"contact_phone";s:5:"value";s:11:"01736990123";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:13:"Contact Phone";s:11:"description";s:28:"Primary contact phone number";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:8;s:3:"key";s:13:"contact_phone";s:5:"value";s:11:"01736990123";s:4:"type";s:4:"text";s:5:"group";s:7:"contact";s:5:"label";s:13:"Contact Phone";s:11:"description";s:28:"Primary contact phone number";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:7;s:3:"key";s:13:"contact_email";s:5:"value";s:18:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:13:"Contact Email";s:11:"description";s:29:"Primary contact email address";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:7;s:3:"key";s:13:"contact_email";s:5:"value";s:18:"<EMAIL>";s:4:"type";s:5:"email";s:5:"group";s:7:"contact";s:5:"label";s:13:"Contact Email";s:11:"description";s:29:"Primary contact email address";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:9;s:3:"key";s:15:"contact_address";s:5:"value";s:45:"Road -15, House -18, sector -12, Uttara Dhaka";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:15:"Contact Address";s:11:"description";s:36:"Physical address of the organization";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:9;s:3:"key";s:15:"contact_address";s:5:"value";s:45:"Road -15, House -18, sector -12, Uttara Dhaka";s:4:"type";s:8:"textarea";s:5:"group";s:7:"contact";s:5:"label";s:15:"Contact Address";s:11:"description";s:36:"Physical address of the organization";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:7:"general";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:3:"key";s:9:"site_name";s:5:"value";s:9:"NitiKotha";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:9:"Site Name";s:11:"description";s:24:"The name of your website";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:33";s:10:"updated_at";s:19:"2025-07-13 20:29:15";}s:11:" * original";a:11:{s:2:"id";i:1;s:3:"key";s:9:"site_name";s:5:"value";s:9:"NitiKotha";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:9:"Site Name";s:11:"description";s:24:"The name of your website";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:33";s:10:"updated_at";s:19:"2025-07-13 20:29:15";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:14;s:3:"key";s:12:"site_tagline";s:5:"value";s:24:"Your Trusted News Source";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:12:"Site Tagline";s:11:"description";s:35:"A short description of your website";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:14;s:3:"key";s:12:"site_tagline";s:5:"value";s:24:"Your Trusted News Source";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:12:"Site Tagline";s:11:"description";s:35:"A short description of your website";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:2;s:3:"key";s:10:"site_title";s:5:"value";s:33:"NitiKotha - Latest Blog & Updates";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:10:"Site Title";s:11:"description";s:38:"The title that appears in browser tabs";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:33";s:10:"updated_at";s:19:"2025-07-13 19:45:36";}s:11:" * original";a:11:{s:2:"id";i:2;s:3:"key";s:10:"site_title";s:5:"value";s:33:"NitiKotha - Latest Blog & Updates";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:10:"Site Title";s:11:"description";s:38:"The title that appears in browser tabs";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:33";s:10:"updated_at";s:19:"2025-07-13 19:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:3:"key";s:16:"site_description";s:5:"value";s:47:"Your trusted source for latest news and updates";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:16:"Site Description";s:11:"description";s:35:"A brief description of your website";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:26:34";}s:11:" * original";a:11:{s:2:"id";i:3;s:3:"key";s:16:"site_description";s:5:"value";s:47:"Your trusted source for latest news and updates";s:4:"type";s:8:"textarea";s:5:"group";s:7:"general";s:5:"label";s:16:"Site Description";s:11:"description";s:35:"A brief description of your website";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:26:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:4;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:4;s:3:"key";s:13:"site_keywords";s:5:"value";s:36:"news, updates, latest, breaking news";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:13:"Site Keywords";s:11:"description";s:32:"SEO keywords separated by commas";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:26:34";}s:11:" * original";a:11:{s:2:"id";i:4;s:3:"key";s:13:"site_keywords";s:5:"value";s:36:"news, updates, latest, breaking news";s:4:"type";s:4:"text";s:5:"group";s:7:"general";s:5:"label";s:13:"Site Keywords";s:11:"description";s:32:"SEO keywords separated by commas";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:26:34";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:3:"seo";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:16;s:3:"key";s:16:"meta_description";s:5:"value";s:59:"NitiKotha - Your trusted source for latest news and updates";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:16:"Meta Description";s:11:"description";s:32:"Default meta description for SEO";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:16;s:3:"key";s:16:"meta_description";s:5:"value";s:59:"NitiKotha - Your trusted source for latest news and updates";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:16:"Meta Description";s:11:"description";s:32:"Default meta description for SEO";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:17;s:3:"key";s:13:"meta_keywords";s:5:"value";s:61:"news, bangladesh, politics, sports, technology, entertainment";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:13:"Meta Keywords";s:11:"description";s:29:"Default meta keywords for SEO";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:17;s:3:"key";s:13:"meta_keywords";s:5:"value";s:61:"news, bangladesh, politics, sports, technology, entertainment";s:4:"type";s:8:"textarea";s:5:"group";s:3:"seo";s:5:"label";s:13:"Meta Keywords";s:11:"description";s:29:"Default meta keywords for SEO";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-14 13:38:09";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:6:"social";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:10;s:3:"key";s:15:"social_facebook";s:5:"value";s:30:"https://facebook.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:12:"Facebook URL";s:11:"description";s:17:"Facebook page URL";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:10;s:3:"key";s:15:"social_facebook";s:5:"value";s:30:"https://facebook.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:12:"Facebook URL";s:11:"description";s:17:"Facebook page URL";s:10:"sort_order";i:1;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:11;s:3:"key";s:14:"social_twitter";s:5:"value";s:29:"https://twitter.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:11:"Twitter URL";s:11:"description";s:19:"Twitter profile URL";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:11;s:3:"key";s:14:"social_twitter";s:5:"value";s:29:"https://twitter.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:11:"Twitter URL";s:11:"description";s:19:"Twitter profile URL";s:10:"sort_order";i:2;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:12;s:3:"key";s:14:"social_youtube";s:5:"value";s:29:"https://youtube.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:11:"YouTube URL";s:11:"description";s:19:"YouTube channel URL";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:11:" * original";a:11:{s:2:"id";i:12;s:3:"key";s:14:"social_youtube";s:5:"value";s:29:"https://youtube.com/nitikotha";s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:11:"YouTube URL";s:11:"description";s:19:"YouTube channel URL";s:10:"sort_order";i:3;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-14 13:38:09";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:22:"App\Models\SiteSetting":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"site_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:13;s:3:"key";s:16:"social_instagram";s:5:"value";N;s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:13:"Instagram URL";s:11:"description";s:26:"Your Instagram profile URL";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:45:36";}s:11:" * original";a:11:{s:2:"id";i:13;s:3:"key";s:16:"social_instagram";s:5:"value";N;s:4:"type";s:3:"url";s:5:"group";s:6:"social";s:5:"label";s:13:"Instagram URL";s:11:"description";s:26:"Your Instagram profile URL";s:10:"sort_order";i:4;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-13 19:26:34";s:10:"updated_at";s:19:"2025-07-13 19:45:36";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"sort_order";s:7:"integer";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:3:"key";i:1;s:5:"value";i:2;s:4:"type";i:3;s:5:"group";i:4;s:5:"label";i:5;s:11:"description";i:6;s:10:"sort_order";i:7;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}