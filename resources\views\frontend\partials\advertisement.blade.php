{{-- Regular Advertisement Display Component --}}
@if($ad && $ad->is_active)
<div class="fb-advertisement-container" data-ad-id="{{ $ad->id }}" data-ad-type="regular" data-position="{{ $position ?? 'default' }}">
    <div class="fb-advertisement fb-ad-{{ $ad->ad_type }} fb-ad-position-{{ $position ?? 'default' }}">
        <!-- Advertisement Header -->
        <div class="fb-ad-header">
            <div class="fb-ad-label">
                <i class="fas fa-bullhorn"></i>
                <span>Sponsored</span>
            </div>
            <div class="fb-ad-actions">
                <button class="fb-ad-action" data-action="hide" data-ad-id="{{ $ad->id }}" title="Hide this ad">
                    <i class="fas fa-times"></i>
                </button>
                <button class="fb-ad-action" data-action="report" data-ad-id="{{ $ad->id }}" title="Report ad">
                    <i class="fas fa-flag"></i>
                </button>
            </div>
        </div>

        <!-- Advertisement Content -->
        <div class="fb-ad-content">
            @if($ad->ad_type === 'banner')
                <!-- Banner Advertisement -->
                <div class="fb-ad-banner">
                    @if($ad->image)
                        <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-ad-image">
                    @endif
                    <div class="fb-ad-text">
                        <h4 class="fb-ad-title">{{ $ad->title }}</h4>
                        @if($ad->description)
                            <p class="fb-ad-description">{{ Str::limit($ad->description, 100) }}</p>
                        @endif
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-ad-link" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                Learn More
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_type === 'sidebar')
                <!-- Sidebar Advertisement -->
                <div class="fb-ad-sidebar">
                    @if($ad->image)
                        <div class="fb-ad-image-container">
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-ad-image">
                        </div>
                    @endif
                    <div class="fb-ad-content-text">
                        <h5 class="fb-ad-title">{{ $ad->title }}</h5>
                        @if($ad->description)
                            <p class="fb-ad-description">{{ Str::limit($ad->description, 80) }}</p>
                        @endif
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-ad-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                View Details
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_type === 'inline')
                <!-- Inline Advertisement -->
                <div class="fb-ad-inline">
                    <div class="fb-ad-inline-content">
                        @if($ad->image)
                            <div class="fb-ad-image-wrapper">
                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-ad-image">
                            </div>
                        @endif
                        <div class="fb-ad-text-wrapper">
                            <h4 class="fb-ad-title">{{ $ad->title }}</h4>
                            @if($ad->description)
                                <p class="fb-ad-description">{{ Str::limit($ad->description, 120) }}</p>
                            @endif
                            @if($ad->link_url)
                                <a href="{{ $ad->link_url }}" class="fb-ad-button" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                    <i class="fas fa-arrow-right"></i>
                                    Learn More
                                </a>
                            @endif
                        </div>
                    </div>
                </div>

            @elseif($ad->ad_type === 'popup')
                <!-- Popup Advertisement (Modal) -->
                <div class="fb-ad-popup-trigger" data-ad-id="{{ $ad->id }}">
                    <div class="fb-ad-popup-preview">
                        @if($ad->image)
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-ad-preview-image">
                        @endif
                        <div class="fb-ad-preview-text">
                            <h5 class="fb-ad-title">{{ $ad->title }}</h5>
                            <p class="fb-ad-description">{{ Str::limit($ad->description, 60) }}</p>
                            <button class="fb-ad-popup-btn">View Offer</button>
                        </div>
                    </div>
                </div>

            @endif
        </div>

        <!-- Advertisement Footer (Hidden on Frontend) -->
        <div class="fb-ad-footer d-none">
            <div class="fb-ad-performance">
                <span class="fb-ad-views">{{ number_format($ad->view_count ?? 0) }} views</span>
                <span class="fb-ad-clicks">{{ number_format($ad->click_count ?? 0) }} clicks</span>
            </div>
            <div class="fb-ad-meta">
                <span class="fb-ad-type">{{ ucfirst($ad->ad_type) }} Ad</span>
                <span class="fb-ad-position">{{ ucfirst($position ?? 'default') }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Popup Modal for Popup Ads -->
@if($ad->ad_type === 'popup')
<div class="fb-ad-modal" id="ad-modal-{{ $ad->id }}" style="display: none;">
    <div class="fb-ad-modal-overlay" data-close-modal="{{ $ad->id }}"></div>
    <div class="fb-ad-modal-content">
        <button class="fb-ad-modal-close" data-close-modal="{{ $ad->id }}">
            <i class="fas fa-times"></i>
        </button>
        @if($ad->image)
            <div class="fb-ad-modal-image">
                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}">
            </div>
        @endif
        <div class="fb-ad-modal-text">
            <h3 class="fb-ad-modal-title">{{ $ad->title }}</h3>
            @if($ad->description)
                <p class="fb-ad-modal-description">{{ $ad->description }}</p>
            @endif
            @if($ad->link_url)
                <a href="{{ $ad->link_url }}" class="fb-ad-modal-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                    <i class="fas fa-external-link-alt"></i>
                    Visit Now
                </a>
            @endif
        </div>
    </div>
</div>
@endif

@push('scripts')
<script>
// Advertisement interaction tracking
$(document).ready(function() {
    var adId = {{ $ad->id }};
    var adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="regular"]');

    console.log('Initializing tracking for regular ad:', adId);

    // Track advertisement view using Intersection Observer for accurate tracking
    if (typeof IntersectionObserver !== 'undefined') {
        var observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                    trackAdView(adId);
                    observer.unobserve(entry.target); // Track view only once
                }
            });
        }, { threshold: 0.5 });

        if (adContainer.length > 0) {
            observer.observe(adContainer[0]);
        }
    } else {
        // Fallback for older browsers
        trackAdView(adId);
    }

    // Track advertisement clicks
    adContainer.on('click', 'a[href]', function(e) {
        console.log('Regular ad click tracked:', adId);
        trackAdClick(adId);
    });

    // Handle ad actions (hide, report)
    adContainer.on('click', '.fb-ad-action', function(e) {
        e.preventDefault();
        var action = $(this).data('action');

        console.log('Ad action:', action, 'for ad:', adId);

        if (action === 'hide') {
            hideAdvertisement(adId);
        } else if (action === 'report') {
            reportAdvertisement(adId);
        }
    });

    // Handle popup ads
    @if($ad->ad_type === 'popup')
    $('.fb-ad-popup-trigger[data-ad-id="' + adId + '"]').on('click', function() {
        $('#ad-modal-' + adId).fadeIn(300);
        trackAdClick(adId);
    });

    $('[data-close-modal="' + adId + '"]').on('click', function() {
        $('#ad-modal-' + adId).fadeOut(300);
    });
    @endif
});

function trackAdView(adId) {
    console.log('Tracking view for regular ad:', adId);

    $.ajax({
        url: '/api/regular-ads/track-view',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('View tracked successfully for ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track view for ad:', adId, error);
        }
    });
}

function trackAdClick(adId) {
    console.log('Tracking click for regular ad:', adId);

    $.ajax({
        url: '/api/regular-ads/track-click',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Click tracked successfully for ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track click for ad:', adId, error);
        }
    });
}

function hideAdvertisement(adId) {
    $('[data-ad-id="' + adId + '"]').fadeOut(300);

    $.ajax({
        url: '/api/regular-ads/track-hide',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Hide tracked successfully for ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track hide for ad:', adId, error);
        }
    });
}

function reportAdvertisement(adId) {
    if (confirm('Report this advertisement as inappropriate?')) {
        $.ajax({
            url: '/api/regular-ads/report',
            method: 'POST',
            data: {
                ad_id: adId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                console.log('Report tracked successfully for ad:', adId);
                alert('Advertisement reported successfully.');
                $('[data-ad-id="' + adId + '"]').fadeOut(300);
            },
            error: function(xhr, status, error) {
                console.error('Failed to track report for ad:', adId, error);
                alert('Failed to report advertisement. Please try again.');
            }
        });
    }
}
</script>
@endpush
@endif
