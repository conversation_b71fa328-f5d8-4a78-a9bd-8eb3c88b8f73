@extends('admin.admin_management_dashboard')
@section('admin')

 <div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">
                        
                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <a href="{{ route('add.category') }}" class="btn btn-blue waves-effect waves-light">Add Category</a>
                </ol>
            </div>
                                    <h4 class="page-title">Category All </h4>
                                </div>
                            </div>
                        </div>     
                        <!-- end page title --> 

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
 

        <table id="basic-datatable" class="table dt-responsive nowrap w-100">
            <thead>
                <tr>
                    <th width="80">Order</th>
                    <th>Icon</th>
                    <th>Category Name</th>
                    <th>Status</th>
                    <th width="200">Order Controls</th>
                    <th>Action</th>
                </tr>
            </thead>


            <tbody id="sortable-categories">
            	@foreach($categories as $key=> $item)
                <tr data-category-id="{{ $item->id }}" data-order="{{ $item->display_order }}"
                    class="category-row {{ $item->is_active ? '' : 'table-secondary' }}">
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2 order-display">{{ $item->display_order }}</span>
                            <input type="number" class="form-control form-control-sm order-input"
                                   value="{{ $item->display_order }}"
                                   min="0"
                                   max="999"
                                   style="width: 60px; display: none;">
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="category-icon me-2" style="font-size: 24px;">{{ $item->category_icon }}</span>
                            <small class="text-muted">{{ $item->category_mdi_icon }}</small>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="me-2">{{ $item->category_name }}</span>
                            @if($item->is_featured)
                                <span class="badge bg-warning">Featured</span>
                            @endif
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column gap-1">
                            <div class="form-check form-switch">
                                <input class="form-check-input active-toggle" type="checkbox"
                                       data-category-id="{{ $item->id }}" {{ $item->is_active ? 'checked' : '' }}>
                                <label class="form-check-label">
                                    {{ $item->is_active ? 'Active' : 'Inactive' }}
                                </label>
                            </div>
                            <button class="btn btn-sm {{ $item->is_featured ? 'btn-warning' : 'btn-outline-warning' }} featured-btn"
                                    data-category-id="{{ $item->id }}" title="{{ $item->is_featured ? 'Remove Featured' : 'Mark Featured' }}">
                                <i class="mdi mdi-star"></i> {{ $item->is_featured ? 'Featured' : 'Feature' }}
                            </button>
                        </div>
                    </td>

                    <!-- Order Controls -->
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary move-up"
                                    data-category-id="{{ $item->id }}"
                                    title="Move Up">
                                <i class="mdi mdi-arrow-up"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary move-down"
                                    data-category-id="{{ $item->id }}"
                                    title="Move Down">
                                <i class="mdi mdi-arrow-down"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary edit-order"
                                    data-category-id="{{ $item->id }}"
                                    title="Edit Order">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-success save-order"
                                    data-category-id="{{ $item->id }}"
                                    title="Save Order"
                                    style="display: none;">
                                <i class="mdi mdi-check"></i>
                            </button>
                        </div>
                    </td>

                    <td>
        @if(Auth::user()->can('category.edit'))
      <a href="{{ route('edit.category',$item->id) }}" class="btn btn-primary rounded-pill waves-effect waves-light">Edit</a>
      @endif
 @if(Auth::user()->can('category.delete'))
      <a href="{{ route('delete.category',$item->id) }}" class="btn btn-danger rounded-pill waves-effect waves-light" id="delete">Delete</a>
@endif
                    </td>
                </tr>
                @endforeach
                 
            </tbody>
        </table>

                                    </div> <!-- end card body-->
                                </div> <!-- end card -->
                            </div><!-- end col-->
                        </div>
                        <!-- end row-->

 
                        
                    </div> <!-- container -->

                </div> <!-- content -->

<style>
.category-row.ui-sortable-helper {
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.category-row.table-secondary {
    opacity: 0.7;
}

.order-badge {
    min-width: 30px;
    display: inline-block;
    text-align: center;
}

#sortable-categories .mdi-drag-vertical {
    opacity: 0.5;
    transition: opacity 0.2s;
}

#sortable-categories tr:hover .mdi-drag-vertical {
    opacity: 1;
}
</style>

<script>
$(document).ready(function() {
    console.log('Category Management Ready');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');

    // Initialize category sortable
    initializeCategorySortable();
    initializeCategoryActions();

    function initializeCategorySortable() {
        console.log('Initializing category sortable...');

        var $table = $("#sortable-categories");
        console.log('Category table found:', $table.length);

        if ($table.length === 0) {
            console.error('Category sortable table not found');
            return;
        }

        try {
            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr",
                axis: "y",
                cursor: "move",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Category drag started');
                    ui.placeholder.height(ui.item.height());
                },
                update: function(event, ui) {
                    console.log('Category order updated');
                    updateCategoryOrder();
                },
                stop: function(event, ui) {
                    console.log('Category drag stopped');
                }
            });

            console.log('Category sortable initialized successfully');

        } catch (error) {
            console.error('Category sortable initialization error:', error);
        }
    }

    // Update category order after drag and drop
    function updateCategoryOrder() {
        var categories = [];
        $("#sortable-categories tr").each(function(index) {
            var categoryId = $(this).data('category-id');
            if (categoryId) {
                categories.push({
                    id: categoryId,
                    order: index
                });
                // Update the order badge
                $(this).find('.order-badge').text(index);
            }
        });

        console.log('Updating order for categories:', categories);

        $.ajax({
            url: '{{ route("categories.update-order") }}',
            method: 'POST',
            data: {
                categories: categories,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                $('.order-badge').addClass('bg-warning').removeClass('bg-secondary');
            },
            success: function(response) {
                console.log('Category order update response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    $('.order-badge').addClass('bg-secondary').removeClass('bg-warning');
                }
            },
            error: function(xhr, status, error) {
                console.error('Category order update error:', xhr.responseText);
                toastr.error('Failed to update category order');
                location.reload();
            }
        });
    }

    function initializeCategoryActions() {
        console.log('Initializing category actions...');

        // Active toggle functionality
        $(document).off('change', '.active-toggle').on('change', '.active-toggle', function() {
            var categoryId = $(this).data('category-id');
            var isActive = $(this).is(':checked');
            var label = $(this).next('label');
            var toggle = $(this);

            console.log('Category active toggle:', {categoryId, isActive});

            $.ajax({
                url: '{{ route("categories.toggle-active", ":id") }}'.replace(':id', categoryId),
                method: 'POST',
                data: { _token: '{{ csrf_token() }}' },
                beforeSend: function() {
                    toggle.prop('disabled', true);
                },
                success: function(response) {
                    console.log('Category active response:', response);
                    if (response.success) {
                        label.text(response.is_active ? 'Active' : 'Inactive');
                        toastr.success(response.message);
                    }
                    toggle.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Category status toggle error:', xhr.responseText);
                    // Revert the toggle
                    toggle.prop('checked', !isActive);
                    toastr.error('Failed to toggle category status');
                    toggle.prop('disabled', false);
                }
            });
        });

        // Featured toggle functionality
        $(document).off('click', '.featured-btn').on('click', '.featured-btn', function(e) {
            e.preventDefault();
            console.log('Category featured button clicked');

            var categoryId = $(this).data('category-id');
            var btn = $(this);

            $.ajax({
                url: '{{ route("categories.toggle-featured", ":id") }}'.replace(':id', categoryId),
                method: 'POST',
                data: { _token: '{{ csrf_token() }}' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                },
                success: function(response) {
                    console.log('Category featured response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_featured) {
                            btn.removeClass('btn-outline-warning').addClass('btn-warning');
                            btn.attr('title', 'Remove Featured');
                            btn.html('<i class="mdi mdi-star"></i> Featured');
                        } else {
                            btn.removeClass('btn-warning').addClass('btn-outline-warning');
                            btn.attr('title', 'Mark Featured');
                            btn.html('<i class="mdi mdi-star"></i> Feature');
                        }
                        toastr.success(response.message);
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Category featured toggle error:', xhr.responseText);
                    toastr.error('Failed to toggle featured status');
                    btn.prop('disabled', false);
                }
            });
        });

        console.log('Category actions initialized');
    }

    // ===== CATEGORY ORDER CONTROLS =====

    // Move Up
    $(document).on('click', '.move-up', function(e) {
        e.preventDefault();
        const categoryId = $(this).data('category-id');
        const $row = $(this).closest('tr');
        const currentOrder = parseInt($row.data('order'));
        const newOrder = Math.max(0, currentOrder - 1);

        updateCategoryOrder(categoryId, newOrder, $row);
    });

    // Move Down
    $(document).on('click', '.move-down', function(e) {
        e.preventDefault();
        const categoryId = $(this).data('category-id');
        const $row = $(this).closest('tr');
        const currentOrder = parseInt($row.data('order'));
        const newOrder = currentOrder + 1;

        updateCategoryOrder(categoryId, newOrder, $row);
    });

    // Edit Order
    $(document).on('click', '.edit-order', function(e) {
        e.preventDefault();
        const $row = $(this).closest('tr');
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $(this);
        const $saveBtn = $row.find('.save-order');

        $orderDisplay.hide();
        $orderInput.show().focus();
        $editBtn.hide();
        $saveBtn.show();
    });

    // Save Order
    $(document).on('click', '.save-order', function(e) {
        e.preventDefault();
        const categoryId = $(this).data('category-id');
        const $row = $(this).closest('tr');
        const newOrder = parseInt($row.find('.order-input').val());

        updateCategoryOrder(categoryId, newOrder, $row);
    });

    // Enter key to save
    $(document).on('keypress', '.order-input', function(e) {
        if (e.which === 13) {
            $(this).closest('tr').find('.save-order').click();
        }
    });

    function updateCategoryOrder(categoryId, newOrder, $row) {
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        // Show loading
        $orderDisplay.text('...').removeClass('bg-secondary').addClass('bg-warning');

        $.ajax({
            url: '{{ route("categories.update-order") }}',
            method: 'POST',
            data: {
                category_id: categoryId,
                order: newOrder,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $orderDisplay.text(newOrder).removeClass('bg-warning').addClass('bg-secondary');
                    $row.data('order', newOrder);

                    // Reset edit mode
                    $orderDisplay.show();
                    $orderInput.hide();
                    $editBtn.show();
                    $saveBtn.hide();

                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message || 'Category order updated successfully!');
                    }
                } else {
                    resetCategoryOrderEdit($row);
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message || 'Failed to update category order');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Category order update error:', xhr.responseText);
                resetCategoryOrderEdit($row);

                let errorMessage = 'Failed to update category order';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                }
            }
        });
    }

    function resetCategoryOrderEdit($row) {
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        $orderDisplay.removeClass('bg-warning').addClass('bg-secondary').show();
        $orderInput.hide();
        $editBtn.show();
        $saveBtn.hide();
    }

    console.log('Category order management initialized');

});
</script>

@endsection
        