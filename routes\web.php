<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\UserController;
use App\Http\Middleware\RedirectIfAuthenticated;

use App\Http\Controllers\Frontend\IndexController;
use App\Http\Controllers\Frontend\ReviewController;

use App\Http\Controllers\Backend\CategoryController;
use App\Http\Controllers\Backend\NewsPostController;
use App\Http\Controllers\Backend\BannerController;
use App\Http\Controllers\Backend\PhotoGalleryController;
use App\Http\Controllers\Backend\VideoGalleryController;
use App\Http\Controllers\Backend\SeoSettingController;
use App\Http\Controllers\Backend\RoleController;
use App\Http\Controllers\Backend\PostApprovalController;
use App\Http\Controllers\Backend\PostManagementController;
use App\Http\Controllers\Backend\AdvertisementController;
use App\Http\Controllers\Backend\SponsoredAdController;
use App\Http\Controllers\Backend\SubscriberManagementController;
use App\Http\Controllers\Frontend\SubscriberPostController;
  
/*   
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/ 

// Route::get('/', function () {
//     return view('welcome');
// });

Route::get('/', [IndexController::class, 'Index']);
Route::get('/v2', [IndexController::class, 'IndexV2']);

// AJAX endpoints for v2
Route::get('/v2/load-more-posts', [IndexController::class, 'loadMorePosts'])->name('v2.load-more-posts');

// API routes for news interactions
Route::post('/api/news/like', [IndexController::class, 'toggleLike'])->name('api.news.like');
Route::post('/api/news/share', [IndexController::class, 'recordShare'])->name('api.news.share');
Route::post('/api/news/comment', [IndexController::class, 'addComment'])->name('api.news.comment');

// API routes for category functionality
Route::get('/api/category/{id}/filter', [IndexController::class, 'filterCategoryPosts'])->name('api.category.filter');
Route::post('/api/category/follow', [IndexController::class, 'toggleCategoryFollow'])->name('api.category.follow');

// API routes for reactions and comments
Route::post('/api/news/react', [IndexController::class, 'addReaction'])->name('api.news.react');
Route::get('/api/news/{id}/comments', [IndexController::class, 'getComments'])->name('api.news.comments');

// Sponsored Advertisement tracking API routes
Route::post('/api/ads/track-view', function(Request $request) {
    try {
        $adId = $request->input('ad_id');
        if ($adId) {
            $ad = \App\Models\SponsoredAd::find($adId);
            if ($ad) {
                $ad->incrementViews();
                \Log::info('Sponsored ad view tracked', ['ad_id' => $adId]);
                return response()->json(['success' => true, 'message' => 'View tracked']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Ad not found']);
    } catch (\Exception $e) {
        \Log::error('Error tracking sponsored ad view', ['error' => $e->getMessage(), 'ad_id' => $request->input('ad_id')]);
        return response()->json(['success' => false, 'message' => 'Tracking failed']);
    }
});

Route::post('/api/ads/track-click', function(Request $request) {
    try {
        $adId = $request->input('ad_id');
        if ($adId) {
            $ad = \App\Models\SponsoredAd::find($adId);
            if ($ad) {
                $ad->incrementClicks();
                \Log::info('Sponsored ad click tracked', ['ad_id' => $adId]);
                return response()->json(['success' => true, 'message' => 'Click tracked']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Ad not found']);
    } catch (\Exception $e) {
        \Log::error('Error tracking sponsored ad click', ['error' => $e->getMessage(), 'ad_id' => $request->input('ad_id')]);
        return response()->json(['success' => false, 'message' => 'Tracking failed']);
    }
});

Route::post('/api/ads/track-hide', function(Request $request) {
    $adId = $request->input('ad_id');
    if ($adId) {
        $ad = \App\Models\SponsoredAd::find($adId);
        if ($ad) {
            $ad->increment('hide_count');
        }
    }
    return response()->json(['success' => true]);
});

Route::post('/api/ads/report', function(Request $request) {
    $adId = $request->input('ad_id');
    if ($adId) {
        $ad = \App\Models\SponsoredAd::find($adId);
        if ($ad) {
            $ad->increment('report_count');
        }
    }
    return response()->json(['success' => true]);
});

// Regular Advertisement tracking API routes
Route::post('/api/regular-ads/track-view', function(Request $request) {
    try {
        $adId = $request->input('ad_id');
        if ($adId) {
            $ad = \App\Models\Advertisement::find($adId);
            if ($ad) {
                $ad->incrementViews();
                \Log::info('Regular ad view tracked', ['ad_id' => $adId]);
                return response()->json(['success' => true, 'message' => 'View tracked']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Ad not found']);
    } catch (\Exception $e) {
        \Log::error('Error tracking regular ad view', ['error' => $e->getMessage(), 'ad_id' => $request->input('ad_id')]);
        return response()->json(['success' => false, 'message' => 'Tracking failed']);
    }
});

Route::post('/api/regular-ads/track-click', function(Request $request) {
    try {
        $adId = $request->input('ad_id');
        if ($adId) {
            $ad = \App\Models\Advertisement::find($adId);
            if ($ad) {
                $ad->incrementClicks();
                \Log::info('Regular ad click tracked', ['ad_id' => $adId]);
                return response()->json(['success' => true, 'message' => 'Click tracked']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Ad not found']);
    } catch (\Exception $e) {
        \Log::error('Error tracking regular ad click', ['error' => $e->getMessage(), 'ad_id' => $request->input('ad_id')]);
        return response()->json(['success' => false, 'message' => 'Tracking failed']);
    }
});

Route::post('/api/regular-ads/track-hide', function(Request $request) {
    $adId = $request->input('ad_id');
    if ($adId) {
        $ad = \App\Models\Advertisement::find($adId);
        if ($ad) {
            $ad->increment('hide_count');
        }
    }
    return response()->json(['success' => true]);
});

Route::post('/api/regular-ads/report', function(Request $request) {
    $adId = $request->input('ad_id');
    if ($adId) {
        $ad = \App\Models\Advertisement::find($adId);
        if ($ad) {
            $ad->increment('report_count');
        }
    }
    return response()->json(['success' => true]);
});

// Real-time performance tracking endpoints
Route::get('/api/ads/performance/{id}', function($id) {
    $ad = \App\Models\Advertisement::find($id);
    if (!$ad) {
        return response()->json(['error' => 'Advertisement not found'], 404);
    }

    return response()->json([
        'success' => true,
        'performance' => $ad->getPerformanceMetrics()
    ]);
});

Route::get('/api/sponsored-ads/performance/{id}', function($id) {
    $ad = \App\Models\SponsoredAd::find($id);
    if (!$ad) {
        return response()->json(['error' => 'Sponsored ad not found'], 404);
    }

    return response()->json([
        'success' => true,
        'performance' => $ad->getPerformanceMetrics()
    ]);
});

// Bulk performance data for admin dashboard
Route::get('/api/ads/performance-summary', function() {
    $advertisements = \App\Models\Advertisement::where('is_active', true)
        ->select('id', 'title', 'view_count', 'click_count', 'performance_score')
        ->get()
        ->map(function($ad) {
            return array_merge($ad->toArray(), ['performance' => $ad->getPerformanceMetrics()]);
        });

    $sponsoredAds = \App\Models\SponsoredAd::where('is_active', true)
        ->select('id', 'title', 'view_count', 'click_count', 'performance_score', 'budget')
        ->get()
        ->map(function($ad) {
            return array_merge($ad->toArray(), ['performance' => $ad->getPerformanceMetrics()]);
        });

    return response()->json([
        'success' => true,
        'advertisements' => $advertisements,
        'sponsored_ads' => $sponsoredAds
    ]);
});

// Test route for advertisement tracking
Route::get('/test-ad-tracking', function() {
    $regularAds = \App\Models\Advertisement::where('is_active', true)->take(3)->get();
    $sponsoredAds = \App\Models\SponsoredAd::where('is_active', true)->take(3)->get();

    return response()->json([
        'regular_ads' => $regularAds->map(function($ad) {
            return [
                'id' => $ad->id,
                'title' => $ad->title,
                'view_count' => $ad->view_count,
                'click_count' => $ad->click_count,
                'is_active' => $ad->is_active
            ];
        }),
        'sponsored_ads' => $sponsoredAds->map(function($ad) {
            return [
                'id' => $ad->id,
                'title' => $ad->title,
                'view_count' => $ad->view_count,
                'click_count' => $ad->click_count,
                'is_active' => $ad->is_active
            ];
        })
    ]);
});

// Facebook-style routes for v2
Route::get('/v2/news/category/{id}/{slug}', [IndexController::class, 'CatWiseNewsV2']);
Route::get('/v2/news/details/{id}/{slug}', [IndexController::class, 'NewsDetailsV2']);
Route::get('/v2/news/subcategory/{id}/{slug}', [IndexController::class, 'SubCatWiseNewsV2']);

// News interaction routes
use App\Http\Controllers\Frontend\NewsInteractionController;
Route::post('/news/{id}/like', [NewsInteractionController::class, 'toggleLike'])->name('news.like');
Route::post('/news/{id}/save', [NewsInteractionController::class, 'toggleSave'])->name('news.save');
Route::post('/news/{id}/share', [NewsInteractionController::class, 'recordShare'])->name('news.share');
Route::post('/news/{id}/comment', [NewsInteractionController::class, 'addComment'])->name('news.comment');
Route::get('/news/{id}/comments', [NewsInteractionController::class, 'getComments'])->name('news.comments');
Route::post('/comment/{id}/like', [NewsInteractionController::class, 'likeComment'])->name('comment.like');

// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth', 'verified'])->name('dashboard');


Route::middleware(['auth'])->group(function() {

Route::get('/dashboard', [UserController::class, 'UserDashboard'])->name('dashboard');

Route::post('/user/profile/store', [UserController::class, 'UserProfileStore'])->name('user.profile.store');

Route::get('/user/logout', [UserController::class, 'UserLogout'])->name('user.logout');

Route::get('/change/password', [UserController::class, 'ChangePassword'])->name('change.password');

Route::post('/user/change/password', [UserController::class, 'UserChangePassword'])->name('user.change.password');

}); // End User Middleware

// Subcategory API route (accessible to authenticated users)
Route::middleware(['auth'])->group(function() {
    Route::get('/get-subcategories/{category_id}', [SubscriberPostController::class, 'getSubcategories']);
});

// Subscriber Routes
Route::middleware(['auth', 'role:Subscriber'])->group(function() {
    Route::prefix('subscriber')->name('subscriber.')->group(function() {
        Route::get('/dashboard', [SubscriberPostController::class, 'dashboard'])->name('dashboard');
        Route::get('/posts', [SubscriberPostController::class, 'index'])->name('posts.index');
        Route::get('/posts/create', [SubscriberPostController::class, 'create'])->name('posts.create');
        Route::post('/posts', [SubscriberPostController::class, 'store'])->name('posts.store');
        Route::get('/posts/{id}', [SubscriberPostController::class, 'show'])->name('posts.show');
        Route::get('/posts/{id}/edit', [SubscriberPostController::class, 'edit'])->name('posts.edit');
        Route::put('/posts/{id}', [SubscriberPostController::class, 'update'])->name('posts.update');
        Route::delete('/posts/{id}', [SubscriberPostController::class, 'destroy'])->name('posts.destroy');
        Route::get('/subcategories/{categoryId}', [SubscriberPostController::class, 'getSubcategories'])->name('subcategories');
        Route::post('/upload-image', [SubscriberPostController::class, 'uploadImage'])->name('upload.image');
        Route::get('/profile', [SubscriberPostController::class, 'profile'])->name('profile');
        Route::post('/profile', [SubscriberPostController::class, 'updateProfile'])->name('profile.update');
        Route::post('/test-form', function(\Illuminate\Http\Request $request) {
            \Log::info('Test form submitted', $request->all());
            return response()->json(['status' => 'success', 'data' => $request->all()]);
        })->name('test.form');
    });
});

require __DIR__.'/auth.php';




Route::middleware(['auth','role:admin|Manager'])->group(function() {


Route::get('/admin/dashboard', [AdminController::class, 'AdminDashboard'])->name('admin.dashboard');

Route::get('/admin/logout', [AdminController::class, 'AdminLogout'])->name('admin.logout');

Route::get('/admin/profile', [AdminController::class, 'AdminProfile'])->name('admin.profile');

Route::post('/admin/profile/store', [AdminController::class, 'AdminProfileStore'])->name('admin.profile.store');

Route::get('/admin/change/password', [AdminController::class, 'AdminChangePassword'])->name('admin.change.password');

Route::post('/admin/update/password', [AdminController::class, 'AdminUpdatePassword'])->name('admin.update.password');

}); // End Admin Middleware


Route::get('/admin/login', [AdminController::class, 'AdminLogin'])->middleware(RedirectIfAuthenticated::class)->name('admin.login');

Route::get('/admin/logout/page', [AdminController::class, 'AdminLogoutPage'])->name('admin.logout.page'); 




Route::middleware(['auth','role:admin|Manager'])->group(function() {

// Category all Route
Route::controller(CategoryController::class)->group(function(){

    Route::get('/all/category','AllCategory')->name('all.category');
    Route::get('/add/category','AddCategory')->name('add.category');
    Route::post('/store/category','StoreCategory')->name('category.store');
    Route::get('/edit/category/{id}','EditCategory')->name('edit.category');
    Route::post('/update/category','UpdateCategory')->name('category.update');
    Route::get('/delete/category/{id}','DeleteCategory')->name('delete.category');
    Route::post('/categories/update-order','updateOrder')->name('categories.update-order');
    Route::post('/categories/{id}/toggle-featured','toggleFeatured')->name('categories.toggle-featured');
    Route::post('/categories/{id}/toggle-active','toggleActive')->name('categories.toggle-active');

});


// SubCategory all Route
Route::controller(CategoryController::class)->group(function(){

    Route::get('/all/subcategory','AllSubCategory')->name('all.subcategory');
    Route::get('/add/subcategory','AddSubCategory')->name('add.subcategory');
    Route::post('/store/subcategory','StoreSubCategory')->name('subcategory.store');
    Route::get('/edit/subcategory/{id}','EditSubCategory')->name('edit.subcategory');
    Route::post('/update/subcategory','UpdateSubCategory')->name('subcategory.updated');
    Route::get('/delete/subcategory/{id}','DeleteSubCategory')->name('delete.subcategory');
    Route::post('/subcategories/update-order','updateSubcategoryOrder')->name('subcategories.update-order');
    Route::post('/subcategories/{id}/toggle-featured','toggleSubcategoryFeatured')->name('subcategories.toggle-featured');
    Route::post('/subcategories/{id}/toggle-active','toggleSubcategoryActive')->name('subcategories.toggle-active');

     Route::get('/subcategory/ajax/{category_id}','GetSubCategory');




// Admin User all Route
Route::controller(AdminController::class)->group(function(){

    Route::get('/all/admin','AllAdmin')->name('all.admin');
    Route::get('/add/admin','AddAdmin')->name('add.admin');
    Route::post('/store/admin','StoreAdmin')->name('admin.store');
    Route::get('/edit/admin/{id}','EditAdmin')->name('edit.admin');
    Route::post('/update/admin','UpdateAdmin')->name('admin.update');
    Route::get('/delete/admin/{id}','DeleteAdmin')->name('delete.admin');

    Route::get('/inactive/admin/user/{id}','InactiveAdminUser')->name('inactive.admin.user');

    Route::get('/active/admin/user/{id}','ActiveAdminUser')->name('active.admin.user');

});


});



// News Post all Route
Route::controller(NewsPostController::class)->group(function(){

    Route::get('/all/news/post','AllNewsPost')->name('all.news.post');
    Route::get('/add/news/post','AddNewsPost')->name('add.news.post');

    Route::post('/store/news/post','StoreNewsPost')->name('store.news.post');
    Route::get('/edit/news/post/{id}','EditNewsPost')->name('edit.news.post');
    Route::post('/update/news/post','UpdateNewsPost')->name('update.news.post');
    Route::get('/delete/news/post/{id}','DeleteNewsPost')->name('delete.news.post');

    Route::get('/inactive/news/post/{id}','InactiveNewsPost')->name('inactive.news.post');
     Route::get('/active/news/post/{id}','ActiveNewsPost')->name('active.news.post');

    // CKEditor image upload for admin
    Route::post('/ckeditor/upload','CKEditorUpload')->name('ckeditor.upload');

});

// Subscriber Management Routes
Route::controller(SubscriberManagementController::class)->group(function(){
    Route::get('/admin/subscribers', 'index')->name('admin.subscribers.index');
    Route::get('/admin/subscribers/create', 'create')->name('admin.subscribers.create');
    Route::post('/admin/subscribers', 'store')->name('admin.subscribers.store');
    Route::get('/admin/subscribers/{id}', 'show')->name('admin.subscribers.show');
    Route::get('/admin/subscribers/{id}/edit', 'edit')->name('admin.subscribers.edit');
    Route::put('/admin/subscribers/{id}', 'update')->name('admin.subscribers.update');
    Route::patch('/admin/subscribers/{id}/toggle-status', 'toggleStatus')->name('admin.subscribers.toggle-status');
    Route::delete('/admin/subscribers/{id}', 'destroy')->name('admin.subscribers.destroy');
    Route::post('/admin/subscribers/bulk-action', 'bulkAction')->name('admin.subscribers.bulk-action');
});

// Site Settings Routes
Route::middleware(['auth'])->prefix('admin')->group(function() {
    Route::get('/site-settings', [App\Http\Controllers\Backend\SiteSettingsController::class, 'index'])->name('admin.site-settings.index');
    Route::put('/site-settings', [App\Http\Controllers\Backend\SiteSettingsController::class, 'update'])->name('admin.site-settings.update');
});

// Comment Management Routes
Route::middleware(['auth'])->prefix('admin')->group(function() {
    Route::get('/comment-management', [App\Http\Controllers\Backend\CommentManagementController::class, 'index'])->name('admin.comment-management');
    Route::post('/comment-management/{id}/approve', [App\Http\Controllers\Backend\CommentManagementController::class, 'approve'])->name('admin.comment.approve');
    Route::post('/comment-management/{id}/reject', [App\Http\Controllers\Backend\CommentManagementController::class, 'reject'])->name('admin.comment.reject');
    Route::delete('/comment-management/{id}', [App\Http\Controllers\Backend\CommentManagementController::class, 'destroy'])->name('admin.comment.destroy');
    Route::post('/comment-management/bulk-action', [App\Http\Controllers\Backend\CommentManagementController::class, 'bulkAction'])->name('admin.comment.bulk-action');
    Route::get('/comment-management/{id}/show', [App\Http\Controllers\Backend\CommentManagementController::class, 'show'])->name('admin.comment.show');
});

// Banner all Route
Route::controller(BannerController::class)->group(function(){

    Route::get('/all/banners','AllBanners')->name('all.banners');
    Route::post('/update/banners','UpdateBanners')->name('update.banners');
   

});



// PhotoGallery all Route
Route::controller(PhotoGalleryController::class)->group(function(){

    Route::get('/all/photo/gallery','AllPhotoGallery')->name('all.photo.gallery');
    Route::get('/add/photo/gallery','AddPhotoGallery')->name('add.photo.gallery');
    Route::post('/store/photo/gallery','StorePhotoGallery')->name('store.photo.gallery');

    Route::get('/edit/photo/gallery/{id}','EditPhotoGallery')->name('edit.photo.gallery');

    Route::post('/update/photo/gallery','UpdatePhotoGallery')->name('update.photo.gallery');

    Route::get('/delete/photo/gallery/{id}','DeletePhotoGallery')->name('delete.photo.gallery');
     
   

});



// Video Gallery all Route
Route::controller(VideoGalleryController::class)->group(function(){

    Route::get('/all/video/gallery','AllVideoGallery')->name('all.video.gallery'); 

    Route::get('/add/video/gallery','AddVideoGallery')->name('add.video.gallery');

    Route::post('/store/video/gallery','StoreVideoGallery')->name('store.video.gallery');

     Route::get('/edit/video/gallery/{id}','EditVideoGallery')->name('edit.video.gallery');

     Route::post('/update/video/gallery','UpdateVideoGallery')->name('update.video.gallery');

     Route::get('/delete/video/gallery/{id}','DeleteVideoGallery')->name('delete.video.gallery');

     Route::get('/update/live/tv','UpdateLiveTv')->name('update.live.tv');
     Route::post('/update/live','UpdateLiveData')->name('update.live');

});



// Review all Route
Route::controller(ReviewController::class)->group(function(){

    Route::get('/pending/review','PendingReview')->name('pending.review');
    Route::get('/review/approve/{id}','ReviewApprove')->name('review.approve');
    Route::get('/approve/review','ApproveReview')->name('approve.review'); 
    Route::get('/delete/review/{id}','DeleteReview')->name('delete.review');
 
});


// Review all Route
Route::controller(SeoSettingController::class)->group(function(){

    Route::get('/seo/setting','SeoSiteSetting')->name('seo.setting');
    Route::post('/update/seo/setting','UpdateSeoSetting')->name('update.seo.setting');
 
});


// Permission all Route
Route::controller(RoleController::class)->group(function(){

    Route::get('/all/permission','AllPermission')->name('all.permission');
    Route::get('/add/permission','AddPermission')->name('add.permission');
    Route::post('/store/permission','StorePermission')->name('permission.store');
    Route::get('/edit/permission/{id}','EditPermission')->name('edit.permission');
    Route::post('/update/permission','UpdatePermission')->name('permission.update');
    Route::get('/delete/permission/{id}','DeletePermission')->name('delete.permission');
 
});


// Roles all Route
Route::controller(RoleController::class)->group(function(){

    Route::get('/all/roles','AllRoles')->name('all.roles');
    Route::get('/add/roles','AddRoles')->name('add.roles');
    Route::post('/store/roles','StoreRoles')->name('roles.store');
    Route::get('/edit/roles/{id}','EditRoles')->name('edit.roles');
    Route::post('/update/roles','UpdateRoles')->name('roles.update');
    Route::get('/delete/roles/{id}','DeleteRoles')->name('delete.roles');

    Route::get('/add/roles/permission','AddRolesPermission')->name('add.roles.permission');

    Route::post('/role/permission/store','RolePermisssionStore')->name('role.permission.store');

     Route::get('/all/roles/permission','AllRolesPermission')->name('all.roles.permission');

     Route::get('/admin/edit/roles/{id}','AdminEditRoles')->name('admin.edit.roles');

Route::get('/admin/delete/roles/{id}','AdminDeleteRoles')->name('admin.delete.roles');


     Route::post('/role/permission/update/{id}','RolePermissionUpdate')->name('role.permission.update');

});


// Post Approval Routes
Route::controller(PostApprovalController::class)->group(function(){
    Route::get('/admin/posts/pending','pendingPosts')->name('admin.posts.pending');
    Route::get('/admin/posts/approved','approvedPosts')->name('admin.posts.approved');
    Route::get('/admin/posts/rejected','rejectedPosts')->name('admin.posts.rejected');
    Route::get('/admin/posts/{id}/show','show')->name('admin.posts.show');
    Route::post('/admin/posts/{id}/approve','approve')->name('admin.posts.approve');
    Route::post('/admin/posts/{id}/reject','reject')->name('admin.posts.reject');
    Route::delete('/admin/posts/{id}/delete','deletePost')->name('admin.posts.delete');
    Route::post('/admin/posts/bulk-approve','bulkApprove')->name('admin.posts.bulk-approve');
    Route::post('/admin/posts/bulk-reject','bulkReject')->name('admin.posts.bulk-reject');
});

// Post Management Routes
Route::controller(PostManagementController::class)->group(function(){
    Route::get('/admin/posts/manage','index')->name('admin.posts.manage');
    Route::post('/admin/posts/{id}/toggle-pin','togglePin')->name('admin.posts.toggle-pin');
    Route::post('/admin/posts/{id}/toggle-featured','toggleFeatured')->name('admin.posts.toggle-featured');
    Route::post('/admin/posts/{id}/toggle-trending','toggleTrending')->name('admin.posts.toggle-trending');
    Route::post('/admin/posts/update-order','updateOrder')->name('admin.posts.update-order');
    Route::post('/admin/posts/update-single-order','updateSingleOrder')->name('admin.posts.update-single-order');
    Route::post('/admin/posts/bulk-action','bulkAction')->name('admin.posts.bulk-action');
    Route::get('/admin/posts/test-ajax','testAjax')->name('admin.posts.test-ajax');
});

// Advertisement Management Routes
Route::controller(AdvertisementController::class)->group(function(){
    Route::get('/admin/advertisements','index')->name('admin.advertisements.index');
    Route::get('/admin/advertisements/create','create')->name('admin.advertisements.create');
    Route::post('/admin/advertisements','store')->name('admin.advertisements.store');
    Route::get('/admin/advertisements/{advertisement}','show')->name('admin.advertisements.show');
    Route::get('/admin/advertisements/{advertisement}/edit','edit')->name('admin.advertisements.edit');
    Route::put('/admin/advertisements/{advertisement}','update')->name('admin.advertisements.update');
    Route::delete('/admin/advertisements/{advertisement}','destroy')->name('admin.advertisements.destroy');
    Route::post('/admin/advertisements/{advertisement}/toggle-status','toggleStatus')->name('admin.advertisements.toggle-status');
    Route::post('/admin/advertisements/update-order','updateOrder')->name('admin.advertisements.update-order');
    Route::get('/admin/advertisements/performance-stats','performanceStats')->name('admin.advertisements.performance-stats');
});

// Sponsored Ads Management Routes
Route::controller(SponsoredAdController::class)->group(function(){
    Route::get('/admin/sponsored-ads','index')->name('admin.sponsored-ads.index');
    Route::get('/admin/sponsored-ads/create','create')->name('admin.sponsored-ads.create');
    Route::post('/admin/sponsored-ads','store')->name('admin.sponsored-ads.store');
    Route::get('/admin/sponsored-ads/{sponsoredAd}','show')->name('admin.sponsored-ads.show');
    Route::get('/admin/sponsored-ads/{sponsoredAd}/edit','edit')->name('admin.sponsored-ads.edit');
    Route::put('/admin/sponsored-ads/{sponsoredAd}','update')->name('admin.sponsored-ads.update');
    Route::delete('/admin/sponsored-ads/{sponsoredAd}','destroy')->name('admin.sponsored-ads.destroy');
    Route::post('/admin/sponsored-ads/{sponsoredAd}/toggle-status','toggleStatus')->name('admin.sponsored-ads.toggle-status');
    Route::post('/admin/sponsored-ads/{sponsoredAd}/toggle-premium','togglePremium')->name('admin.sponsored-ads.toggle-premium');
    Route::post('/admin/sponsored-ads/update-order','updateOrder')->name('admin.sponsored-ads.update-order');
    Route::get('/admin/sponsored-ads/performance-stats','performanceStats')->name('admin.sponsored-ads.performance-stats');
});

 }); // End Admin Middleware


/// Access for All 
Route::get('/news/details/{id}/{slug}', [IndexController::class, 'NewsDetails']);
Route::get('/news/category/{id}/{slug}', [IndexController::class, 'CatWiseNews']);
Route::get('/news/subcategory/{id}/{slug}', [IndexController::class, 'SubCatWiseNews']);

Route::get('/lang/change', [IndexController::class, 'Change'])->name('changeLang');
Route::post('/change-language', [IndexController::class, 'ChangeLanguage'])->name('change.language');

// API Routes for dynamic loading with error handling
Route::get('/api/subcategories/{categoryId}', function($categoryId) {
    try {
        // Validate category ID
        if (!is_numeric($categoryId) || $categoryId <= 0) {
            return response()->json(['error' => 'Invalid category ID'], 400);
        }

        // Check if category exists
        $categoryExists = \App\Models\Category::where('id', $categoryId)->exists();
        if (!$categoryExists) {
            return response()->json(['error' => 'Category not found'], 404);
        }

        // Get subcategories with timeout protection
        $subcategories = \App\Models\Subcategory::where('category_id', $categoryId)
                                               ->select('id', 'subcategory_name')
                                               ->orderBy('subcategory_name', 'ASC')
                                               ->get();

        return response()->json($subcategories, 200, [
            'Cache-Control' => 'public, max-age=300', // 5 minutes cache
            'Content-Type' => 'application/json'
        ]);

    } catch (\Exception $e) {
        \Log::error('Subcategory API Error: ' . $e->getMessage());
        return response()->json(['error' => 'Server error occurred'], 500);
    }
})->name('api.subcategories');

// Search Routes
Route::get('/search', [IndexController::class, 'Search'])->name('search');
Route::post('/search', [IndexController::class, 'SearchByDate'])->name('search-by-date');

Route::post('/news', [IndexController::class, 'NewsSearch'])->name('news.search');

Route::get('/reporter/{id}', [IndexController::class, 'ReporterNews'])->name('reporter.all.news');

Route::post('/store/review', [ReviewController::class, 'StoreReview'])->name('store.review');


/// End Access for All 

