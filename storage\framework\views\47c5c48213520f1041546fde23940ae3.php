<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                                <i class="mdi mdi-format-list-bulleted"></i> Bulk Actions
                            </button>
                        </ol>
                    </div>
                    <h4 class="page-title">Post Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-pin text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($pinnedCount); ?></h5>
                                <p class="text-muted mb-0">Pinned Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-star text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($featuredCount); ?></h5>
                                <p class="text-muted mb-0">Featured Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-trending-up text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($trendingCount); ?></h5>
                                <p class="text-muted mb-0">Trending Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-file-document text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($totalPosts); ?></h5>
                                <p class="text-muted mb-0">Total Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Posts Display Order & Pinning</h4>
                        <p class="card-title-desc">Drag and drop to reorder posts, or use the action buttons to pin/feature posts.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <div class="search-box">
                                    <div class="position-relative">
                                        <input type="text" class="form-control" id="searchPosts" placeholder="Search by title, category, or author...">
                                        <i class="mdi mdi-magnify search-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="pinned">Pinned Only</option>
                                    <option value="featured">Featured Only</option>
                                    <option value="trending">Trending Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterCategory">
                                    <option value="">All Categories</option>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->category_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                                    <i class="mdi mdi-filter-remove me-1"></i>Clear
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover sortable-table" id="postsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th width="50">
                                            <i class="mdi mdi-drag-vertical text-muted"></i>
                                        </th>
                                        <th width="50">Order</th>
                                        <th>Post Details</th>
                                        <th>Author</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-posts">
                                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-post-id="<?php echo e($post->id); ?>" data-order="<?php echo e($post->display_order); ?>" 
                                        data-category-id="<?php echo e($post->category_id); ?>"
                                        class="post-row <?php echo e($post->is_pinned ? 'table-warning' : ''); ?>">
                                        <td>
                                            <input type="checkbox" class="form-check-input post-checkbox" value="<?php echo e($post->id); ?>">
                                        </td>
                                        <td>
                                            <i class="mdi mdi-drag-vertical text-muted drag-handle" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary order-badge"><?php echo e($post->display_order); ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($post->post_image): ?>
                                                    <img src="<?php echo e(asset($post->post_image)); ?>" alt="Post Image" class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                        <i class="mdi mdi-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e(Str::limit($post->post_title, 50)); ?></h6>
                                                    <div class="d-flex gap-1 mb-1">
                                                        <span class="badge bg-primary"><?php echo e($post->category->category_name); ?></span>
                                                        <?php if($post->subcategory): ?>
                                                            <span class="badge bg-secondary"><?php echo e($post->subcategory->subcategory_name); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <small class="text-muted"><?php echo e($post->created_at->format('M d, Y')); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo e($post->user->name); ?></td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <?php if($post->is_pinned): ?>
                                                    <span class="badge bg-warning"><i class="mdi mdi-pin"></i> Pinned</span>
                                                <?php endif; ?>
                                                <?php if($post->is_featured): ?>
                                                    <span class="badge bg-info"><i class="mdi mdi-star"></i> Featured</span>
                                                <?php endif; ?>
                                                <?php if($post->is_trending): ?>
                                                    <span class="badge bg-success"><i class="mdi mdi-trending-up"></i> Trending</span>
                                                <?php endif; ?>
                                                <?php if(!$post->is_pinned && !$post->is_featured && !$post->is_trending): ?>
                                                    <span class="badge bg-light text-dark">Normal</span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- Pin Button -->
                                                <button class="btn btn-sm <?php echo e($post->is_pinned ? 'btn-warning' : 'btn-outline-warning'); ?> pin-btn" 
                                                        data-post-id="<?php echo e($post->id); ?>" 
                                                        title="<?php echo e($post->is_pinned ? 'Unpin' : 'Pin'); ?>">
                                                    <i class="mdi mdi-pin"></i>
                                                </button>
                                                
                                                <!-- Feature Button -->
                                                <button class="btn btn-sm <?php echo e($post->is_featured ? 'btn-info' : 'btn-outline-info'); ?> feature-btn" 
                                                        data-post-id="<?php echo e($post->id); ?>" 
                                                        title="<?php echo e($post->is_featured ? 'Remove Featured' : 'Mark Featured'); ?>">
                                                    <i class="mdi mdi-star"></i>
                                                </button>
                                                
                                                <!-- Trending Button -->
                                                <button class="btn btn-sm <?php echo e($post->is_trending ? 'btn-success' : 'btn-outline-success'); ?> trending-btn" 
                                                        data-post-id="<?php echo e($post->id); ?>" 
                                                        title="<?php echo e($post->is_trending ? 'Remove Trending' : 'Mark Trending'); ?>">
                                                    <i class="mdi mdi-trending-up"></i>
                                                </button>
                                                
                                                <!-- Edit Button -->
                                                <a href="<?php echo e(route('edit.news.post', $post->id)); ?>"
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        
    </div> <!-- container -->
</div> <!-- content -->

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" aria-labelledby="bulkActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionModalLabel">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="bulkAction" class="form-label">Select Action</label>
                    <select class="form-select" id="bulkAction">
                        <option value="">Choose action...</option>
                        <option value="pin">Pin Selected Posts</option>
                        <option value="unpin">Unpin Selected Posts</option>
                        <option value="feature">Mark as Featured</option>
                        <option value="unfeature">Remove Featured</option>
                        <option value="trending">Mark as Trending</option>
                        <option value="untrending">Remove Trending</option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <strong><span id="selectedCount">0</span></strong> posts selected
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="executeBulkAction">Execute Action</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('Post Management Ready');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');
    
    // Initialize all functionality
    console.log('Starting initialization...');
    initializeSortable();
    initializeActionButtons();
    initializeFilters();
    
    // Test button click
    $('body').on('click', '.pin-btn', function() {
        console.log('Pin button clicked - event working!');
    });

    // Initialize sortable functionality
    function initializeSortable() {
        console.log('Initializing sortable...');

        var $table = $("#sortable-posts");
        console.log('Table found:', $table.length);

        if ($table.length === 0) {
            console.error('Sortable table not found');
            return;
        }

        try {
            $table.sortable({
                handle: ".drag-handle",
                items: "> tr",
                axis: "y",
                cursor: "move",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Drag started');
                    ui.placeholder.height(ui.item.height());
                    ui.item.addClass('dragging');
                },
                update: function(event, ui) {
                    console.log('Order updated');
                    updatePostOrder();
                },
                stop: function(event, ui) {
                    console.log('Drag stopped');
                    ui.item.removeClass('dragging');
                }
            });

            console.log('Sortable initialized successfully');

        } catch (error) {
            console.error('Sortable initialization error:', error);
        }
    }

    // Update post order after drag and drop
    function updatePostOrder() {
        var posts = [];
        $("#sortable-posts tr").each(function(index) {
            var postId = $(this).data('post-id');
            if (postId) {
                posts.push({
                    id: postId,
                    order: index
                });
                // Update the order badge
                $(this).find('.order-badge').text(index);
            }
        });

        console.log('Updating order for posts:', posts);

        $.ajax({
            url: '<?php echo e(route("admin.posts.update-order")); ?>',
            method: 'POST',
            data: {
                posts: posts,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            beforeSend: function() {
                $('.order-badge').addClass('bg-warning').removeClass('bg-secondary');
            },
            success: function(response) {
                console.log('Order update response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    $('.order-badge').addClass('bg-secondary').removeClass('bg-warning');
                }
            },
            error: function(xhr, status, error) {
                console.error('Order update error:', xhr.responseText);
                toastr.error('Failed to update post order');
                location.reload();
            }
        });
    }

    // Initialize action buttons
    function initializeActionButtons() {
        console.log('Initializing action buttons...');

        // Pin/Unpin functionality
        $(document).off('click', '.pin-btn').on('click', '.pin-btn', function(e) {
            e.preventDefault();
            console.log('Pin button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            console.log('Post ID:', postId);

            if (!postId) {
                toastr.error('No post ID found');
                return;
            }

            $.ajax({
                url: '<?php echo e(route("admin.posts.toggle-pin", ":id")); ?>'.replace(':id', postId),
                method: 'POST',
                data: { _token: '<?php echo e(csrf_token()); ?>' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Pin response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_pinned) {
                            btn.removeClass('btn-outline-warning').addClass('btn-warning');
                            btn.attr('title', 'Unpin');
                            btn.html('<i class="mdi mdi-pin"></i>');
                            row.addClass('table-warning');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Pinned")').length) {
                                statusCell.prepend('<span class="badge bg-warning me-1"><i class="mdi mdi-pin"></i> Pinned</span>');
                            }
                        } else {
                            btn.removeClass('btn-warning').addClass('btn-outline-warning');
                            btn.attr('title', 'Pin');
                            btn.html('<i class="mdi mdi-pin"></i>');
                            row.removeClass('table-warning');

                            // Remove pinned badge
                            row.find('.badge:contains("Pinned")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Pin error:', xhr.responseText);
                    toastr.error('Failed to toggle pin status');
                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-pin"></i>');
                }
            });
        });

        // Feature functionality
        $(document).off('click', '.feature-btn').on('click', '.feature-btn', function(e) {
            e.preventDefault();
            console.log('Feature button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            if (!postId) {
                toastr.error('No post ID found');
                return;
            }

            $.ajax({
                url: '<?php echo e(route("admin.posts.toggle-featured", ":id")); ?>'.replace(':id', postId),
                method: 'POST',
                data: { _token: '<?php echo e(csrf_token()); ?>' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Feature response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_featured) {
                            btn.removeClass('btn-outline-info').addClass('btn-info');
                            btn.attr('title', 'Remove Featured');
                            btn.html('<i class="mdi mdi-star"></i>');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Featured")').length) {
                                statusCell.append('<span class="badge bg-info me-1"><i class="mdi mdi-star"></i> Featured</span>');
                            }
                        } else {
                            btn.removeClass('btn-info').addClass('btn-outline-info');
                            btn.attr('title', 'Mark Featured');
                            btn.html('<i class="mdi mdi-star"></i>');

                            // Remove featured badge
                            row.find('.badge:contains("Featured")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Feature error:', xhr.responseText);
                    toastr.error('Failed to toggle featured status');
                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-star"></i>');
                }
            });
        });

        // Trending functionality
        $(document).off('click', '.trending-btn').on('click', '.trending-btn', function(e) {
            e.preventDefault();
            console.log('Trending button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            if (!postId) {
                toastr.error('No post ID found');
                return;
            }

            $.ajax({
                url: '<?php echo e(route("admin.posts.toggle-trending", ":id")); ?>'.replace(':id', postId),
                method: 'POST',
                data: { _token: '<?php echo e(csrf_token()); ?>' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Trending response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_trending) {
                            btn.removeClass('btn-outline-success').addClass('btn-success');
                            btn.attr('title', 'Remove Trending');
                            btn.html('<i class="mdi mdi-trending-up"></i>');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Trending")').length) {
                                statusCell.append('<span class="badge bg-success me-1"><i class="mdi mdi-trending-up"></i> Trending</span>');
                            }
                        } else {
                            btn.removeClass('btn-success').addClass('btn-outline-success');
                            btn.attr('title', 'Mark Trending');
                            btn.html('<i class="mdi mdi-trending-up"></i>');

                            // Remove trending badge
                            row.find('.badge:contains("Trending")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Trending error:', xhr.responseText);
                    toastr.error('Failed to toggle trending status');
                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-trending-up"></i>');
                }
            });
        });

        console.log('Action buttons initialized');
    }

    // Initialize filters
    function initializeFilters() {
        console.log('Initializing filters...');

        // Search functionality with debounce
        var searchTimeout;
        $('#searchPosts').off('input').on('input', function() {
            var searchTerm = $(this).val().toLowerCase().trim();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                console.log('Searching for:', searchTerm);

                var visibleCount = 0;
                $('#postsTable tbody tr').each(function() {
                    var $row = $(this);
                    var postTitle = $row.find('h6').text().toLowerCase();
                    var category = $row.find('.badge.bg-primary').text().toLowerCase();
                    var subcategory = $row.find('.badge.bg-secondary').text().toLowerCase();
                    var author = $row.find('td:nth-child(5)').text().toLowerCase();

                    var isVisible = searchTerm === '' ||
                                   postTitle.includes(searchTerm) ||
                                   category.includes(searchTerm) ||
                                   subcategory.includes(searchTerm) ||
                                   author.includes(searchTerm);

                    if (isVisible) {
                        $row.show();
                        visibleCount++;
                    } else {
                        $row.hide();
                    }
                });

                // Show search results count
                updateSearchResults(visibleCount);
            }, 300);
        });

        // Filter functionality
        $('#filterStatus, #filterCategory').off('change').on('change', function() {
            var statusFilter = $('#filterStatus').val();
            var categoryFilter = $('#filterCategory').val();
            var searchTerm = $('#searchPosts').val().toLowerCase().trim();

            console.log('Applying filters:', {statusFilter, categoryFilter, searchTerm});

            var visibleCount = 0;
            $('#postsTable tbody tr').each(function() {
                var $row = $(this);
                var show = true;

                // Search filter
                if (searchTerm !== '') {
                    var postTitle = $row.find('h6').text().toLowerCase();
                    var category = $row.find('.badge.bg-primary').text().toLowerCase();
                    var subcategory = $row.find('.badge.bg-secondary').text().toLowerCase();
                    var author = $row.find('td:nth-child(5)').text().toLowerCase();

                    show = show && (postTitle.includes(searchTerm) ||
                                   category.includes(searchTerm) ||
                                   subcategory.includes(searchTerm) ||
                                   author.includes(searchTerm));
                }

                // Status filter
                if (statusFilter === 'pinned') {
                    show = show && ($row.hasClass('table-warning') || $row.find('.badge:contains("Pinned")').length > 0);
                } else if (statusFilter === 'featured') {
                    show = show && $row.find('.badge:contains("Featured")').length > 0;
                } else if (statusFilter === 'trending') {
                    show = show && $row.find('.badge:contains("Trending")').length > 0;
                }

                // Category filter
                if (categoryFilter) {
                    var postCategoryId = $row.data('category-id');
                    show = show && (postCategoryId == categoryFilter);
                }

                if (show) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            updateSearchResults(visibleCount);
        });

        // Clear filters button
        $('#clearFilters').off('click').on('click', function() {
            $('#searchPosts').val('');
            $('#filterStatus').val('');
            $('#filterCategory').val('');
            $('#postsTable tbody tr').show();
            updateSearchResults($('#postsTable tbody tr').length);
        });

        console.log('Filters initialized');
    }

    // Update search results count
    function updateSearchResults(count) {
        var total = $('#postsTable tbody tr').length;
        var resultsText = count === total ?
            `Showing all ${total} posts` :
            `Showing ${count} of ${total} posts`;

        // Add or update results indicator
        if ($('#searchResults').length === 0) {
            $('#searchPosts').parent().after(`<small id="searchResults" class="text-muted">${resultsText}</small>`);
        } else {
            $('#searchResults').text(resultsText);
        }
    }

    // Update stats function
    function updateStats() {
        // For now, we'll just reload the page after a short delay
        setTimeout(function() {
            location.reload();
        }, 1000);
    }
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/post_management/index.blade.php ENDPATH**/ ?>