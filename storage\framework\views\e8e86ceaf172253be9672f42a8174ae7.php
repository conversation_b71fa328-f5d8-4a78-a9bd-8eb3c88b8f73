<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.advertisements.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to List
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Add New Advertisement</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title d-flex align-items-center">
                            <div><i class="mdi mdi-advertisement me-1 font-22 text-primary"></i></div>
                            <h5 class="mb-0 text-primary">Create New Advertisement</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <form method="POST" action="<?php echo e(route('admin.advertisements.store')); ?>" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            
                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Advertisement Title <span class="text-danger">*</span></label>
                                        <input type="text" name="title" class="form-control" id="title" value="<?php echo e(old('title')); ?>" required>
                                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea name="description" class="form-control" id="description" rows="4"><?php echo e(old('description')); ?></textarea>
                                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="link_url" class="form-label">Link URL</label>
                                        <input type="url" name="link_url" class="form-control" id="link_url" value="<?php echo e(old('link_url')); ?>" placeholder="https://example.com">
                                        <?php $__errorArgs = ['link_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="image" class="form-label">Advertisement Image</label>
                                        <input type="file" name="image" class="form-control" id="image" accept="image/*">
                                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="text-muted">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</small>
                                    </div>
                                </div>

                                <!-- Settings -->
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ad_type" class="form-label">Advertisement Type <span class="text-danger">*</span></label>
                                        <select name="ad_type" class="form-select" id="ad_type" required>
                                            <option value="">Select Type</option>
                                            <option value="banner" <?php echo e(old('ad_type') == 'banner' ? 'selected' : ''); ?>>Banner</option>
                                            <option value="sidebar" <?php echo e(old('ad_type') == 'sidebar' ? 'selected' : ''); ?>>Sidebar</option>
                                            <option value="popup" <?php echo e(old('ad_type') == 'popup' ? 'selected' : ''); ?>>Popup</option>
                                            <option value="inline" <?php echo e(old('ad_type') == 'inline' ? 'selected' : ''); ?>>Inline</option>
                                        </select>
                                        <?php $__errorArgs = ['ad_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="position" class="form-label">Position <span class="text-danger">*</span></label>
                                        <select name="position" class="form-select" id="position" required>
                                            <option value="">Select Position</option>
                                            <option value="top" <?php echo e(old('position') == 'top' ? 'selected' : ''); ?>>Top</option>
                                            <option value="middle" <?php echo e(old('position') == 'middle' ? 'selected' : ''); ?>>Middle</option>
                                            <option value="bottom" <?php echo e(old('position') == 'bottom' ? 'selected' : ''); ?>>Bottom</option>
                                            <option value="left" <?php echo e(old('position') == 'left' ? 'selected' : ''); ?>>Left</option>
                                            <option value="right" <?php echo e(old('position') == 'right' ? 'selected' : ''); ?>>Right</option>
                                            <option value="header" <?php echo e(old('position') == 'header' ? 'selected' : ''); ?>>Header</option>
                                            <option value="footer" <?php echo e(old('position') == 'footer' ? 'selected' : ''); ?>>Footer</option>
                                        </select>
                                        <?php $__errorArgs = ['position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="display_order" class="form-label">Display Order</label>
                                        <input type="number" name="display_order" class="form-control" id="display_order" value="<?php echo e(old('display_order', 0)); ?>" min="0">
                                        <?php $__errorArgs = ['display_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="text-muted">Leave 0 for automatic ordering</small>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                Active Advertisement
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Scheduling -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="date" name="start_date" class="form-control" id="start_date" value="<?php echo e(old('start_date')); ?>">
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="date" name="end_date" class="form-control" id="end_date" value="<?php echo e(old('end_date')); ?>">
                                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Targeting -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="target_pages" class="form-label">Target Pages</label>
                                        <select name="target_pages[]" class="form-select" id="target_pages" multiple>
                                            <option value="homepage">Homepage</option>
                                            <option value="category">Category Pages</option>
                                            <option value="news_details">News Details</option>
                                            <option value="all">All Pages</option>
                                        </select>
                                        <small class="text-muted">Hold Ctrl to select multiple pages</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="target_categories" class="form-label">Target Categories</label>
                                        <select name="target_categories[]" class="form-select" id="target_categories" multiple>
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($category->id); ?>"><?php echo e($category->category_name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <small class="text-muted">Hold Ctrl to select multiple categories</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary px-5">
                                    <i class="mdi mdi-content-save"></i> Create Advertisement
                                </button>
                                <button type="reset" class="btn btn-secondary px-5">
                                    <i class="mdi mdi-refresh"></i> Reset Form
                                </button>
                                <a href="<?php echo e(route('admin.advertisements.index')); ?>" class="btn btn-outline-secondary px-5">
                                    <i class="mdi mdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/advertisements/create.blade.php ENDPATH**/ ?>