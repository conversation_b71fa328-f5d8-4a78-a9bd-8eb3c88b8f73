@extends('frontend.layout_facebook_style')

@section('title')
{{ \App\Models\SiteSetting::get('site_title', 'NitiKotha - Latest News & Updates') }} (Facebook Style)
@endsection

@section('content')

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                @php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                @endphp
                <img src="{{ $logoPath }}" alt="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}">
            </div>
            <div class="fb-profile-info">
                <h3 class="fb-profile-name">NitiKotha</h3>
                <p class="fb-profile-tagline">Premium News Portal</p>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="fb-nav-menu">
            <a href="{{ url('/') }}" class="fb-nav-item active">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-fire"></i>
                <span>Trending</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-calendar"></i>
                <span>Events</span>
            </a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                @if(isset($categories))
                    @foreach($categories as $category)
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category" data-category-id="{{ $category->id }}">
                            <div class="fb-category-left">
                                <div class="fb-category-icon">{{ $category->category_icon }}</div>
                                <span class="fb-category-name">{{ $category->category_name }}</span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count">{{ $category->news_posts_count ?? 0 }}</span>
                                @if($category->subcategories && $category->subcategories->count() > 0)
                                    <button class="fb-category-toggle" data-target="subcats-{{ $category->id }}">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="{{ url('v2/news/category/'.$category->id.'/'.$category->category_slug) }}" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        @if($category->subcategories && $category->subcategories->count() > 0)
                        <div class="fb-subcategories" id="subcats-{{ $category->id }}">
                            @foreach($category->subcategories as $subcategory)
                            <a href="{{ url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug) }}" class="fb-subcategory-item">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name">{{ $subcategory->subcategory_name }}</span>
                                <span class="fb-subcategory-count">{{ $subcategory->news_posts_count ?? 0 }}</span>
                            </a>
                            @endforeach
                        </div>
                        @endif
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- Stories Section -->
        <div class="fb-stories-container">
            <div class="fb-stories-wrapper">
                <!-- Create Story -->
                <div class="fb-story-card fb-create-story">
                    <div class="fb-story-image">
                        @php
                            $siteLogo = \App\Models\SiteSetting::get('site_logo');
                            $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                        @endphp
                        <img src="{{ $logoPath }}" alt="Create Story">
                    </div>
                    <div class="fb-story-content">
                        <div class="fb-story-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <span class="fb-story-text">Create Story</span>
                    </div>
                </div>

                <!-- News Stories from Top Slider -->
                @if(isset($news_slider) && $news_slider->count() > 0)
                    @foreach($news_slider as $story)
                    <div class="fb-story-card" data-story-id="{{ $story->id }}">
                        <div class="fb-story-image">
                            <img src="{{ asset($story->image) }}" alt="{{ $story->news_title }}" loading="lazy">
                        </div>
                        <div class="fb-story-overlay"></div>
                        <div class="fb-story-content">
                            <div class="fb-story-avatar">
                                @php
                                    $photoPath = 'upload/no_image.jpg';
                                    if (!empty($story->user->photo)) {
                                        // Check if photo already contains path (starts with 'upload/')
                                        if (str_starts_with($story->user->photo, 'upload/')) {
                                            // Photo already contains full path (new format)
                                            $photoPath = $story->user->photo;
                                        } else {
                                            // Photo contains only filename (old format) - assume admin
                                            $photoPath = 'upload/admin_images/' . $story->user->photo;
                                        }
                                    }
                                @endphp
                                <img src="{{ url($photoPath) }}" alt="{{ $story->user->name ?? 'NitiKotha' }}">
                            </div>
                            <span class="fb-story-title">{{ Str::limit($story->news_title, 35) }}</span>
                            <div class="fb-story-meta">
                                <span class="fb-story-category">{{ $story->category->category_name }}</span>
                                <span class="fb-story-time">{{ $story->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                        <a href="{{ url('v2/news/details/'.$story->id.'/'.$story->news_title_slug) }}" class="fb-story-link"></a>
                    </div>
                    @endforeach
                @else
                    <!-- Fallback stories if no top slider posts -->
                    @if(isset($newnewspost))
                        @foreach($newnewspost->take(6) as $story)
                        <div class="fb-story-card" data-story-id="{{ $story->id }}">
                            <div class="fb-story-image">
                                <img src="{{ asset($story->image) }}" alt="{{ $story->news_title }}" loading="lazy">
                            </div>
                            <div class="fb-story-overlay"></div>
                            <div class="fb-story-content">
                                <div class="fb-story-avatar">
                                    @php
                                        $photoPath = 'upload/no_image.jpg';
                                        if (!empty($story->user->photo)) {
                                            // Check if photo already contains path (starts with 'upload/')
                                            if (str_starts_with($story->user->photo, 'upload/')) {
                                                // Photo already contains full path (new format)
                                                $photoPath = $story->user->photo;
                                            } else {
                                                // Photo contains only filename (old format) - assume admin
                                                $photoPath = 'upload/admin_images/' . $story->user->photo;
                                            }
                                        }
                                    @endphp
                                    <img src="{{ url($photoPath) }}" alt="{{ $story->user->name ?? 'NitiKotha' }}">
                                </div>
                                <span class="fb-story-title">{{ Str::limit($story->news_title, 35) }}</span>
                                <div class="fb-story-meta">
                                    <span class="fb-story-category">{{ $story->category->category_name }}</span>
                                    <span class="fb-story-time">{{ $story->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                            <a href="{{ url('v2/news/details/'.$story->id.'/'.$story->news_title_slug) }}" class="fb-story-link"></a>
                        </div>
                        @endforeach
                    @endif
                @endif
            </div>
        </div>

        <!-- Create Post Section -->
        <div class="fb-create-post">
            <div class="fb-create-post-header">
                <div class="fb-create-avatar">
                    @php
                        $siteLogo = \App\Models\SiteSetting::get('site_logo');
                        $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                    @endphp
                    <img src="{{ $logoPath }}" alt="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}">
                </div>
                <div class="fb-create-input">
                    <input type="text" placeholder="What's happening in the news today?" readonly>
                </div>
            </div>
            <div class="fb-create-post-actions">
                <button class="fb-action-btn">
                    <i class="fas fa-video"></i>
                    <span>Live Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-image"></i>
                    <span>Photo/Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-smile"></i>
                    <span>Feeling/Activity</span>
                </button>
            </div>
        </div>

        <!-- Top Homepage Advertisements -->
        @if(isset($sponsoredAds['homepage_top']) && $sponsoredAds['homepage_top']->count() > 0)
            @foreach($sponsoredAds['homepage_top'] as $ad)
                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'top'])
            @endforeach
        @endif

        <!-- News Feed Posts -->
        <div class="fb-news-feed" id="news-feed-container">
            @if(isset($newnewspost))
                @foreach($newnewspost as $index => $news)

                    <!-- Insert Middle Advertisement after 3rd post -->
                    @if($index == 3 && isset($sponsoredAds['homepage_middle']) && $sponsoredAds['homepage_middle']->count() > 0)
                        @foreach($sponsoredAds['homepage_middle'] as $ad)
                            @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'middle'])
                        @endforeach
                    @endif
                <article class="fb-post">
                    <!-- Post Header -->
                    <div class="fb-post-header">
                        <div class="fb-post-avatar">
                            @php
                                $photoPath = 'upload/no_image.jpg';
                                if (!empty($news->user->photo)) {
                                    // Check if photo already contains path (starts with 'upload/')
                                    if (str_starts_with($news->user->photo, 'upload/')) {
                                        // Photo already contains full path (new format)
                                        $photoPath = $news->user->photo;
                                    } else {
                                        // Photo contains only filename (old format) - assume admin
                                        $photoPath = 'upload/admin_images/' . $news->user->photo;
                                    }
                                }
                            @endphp
                            <img src="{{ url($photoPath) }}" alt="{{ $news->user->name }}">
                        </div>
                        <div class="fb-post-info">
                            <h4 class="fb-post-author">{{ $news->user->name ?? 'NitiKotha' }}</h4>
                            <div class="fb-post-meta">
                                <span class="fb-post-time">{{ $news->created_at->diffForHumans() }}</span>
                                <span class="fb-post-separator">·</span>
                                <span class="fb-post-category">{{ $news->category->category_name }}</span>
                                <span class="fb-post-separator">·</span>
                                <i class="fas fa-globe-americas"></i>
                            </div>
                        </div>
                        <div class="fb-post-options">
                            <button class="fb-options-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="fb-post-content">
                        <h3 class="fb-post-title">
                            <a href="{{ url('v2/news/details/'.$news->id.'/'.$news->news_title_slug) }}">
                                {{ $news->news_title }}
                            </a>
                        </h3>
                        <p class="fb-post-excerpt">
                            {{ Str::limit(strip_tags($news->news_details), 200) }}
                        </p>
                    </div>

                    <!-- Post Image -->
                    @if($news->image)
                    <div class="fb-post-image">
                        <img src="{{ asset($news->image) }}" alt="{{ $news->news_title }}">
                        <a href="{{ url('v2/news/details/'.$news->id.'/'.$news->news_title_slug) }}" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    @endif

                    <!-- Post Stats -->
                    <div class="fb-post-stats">
                        <div class="fb-post-reactions">
                            <div class="fb-reaction-icons">
                                <span class="fb-reaction like">👍</span>
                                <span class="fb-reaction love">❤️</span>
                                <span class="fb-reaction wow">😮</span>
                            </div>
                            <span class="fb-reaction-count" id="likes-count-{{ $news->id }}">{{ $news->likes_count ?? 0 }}</span>
                        </div>
                        <div class="fb-post-engagement">
                            <span class="fb-comments-count" id="comments-count-{{ $news->id }}">{{ $news->comments_count ?? 0 }} comments</span>
                            <span class="fb-shares-count" id="shares-count-{{ $news->id }}">{{ $news->shares_count ?? 0 }} shares</span>
                        </div>
                    </div>

                    <!-- Post Actions -->
                    <div class="fb-post-actions">
                        <button class="fb-action-btn fb-like-btn {{ $news->user_liked ? 'liked' : '' }}" data-news-id="{{ $news->id }}">
                            <i class="fas fa-thumbs-up"></i>
                            <span>{{ $news->user_liked ? 'Liked' : 'Like' }}</span>
                        </button>
                        <button class="fb-action-btn fb-comment-btn" data-news-id="{{ $news->id }}">
                            <i class="fas fa-comment"></i>
                            <span>Comment</span>
                        </button>
                        <button class="fb-action-btn fb-share-btn" data-news-id="{{ $news->id }}">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                        <button class="fb-action-btn fb-save-btn {{ $news->user_saved ? 'saved' : '' }}" data-news-id="{{ $news->id }}">
                            <i class="fas fa-bookmark"></i>
                            <span>{{ $news->user_saved ? 'Saved' : 'Save' }}</span>
                        </button>
                    </div>
                </article>
                @endforeach
            @endif

            <!-- Load More Button -->
            <div class="fb-load-more-container text-center mt-4" id="load-more-container">
                <button class="btn btn-primary btn-lg" id="load-more-btn">
                    <i class="fas fa-plus-circle me-2"></i>
                    Load More Posts
                </button>
                <div class="fb-loading-spinner" id="loading-spinner" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading more posts...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Sponsored Section -->
        <div class="fb-sponsored-section">
            <h4 class="fb-section-title">Sponsored</h4>
            <div class="fb-sponsored-item">
                <div class="fb-sponsored-image">
                    @php
                        $siteLogo = \App\Models\SiteSetting::get('site_logo');
                        $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                    @endphp
                    <img src="{{ $logoPath }}" alt="Sponsored">
                </div>
                <div class="fb-sponsored-content">
                    <h5 class="fb-sponsored-title">{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }} Premium</h5>
                    <p class="fb-sponsored-text">Get unlimited access to premium news content</p>
                </div>
            </div>
        </div>

        <!-- Trending Topics -->
        <div class="fb-trending-section">
            <h4 class="fb-section-title">
                <i class="fas fa-fire text-danger me-2"></i>
                Trending Topics
            </h4>
            <div class="fb-trending-list">
                @if(isset($trendingPosts) && $trendingPosts->count() > 0)
                    @foreach($trendingPosts->take(5) as $index => $trending)
                    <div class="fb-trending-item">
                        <div class="fb-trending-rank">{{ $index + 1 }}</div>
                        <div class="fb-trending-content">
                            <h5 class="fb-trending-title">
                                <a href="{{ url('v2/news/details/'.$trending->id.'/'.$trending->news_title_slug) }}">
                                    {{ Str::limit($trending->news_title, 60) }}
                                </a>
                            </h5>
                            <p class="fb-trending-meta">
                                <i class="fas fa-fire text-danger me-1"></i>
                                {{ $trending->view_count ?? 0 }} people talking about this
                            </p>
                        </div>
                    </div>
                    @endforeach
                @elseif(isset($newspopular))
                    @foreach($newspopular->take(5) as $index => $trending)
                    <div class="fb-trending-item">
                        <div class="fb-trending-rank">{{ $index + 1 }}</div>
                        <div class="fb-trending-content">
                            <h5 class="fb-trending-title">
                                <a href="{{ url('v2/news/details/'.$trending->id.'/'.$trending->news_title_slug) }}">
                                    {{ Str::limit($trending->news_title, 60) }}
                                </a>
                            </h5>
                            <p class="fb-trending-meta">{{ $trending->view_count ?? 0 }} people talking about this</p>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Featured Posts Section -->
        @if(isset($featuredPosts) && $featuredPosts->count() > 0)
        <div class="fb-featured-section mt-4">
            <h4 class="fb-section-title">
                <i class="fas fa-star text-warning me-2"></i>
                Featured Posts
            </h4>
            <div class="fb-featured-list">
                @foreach($featuredPosts->take(3) as $featured)
                <div class="fb-featured-item">
                    <div class="fb-featured-image">
                        <img src="{{ asset($featured->image) }}" alt="{{ $featured->news_title }}" loading="lazy">
                        <div class="fb-featured-badge">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <div class="fb-featured-content">
                        <h6 class="fb-featured-title">
                            <a href="{{ url('v2/news/details/'.$featured->id.'/'.$featured->news_title_slug) }}">
                                {{ Str::limit($featured->news_title, 50) }}
                            </a>
                        </h6>
                        <p class="fb-featured-meta">
                            <span class="text-muted">{{ $featured->category->category_name }}</span>
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Pinned Posts Section -->
        @if(isset($pinnedPosts) && $pinnedPosts->count() > 0)
        <div class="fb-pinned-section mt-4">
            <h4 class="fb-section-title">
                <i class="fas fa-thumbtack text-info me-2"></i>
                Pinned Posts
            </h4>
            <div class="fb-pinned-list">
                @foreach($pinnedPosts->take(3) as $pinned)
                <div class="fb-pinned-item">
                    <div class="fb-pinned-icon">
                        <i class="fas fa-thumbtack text-info"></i>
                    </div>
                    <div class="fb-pinned-content">
                        <h6 class="fb-pinned-title">
                            <a href="{{ url('v2/news/details/'.$pinned->id.'/'.$pinned->news_title_slug) }}">
                                {{ Str::limit($pinned->news_title, 60) }}
                            </a>
                        </h6>
                        <p class="fb-pinned-meta">
                            <span class="text-muted">Pinned {{ $pinned->pinned_at ? $pinned->pinned_at->diffForHumans() : 'recently' }}</span>
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Sidebar Advertisements -->
        @if(isset($sponsoredAds['sidebar']) && $sponsoredAds['sidebar']->count() > 0)
            @foreach($sponsoredAds['sidebar'] as $ad)
                @include('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'sidebar'])
            @endforeach
        @endif

        <!-- Online Friends -->
        <div class="fb-contacts-section">
            <h4 class="fb-section-title">Contacts</h4>
            <div class="fb-contacts-list">
                @php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                @endphp
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="{{ $logoPath }}" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">News Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="{{ $logoPath }}" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">Editorial Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="{{ $logoPath }}" alt="Contact">
                    </div>
                    <span class="fb-contact-name">Support Team</span>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    let currentPage = 2; // Start from page 2 since page 1 is already loaded
    let isLoading = false;
    let hasMorePosts = true;

    // Load More Button Click
    $('#load-more-btn').on('click', function() {
        loadMorePosts();
    });

    // Infinite Scroll (optional - triggers when user scrolls near bottom)
    $(window).on('scroll', function() {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 1000) {
            if (!isLoading && hasMorePosts) {
                loadMorePosts();
            }
        }
    });

    function loadMorePosts() {
        if (isLoading || !hasMorePosts) return;

        isLoading = true;
        $('#load-more-btn').hide();
        $('#loading-spinner').show();

        $.ajax({
            url: '{{ route("v2.load-more-posts") }}',
            method: 'GET',
            data: {
                page: currentPage
            },
            success: function(response) {
                if (response.success && response.posts.length > 0) {
                    // Append new posts to the feed
                    response.posts.forEach(function(post) {
                        const postHtml = createPostHtml(post);
                        $('#news-feed-container').append(postHtml);
                    });

                    currentPage = response.nextPage;
                    hasMorePosts = response.hasMore;

                    if (!hasMorePosts) {
                        $('#load-more-container').html('<p class="text-muted">No more posts to load.</p>');
                    }
                } else {
                    hasMorePosts = false;
                    $('#load-more-container').html('<p class="text-muted">No more posts to load.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading more posts:', error);
                $('#load-more-container').html('<p class="text-danger">Error loading posts. Please try again.</p>');
            },
            complete: function() {
                isLoading = false;
                $('#loading-spinner').hide();
                if (hasMorePosts) {
                    $('#load-more-btn').show();
                }
            }
        });
    }

    function createPostHtml(post) {
        // Determine user photo path
        let photoPath = '{{ asset("upload/no_image.jpg") }}';
        if (post.user && post.user.photo) {
            // Check if photo already contains path (starts with 'upload/')
            if (post.user.photo.startsWith('upload/')) {
                // Photo already contains full path (new format)
                photoPath = '{{ url("") }}/' + post.user.photo;
            } else {
                // Photo contains only filename (old format) - assume admin
                photoPath = '{{ url("upload/admin_images") }}/' + post.user.photo;
            }
        }

        const postUrl = `{{ url('v2/news/details') }}/${post.id}/${post.news_title_slug}`;
        const imageHtml = post.image ? `
            <div class="fb-post-image">
                <img src="{{ url('') }}/${post.image}" alt="${post.news_title}">
                <a href="${postUrl}" class="fb-image-overlay">
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>
        ` : '';

        return `
            <article class="fb-post">
                <!-- Post Header -->
                <div class="fb-post-header">
                    <div class="fb-post-avatar">
                        <img src="${photoPath}" alt="${post.user ? post.user.name : 'User'}">
                    </div>
                    <div class="fb-post-info">
                        <h4 class="fb-post-author">${post.user ? post.user.name : 'NitiKotha'}</h4>
                        <div class="fb-post-meta">
                            <span class="fb-post-time">${formatDate(post.created_at)}</span>
                            <span class="fb-post-separator">·</span>
                            <span class="fb-post-category">${post.category ? post.category.category_name : 'News'}</span>
                            <span class="fb-post-separator">·</span>
                            <i class="fas fa-globe-americas"></i>
                        </div>
                    </div>
                    <div class="fb-post-options">
                        <button class="fb-options-btn">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- Post Content -->
                <div class="fb-post-content">
                    <h3 class="fb-post-title">
                        <a href="${postUrl}">
                            ${post.news_title}
                        </a>
                    </h3>
                    <p class="fb-post-excerpt">
                        ${post.news_details ? post.news_details.replace(/<[^>]*>/g, '').substring(0, 200) + '...' : ''}
                    </p>
                </div>

                ${imageHtml}

                <!-- Post Stats -->
                <div class="fb-post-stats">
                    <div class="fb-stats-left">
                        <span class="fb-stat-item">
                            <i class="fas fa-thumbs-up text-primary"></i>
                            <span class="fb-stat-count">${post.likes_count || 0}</span>
                        </span>
                    </div>
                    <div class="fb-stats-right">
                        <span class="fb-stat-item">${post.comments_count || 0} comments</span>
                        <span class="fb-stat-item">${post.shares_count || 0} shares</span>
                    </div>
                </div>

                <!-- Post Actions -->
                <div class="fb-post-actions">
                    <button class="fb-action-btn like-btn" data-post-id="${post.id}">
                        <i class="far fa-thumbs-up"></i>
                        <span>Like</span>
                    </button>
                    <button class="fb-action-btn comment-btn">
                        <i class="far fa-comment"></i>
                        <span>Comment</span>
                    </button>
                    <button class="fb-action-btn share-btn" data-post-id="${post.id}">
                        <i class="far fa-share"></i>
                        <span>Share</span>
                    </button>
                    <a href="${postUrl}" class="fb-action-btn read-btn">
                        <i class="far fa-eye"></i>
                        <span>Read More</span>
                    </a>
                </div>
            </article>
        `;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + ' days ago';

        return date.toLocaleDateString();
    }

    // Advertisement tracking functions
    window.trackAdView = function(adId) {
        fetch('/api/ads/track-view', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Ad view tracking failed:', error));
    };

    window.trackAdClick = function(adId) {
        fetch('/api/ads/track-click', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Ad click tracking failed:', error));
    };

    window.hideAd = function(adId) {
        const adElement = document.querySelector(`[data-ad-id="${adId}"]`);
        if (adElement) {
            adElement.style.display = 'none';
            // Track ad hide action
            fetch('/api/ads/track-hide', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ad_id: adId })
            }).catch(error => console.log('Ad hide tracking failed:', error));
        }
    };

    window.reportAd = function(adId) {
        if (confirm('Report this advertisement as inappropriate?')) {
            fetch('/api/ads/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ad_id: adId })
            }).then(response => {
                if (response.ok) {
                    alert('Advertisement reported successfully. Thank you for your feedback.');
                }
            }).catch(error => console.log('Ad report failed:', error));
        }
    };
});
</script>
@endsection
