<?php $__env->startSection('admin'); ?>

<div class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Subscriber Management</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><i class="bx bx-home-alt"></i></a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.subscribers.index')); ?>">Subscribers</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo e($subscriber->name); ?></li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <div class="btn-group">
                <a href="<?php echo e(route('admin.subscribers.index')); ?>" class="btn btn-secondary">
                    <i class="mdi mdi-arrow-left"></i> Back to List
                </a>
                <a href="<?php echo e(route('admin.subscribers.edit', $subscriber->id)); ?>" class="btn btn-warning">
                    <i class="mdi mdi-pencil"></i> Edit
                </a>
                <form method="POST" action="<?php echo e(route('admin.subscribers.toggle-status', $subscriber->id)); ?>" class="d-inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PATCH'); ?>
                    <button type="submit" class="btn <?php echo e($subscriber->status === 'active' ? 'btn-secondary' : 'btn-success'); ?>">
                        <i class="mdi <?php echo e($subscriber->status === 'active' ? 'mdi-pause' : 'mdi-play'); ?>"></i>
                        <?php echo e($subscriber->status === 'active' ? 'Deactivate' : 'Activate'); ?>

                    </button>
                </form>
            </div>
        </div>
    </div>
    <!--end breadcrumb-->

    <div class="row">
        <!-- Subscriber Profile -->
        <div class="col-xl-4">
            <div class="card border-top border-0 border-4 border-info">
                <div class="card-body">
                    <div class="text-center">
                        <img src="<?php echo e((!empty($subscriber->photo)) ? url('upload/user_images/'.$subscriber->photo) : url('upload/no_image.jpg')); ?>" 
                             alt="<?php echo e($subscriber->name); ?>" class="rounded-circle shadow" width="120" height="120">
                        <h5 class="mt-3 mb-1"><?php echo e($subscriber->name); ?></h5>
                        <p class="text-muted"><?php echo e($subscriber->email); ?></p>
                        <div class="d-flex justify-content-center gap-2 mb-3">
                            <span class="badge <?php echo e($subscriber->status === 'active' ? 'bg-success' : 'bg-secondary'); ?> px-3 py-2">
                                <?php echo e(ucfirst($subscriber->status)); ?>

                            </span>
                            <span class="badge bg-info px-3 py-2"><?php echo e($subscriber->getRoleNames()->first()); ?></span>
                        </div>
                    </div>

                    <hr>

                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo e($subscriber->total_posts); ?></h4>
                            <p class="text-muted mb-0">Total Posts</p>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($subscriber->approved_posts); ?></h4>
                            <p class="text-muted mb-0">Approved</p>
                        </div>
                    </div>

                    <hr>

                    <div class="contact-info">
                        <h6 class="text-primary">Contact Information</h6>
                        <?php if($subscriber->phone): ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-phone me-2 text-muted"></i>
                            <span><?php echo e($subscriber->phone); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if($subscriber->address): ?>
                        <div class="d-flex align-items-start mb-2">
                            <i class="mdi mdi-map-marker me-2 text-muted mt-1"></i>
                            <span><?php echo e($subscriber->address); ?></span>
                        </div>
                        <?php endif; ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-calendar me-2 text-muted"></i>
                            <span>Joined <?php echo e($subscriber->created_at->format('F d, Y')); ?></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="mdi mdi-clock me-2 text-muted"></i>
                            <span>Last updated <?php echo e($subscriber->updated_at->diffForHumans()); ?></span>
                        </div>
                    </div>

                    <?php if($subscriber->bio): ?>
                    <hr>
                    <div class="bio-section">
                        <h6 class="text-primary">Bio</h6>
                        <p class="text-muted"><?php echo e($subscriber->bio); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics and Recent Posts -->
        <div class="col-xl-8">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?php echo e($subscriber->total_posts); ?></h3>
                            <p class="mb-0">Total Posts</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?php echo e($subscriber->approved_posts); ?></h3>
                            <p class="mb-0">Approved</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?php echo e($subscriber->pending_posts); ?></h3>
                            <p class="mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3 class="mb-1"><?php echo e($subscriber->rejected_posts); ?></h3>
                            <p class="mb-0">Rejected</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0">Recent Posts</h6>
                        <div class="ms-auto">
                            <a href="<?php echo e(route('admin.posts.pending')); ?>" class="btn btn-sm btn-outline-primary">
                                View All Posts
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($recentPosts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($post->image): ?>
                                            <img src="<?php echo e(asset($post->image)); ?>" alt="<?php echo e($post->news_title); ?>" 
                                                 class="rounded me-2" width="40" height="40">
                                            <?php endif; ?>
                                            <div>
                                                <h6 class="mb-0 font-14"><?php echo e(Str::limit($post->news_title, 50)); ?></h6>
                                                <small class="text-muted"><?php echo e($post->view_count ?? 0); ?> views</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark"><?php echo e($post->category->category_name ?? 'N/A'); ?></span>
                                    </td>
                                    <td>
                                        <?php
                                            $statusColors = [
                                                'approved' => 'success',
                                                'pending' => 'warning',
                                                'rejected' => 'danger'
                                            ];
                                        ?>
                                        <span class="badge bg-<?php echo e($statusColors[$post->approval_status] ?? 'secondary'); ?>">
                                            <?php echo e(ucfirst($post->approval_status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo e($post->created_at->format('M d, Y')); ?></small>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="<?php echo e(route('admin.posts.show', $post->id)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.posts.show', $post->id)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="mdi mdi-file-document-outline display-4 text-muted"></i>
                        <h6 class="text-muted mt-2">No posts found</h6>
                        <p class="text-muted">This subscriber hasn't created any posts yet.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/subscriber_management/show.blade.php ENDPATH**/ ?>