<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SponsoredAd;
use App\Models\Category;

class SponsoredAdController extends Controller
{
    /**
     * Display a listing of sponsored ads
     */
    public function index()
    {
        $sponsoredAds = SponsoredAd::ordered()->paginate(15);

        $stats = [
            'total' => SponsoredAd::count(),
            'active' => SponsoredAd::active()->count(),
            'premium' => SponsoredAd::premium()->count(),
            'native' => SponsoredAd::byFormat('native')->count(),
        ];

        return view('backend.sponsored_ads.index', compact('sponsoredAds', 'stats'));
    }

    /**
     * Show the form for creating a new sponsored ad
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->get();
        return view('backend.sponsored_ads.create', compact('categories'));
    }

    /**
     * Store a newly created sponsored ad
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'sponsor_name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sponsor_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'link_url' => 'nullable|url',
            'ad_format' => 'required|in:native,display,video,carousel',
            'placement' => 'required|in:homepage_top,homepage_middle,homepage_bottom,category_top,article_top,article_middle,article_bottom,sidebar',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget' => 'nullable|numeric|min:0',
            'target_categories' => 'nullable|array',
        ]);

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('upload/sponsored_ads'), $imageName);
            $data['image'] = 'upload/sponsored_ads/' . $imageName;
        }

        // Handle sponsor logo upload
        if ($request->hasFile('sponsor_logo')) {
            $logo = $request->file('sponsor_logo');
            $logoName = time() . '_logo_' . $logo->getClientOriginalName();
            $logo->move(public_path('upload/sponsored_ads'), $logoName);
            $data['sponsor_logo'] = 'upload/sponsored_ads/' . $logoName;
        }

        // Set default display order if not provided
        if (!$data['display_order']) {
            $maxOrder = SponsoredAd::where('placement', $data['placement'])->max('display_order');
            $data['display_order'] = $maxOrder + 1;
        }

        SponsoredAd::create($data);

        $notification = [
            'message' => 'Sponsored ad created successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('admin.sponsored-ads.index')->with($notification);
    }

    /**
     * Display the specified sponsored ad
     */
    public function show(SponsoredAd $sponsoredAd)
    {
        return view('backend.sponsored_ads.show', compact('sponsoredAd'));
    }

    /**
     * Show the form for editing the specified sponsored ad
     */
    public function edit(SponsoredAd $sponsoredAd)
    {
        $categories = Category::where('is_active', true)->get();
        return view('backend.sponsored_ads.edit', compact('sponsoredAd', 'categories'));
    }

    /**
     * Update the specified sponsored ad
     */
    public function update(Request $request, SponsoredAd $sponsoredAd)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'sponsor_name' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'sponsor_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'link_url' => 'nullable|url',
            'ad_format' => 'required|in:native,display,video,carousel',
            'placement' => 'required|in:homepage_top,homepage_middle,homepage_bottom,category_top,article_top,article_middle,article_bottom,sidebar',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'budget' => 'nullable|numeric|min:0',
            'target_categories' => 'nullable|array',
        ]);

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($sponsoredAd->image && file_exists(public_path($sponsoredAd->image))) {
                unlink(public_path($sponsoredAd->image));
            }

            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('upload/sponsored_ads'), $imageName);
            $data['image'] = 'upload/sponsored_ads/' . $imageName;
        }

        // Handle sponsor logo upload
        if ($request->hasFile('sponsor_logo')) {
            // Delete old logo
            if ($sponsoredAd->sponsor_logo && file_exists(public_path($sponsoredAd->sponsor_logo))) {
                unlink(public_path($sponsoredAd->sponsor_logo));
            }

            $logo = $request->file('sponsor_logo');
            $logoName = time() . '_logo_' . $logo->getClientOriginalName();
            $logo->move(public_path('upload/sponsored_ads'), $logoName);
            $data['sponsor_logo'] = 'upload/sponsored_ads/' . $logoName;
        }

        $sponsoredAd->update($data);

        $notification = [
            'message' => 'Sponsored ad updated successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('admin.sponsored-ads.index')->with($notification);
    }

    /**
     * Remove the specified sponsored ad
     */
    public function destroy(SponsoredAd $sponsoredAd)
    {
        // Delete image files
        if ($sponsoredAd->image && file_exists(public_path($sponsoredAd->image))) {
            unlink(public_path($sponsoredAd->image));
        }
        if ($sponsoredAd->sponsor_logo && file_exists(public_path($sponsoredAd->sponsor_logo))) {
            unlink(public_path($sponsoredAd->sponsor_logo));
        }

        $sponsoredAd->delete();

        $notification = [
            'message' => 'Sponsored ad deleted successfully',
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Toggle sponsored ad status
     */
    public function toggleStatus(SponsoredAd $sponsoredAd)
    {
        $sponsoredAd->update(['is_active' => !$sponsoredAd->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $sponsoredAd->is_active,
            'message' => $sponsoredAd->is_active ? 'Sponsored ad activated!' : 'Sponsored ad deactivated!'
        ]);
    }

    /**
     * Toggle premium status
     */
    public function togglePremium(SponsoredAd $sponsoredAd)
    {
        $sponsoredAd->update(['is_premium' => !$sponsoredAd->is_premium]);

        return response()->json([
            'success' => true,
            'is_premium' => $sponsoredAd->is_premium,
            'message' => $sponsoredAd->is_premium ? 'Marked as premium!' : 'Removed from premium!'
        ]);
    }

    /**
     * Update display order
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'sponsored_ads' => 'required|array',
            'sponsored_ads.*.id' => 'required|exists:sponsored_ads,id',
            'sponsored_ads.*.order' => 'required|integer|min:0'
        ]);

        foreach ($request->sponsored_ads as $adData) {
            SponsoredAd::where('id', $adData['id'])
                      ->update(['display_order' => $adData['order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Sponsored ad order updated successfully!'
        ]);
    }

    /**
     * Get performance statistics for sponsored ads
     */
    public function performanceStats()
    {
        try {
            $stats = [
                'total' => SponsoredAd::count(),
                'active' => SponsoredAd::where('is_active', true)->count(),
                'inactive' => SponsoredAd::where('is_active', false)->count(),
                'premium' => SponsoredAd::where('is_premium', true)->count(),
                'total_views' => SponsoredAd::sum('view_count'),
                'total_clicks' => SponsoredAd::sum('click_count'),
                'ads' => SponsoredAd::select('id', 'title', 'view_count', 'click_count', 'performance_score')
                    ->where('is_active', true)
                    ->orderBy('performance_score', 'desc')
                    ->take(10)
                    ->get()
                    ->map(function($ad) {
                        return [
                            'id' => $ad->id,
                            'title' => $ad->title,
                            'views' => $ad->view_count,
                            'clicks' => $ad->click_count,
                            'performance_score' => $ad->performance_score,
                            'performance_status' => $ad->getPerformanceStatus()
                        ];
                    })
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch sponsored ad performance statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
