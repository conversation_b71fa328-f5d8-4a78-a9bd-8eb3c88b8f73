@extends('backend.layout.template')

@section('body-content')
<div class="br-pagetitle">
    <i class="icon ion-ios-home-outline"></i>
    <div>
        <h4>Comment Management</h4>
        <p class="mg-b-0">Manage and moderate user comments</p>
    </div>
</div>

<div class="br-pagebody">
    <div class="br-section-wrapper">
        
        <!-- Stats Cards -->
        <div class="row mg-b-25">
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-chatboxes-outline tx-primary"></i>
                        <div class="media-body">
                            <h6>Total Comments</h6>
                            <h4 class="tx-primary">{{ $totalComments }}</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-clock-outline tx-warning"></i>
                        <div class="media-body">
                            <h6>Pending Approval</h6>
                            <h4 class="tx-warning">{{ $pendingComments }}</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-checkmark-outline tx-success"></i>
                        <div class="media-body">
                            <h6>Approved</h6>
                            <h4 class="tx-success">{{ $approvedComments }}</h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-status">
                    <div class="media">
                        <i class="icon ion-ios-close-outline tx-danger"></i>
                        <div class="media-body">
                            <h6>Rejected</h6>
                            <h4 class="tx-danger">{{ $totalComments - $approvedComments }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card mg-b-25">
            <div class="card-header">
                <h6 class="card-title mg-b-0">Filter Comments</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.comment-management') }}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Status</label>
                                <select name="status" class="form-control">
                                    <option value="">All Comments</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Search</label>
                                <input type="text" name="search" class="form-control" placeholder="Search comments, users, or posts..." value="{{ request('search') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">Filter</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div class="card mg-b-25">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-group mg-b-0">
                            <label class="ckbox">
                                <input type="checkbox" id="select-all">
                                <span>Select All</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('approve')">
                                <i class="fa fa-check"></i> Approve Selected
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="bulkAction('reject')">
                                <i class="fa fa-times"></i> Reject Selected
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('delete')">
                                <i class="fa fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comments Table -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mg-b-0">Comments List</h6>
            </div>
            <div class="card-body pd-0">
                <div class="table-responsive">
                    <table class="table table-striped mg-b-0">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="select-all-header">
                                </th>
                                <th>Comment</th>
                                <th>Author</th>
                                <th>Post</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($comments as $comment)
                            <tr>
                                <td>
                                    <input type="checkbox" class="comment-checkbox" value="{{ $comment->id }}">
                                </td>
                                <td>
                                    <div class="comment-preview">
                                        {{ Str::limit($comment->comment, 100) }}
                                    </div>
                                </td>
                                <td>
                                    @if($comment->user)
                                        <div class="d-flex align-items-center">
                                            <img src="{{ asset($comment->user->photo ?? 'upload/no_image.jpg') }}" 
                                                 alt="{{ $comment->user->name }}" 
                                                 class="rounded-circle mg-r-10" 
                                                 width="30" height="30">
                                            <div>
                                                <strong>{{ $comment->user->name }}</strong><br>
                                                <small class="text-muted">{{ $comment->user->email }}</small>
                                            </div>
                                        </div>
                                    @else
                                        <div>
                                            <strong>{{ $comment->guest_name ?? 'Anonymous' }}</strong><br>
                                            <small class="text-muted">{{ $comment->guest_email ?? 'No email' }}</small>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    @if($comment->newsPost)
                                        <a href="{{ url('v2/news/details/'.$comment->newsPost->id.'/'.$comment->newsPost->news_title_slug) }}" 
                                           target="_blank" class="text-primary">
                                            {{ Str::limit($comment->newsPost->news_title, 50) }}
                                        </a>
                                    @else
                                        <span class="text-muted">Post not found</span>
                                    @endif
                                </td>
                                <td>
                                    @if($comment->is_approved)
                                        <span class="badge badge-success">Approved</span>
                                    @else
                                        <span class="badge badge-warning">Pending</span>
                                    @endif
                                </td>
                                <td>
                                    <small>{{ $comment->created_at->format('M d, Y') }}<br>{{ $comment->created_at->format('h:i A') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @if(!$comment->is_approved)
                                            <button type="button" class="btn btn-success" onclick="approveComment({{ $comment->id }})">
                                                <i class="fa fa-check"></i>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-warning" onclick="rejectComment({{ $comment->id }})">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-info" onclick="viewComment({{ $comment->id }})">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="deleteComment({{ $comment->id }})">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="icon ion-ios-chatboxes-outline tx-60"></i>
                                        <p class="mg-t-10">No comments found</p>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($comments->hasPages())
            <div class="card-footer">
                {{ $comments->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.comment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('select-all-header').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.comment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Individual comment actions
function approveComment(id) {
    if (confirm('Are you sure you want to approve this comment?')) {
        fetch(`/admin/comment-management/${id}/approve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function rejectComment(id) {
    if (confirm('Are you sure you want to reject this comment?')) {
        fetch(`/admin/comment-management/${id}/reject`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function deleteComment(id) {
    if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
        fetch(`/admin/comment-management/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

// Bulk actions
function bulkAction(action) {
    const selectedComments = Array.from(document.querySelectorAll('.comment-checkbox:checked')).map(cb => cb.value);
    
    if (selectedComments.length === 0) {
        alert('Please select at least one comment');
        return;
    }

    const actionText = action === 'approve' ? 'approve' : action === 'reject' ? 'reject' : 'delete';
    if (confirm(`Are you sure you want to ${actionText} ${selectedComments.length} comment(s)?`)) {
        fetch('/admin/comment-management/bulk-action', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: action,
                comment_ids: selectedComments
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function viewComment(id) {
    window.open(`/admin/comment-management/${id}/show`, '_blank');
}
</script>
@endsection
