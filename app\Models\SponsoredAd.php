<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SponsoredAd extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'budget' => 'decimal:2',
        'target_demographics' => 'array',
        'target_categories' => 'array',
    ];

    /**
     * Check if sponsored ad is currently active
     */
    public function isCurrentlyActive()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now()->toDateString();

        if ($this->start_date && $this->start_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    /**
     * Increment view count
     */
    public function incrementViews()
    {
        $this->increment('view_count');
        $this->updatePerformanceScore();
    }

    /**
     * Increment click count
     */
    public function incrementClicks()
    {
        $this->increment('click_count');
        $this->updatePerformanceScore();
    }

    /**
     * Calculate and update performance score
     */
    public function updatePerformanceScore()
    {
        $views = $this->view_count ?? 0;
        $clicks = $this->click_count ?? 0;

        // Calculate CTR (Click Through Rate)
        $ctr = $views > 0 ? ($clicks / $views) * 100 : 0;

        // Calculate performance score (0-100) with premium bonus
        $baseScore = min(100, ($ctr * 10) + ($views / 100));
        $premiumBonus = $this->is_premium ? 10 : 0;
        $performanceScore = min(100, $baseScore + $premiumBonus);

        $this->update(['performance_score' => round($performanceScore, 2)]);
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics()
    {
        $views = $this->view_count ?? 0;
        $clicks = $this->click_count ?? 0;
        $ctr = $views > 0 ? ($clicks / $views) * 100 : 0;
        $budget = $this->budget ?? 0;
        $costPerClick = $clicks > 0 ? $budget / $clicks : 0;

        return [
            'views' => $views,
            'clicks' => $clicks,
            'ctr' => round($ctr, 2),
            'performance_score' => $this->performance_score ?? 0,
            'cost_per_click' => round($costPerClick, 2),
            'budget_utilization' => $budget > 0 ? round(($clicks * $costPerClick / $budget) * 100, 2) : 0,
            'status' => $this->getPerformanceStatus()
        ];
    }

    /**
     * Get performance status based on metrics
     */
    public function getPerformanceStatus()
    {
        $score = $this->performance_score ?? 0;

        if ($score >= 80) return 'excellent';
        if ($score >= 60) return 'good';
        if ($score >= 40) return 'average';
        if ($score >= 20) return 'poor';
        return 'very_poor';
    }

    /**
     * Calculate click-through rate
     */
    public function getClickThroughRateAttribute()
    {
        if ($this->view_count == 0) {
            return 0;
        }

        return round(($this->click_count / $this->view_count) * 100, 2);
    }

    /**
     * Scope for active sponsored ads
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for premium sponsored ads
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope for sponsored ads by placement
     */
    public function scopeByPlacement($query, $placement)
    {
        return $query->where('placement', $placement);
    }

    /**
     * Scope for sponsored ads by format
     */
    public function scopeByFormat($query, $format)
    {
        return $query->where('ad_format', $format);
    }

    /**
     * Scope for ordered sponsored ads
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('is_premium', 'desc')
                    ->orderBy('display_order', 'asc')
                    ->orderBy('created_at', 'desc');
    }
}
