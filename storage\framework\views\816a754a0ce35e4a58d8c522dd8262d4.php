<?php $__env->startSection('title'); ?>
<?php echo e(\App\Models\SiteSetting::get('site_title', 'NitiKotha - Latest News & Updates')); ?> (Facebook Style)
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                <?php if(auth()->guard()->check()): ?>
                    <?php
                        $userPhotoPath = 'upload/no_image.jpg';
                        if (!empty(auth()->user()->photo)) {
                            // Check if photo already contains path (starts with 'upload/')
                            if (str_starts_with(auth()->user()->photo, 'upload/')) {
                                // Photo already contains full path (new format)
                                $userPhotoPath = auth()->user()->photo;
                            } else {
                                // Photo contains only filename (old format) - assume admin
                                $userPhotoPath = 'upload/admin_images/' . auth()->user()->photo;
                                
                            } 
                            
                        }
                    ?>
                    <img src="<?php echo e(url($userPhotoPath)); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                <?php else: ?>
                    <?php
                        $siteLogo = \App\Models\SiteSetting::get('site_logo');
                        $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                    ?>
                    <img src="<?php echo e($logoPath); ?>" alt="<?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?>">
                <?php endif; ?>
            </div>
            <div class="fb-profile-info">
                <?php if(auth()->guard()->check()): ?>
                    <h3 class="fb-profile-name"><?php echo e(auth()->user()->name); ?></h3>
                    <p class="fb-profile-tagline"><?php echo e(auth()->user()->hasRole('Subscriber') ? 'Subscriber' : 'Admin'); ?></p>
                <?php else: ?>
                    <h3 class="fb-profile-name">NitiKotha</h3>
                    <p class="fb-profile-tagline">Premium News Portal</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="fb-nav-menu">
            <a href="<?php echo e(url('/')); ?>" class="fb-nav-item active">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-fire"></i>
                <span>Trending</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-calendar"></i>
                <span>Events</span>
            </a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                <?php if(isset($categories)): ?>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category" data-category-id="<?php echo e($category->id); ?>">
                            <div class="fb-category-left">
                                <div class="fb-category-icon"><?php echo e($category->category_icon); ?></div>
                                <span class="fb-category-name"><?php echo e($category->category_name); ?></span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count"><?php echo e($category->news_posts_count ?? 0); ?></span>
                                <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                                    <button class="fb-category-toggle" data-target="subcats-<?php echo e($category->id); ?>">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="<?php echo e(url('v2/news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                        <div class="fb-subcategories" id="subcats-<?php echo e($category->id); ?>">
                            <?php $__currentLoopData = $category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug)); ?>" class="fb-subcategory-item">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name"><?php echo e($subcategory->subcategory_name); ?></span>
                                <span class="fb-subcategory-count"><?php echo e($subcategory->news_posts_count ?? 0); ?></span>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- Stories Section -->
        <div class="fb-stories-container">
            <div class="fb-stories-wrapper">


                <!-- News Stories from Top Slider -->
                <?php if(isset($news_slider) && $news_slider->count() > 0): ?>
                    <?php $__currentLoopData = $news_slider; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-story-card" data-story-id="<?php echo e($story->id); ?>">
                        <div class="fb-story-image">
                            <img src="<?php echo e(url($story->image)); ?>" alt="<?php echo e($story->news_title); ?>" loading="lazy" onerror="this.src='<?php echo e(url('upload/no_image.jpg')); ?>'">
                        </div>
                        <div class="fb-story-overlay"></div>
                        <div class="fb-story-content">
                            <div class="fb-story-avatar">
                                <?php
                                    $photoPath = 'upload/no_image.jpg';
                                    if (!empty($story->user->photo)) {
                                        // Check if photo already contains path (starts with 'upload/')
                                        if (str_starts_with($story->user->photo, 'upload/')) {
                                            // Photo already contains full path (new format)
                                            $photoPath = $story->user->photo;
                                        } else {
                                            // Photo contains only filename (old format) - assume admin
                                            $photoPath = 'upload/admin_images/' . $story->user->photo;
                                        }
                                    }
                                ?>
                                <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($story->user->name ?? 'NitiKotha'); ?>">
                            </div>
                            <span class="fb-story-title"><?php echo e(Str::limit($story->news_title, 35)); ?></span>
                            <div class="fb-story-meta">
                                <span class="fb-story-category"><?php echo e($story->category->category_name); ?></span>
                                <span class="fb-story-time"><?php echo e($story->created_at->diffForHumans()); ?></span>
                            </div>
                        </div>
                        <a href="<?php echo e(url('v2/news/details/'.$story->id.'/'.$story->news_title_slug)); ?>" class="fb-story-link"></a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <!-- Fallback stories if no top slider posts -->
                    <?php if(isset($newnewspost)): ?>
                        <?php $__currentLoopData = $newnewspost->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="fb-story-card" data-story-id="<?php echo e($story->id); ?>">
                            <div class="fb-story-image">
                                <img src="<?php echo e(url($story->image)); ?>" alt="<?php echo e($story->news_title); ?>" loading="lazy" onerror="this.src='<?php echo e(url('upload/no_image.jpg')); ?>'">
                            </div>
                            <div class="fb-story-overlay"></div>
                            <div class="fb-story-content">
                                <div class="fb-story-avatar">
                                    <?php
                                        $photoPath = 'upload/no_image.jpg';
                                        if (!empty($story->user->photo)) {
                                            // Check if photo already contains path (starts with 'upload/')
                                            if (str_starts_with($story->user->photo, 'upload/')) {
                                                // Photo already contains full path (new format)
                                                $photoPath = $story->user->photo;
                                            } else {
                                                // Photo contains only filename (old format) - assume admin
                                                $photoPath = 'upload/admin_images/' . $story->user->photo;
                                            }
                                        }
                                    ?>
                                    <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($story->user->name ?? 'NitiKotha'); ?>">
                                </div>
                                <span class="fb-story-title"><?php echo e(Str::limit($story->news_title, 35)); ?></span>
                                <div class="fb-story-meta">
                                    <span class="fb-story-category"><?php echo e($story->category->category_name); ?></span>
                                    <span class="fb-story-time"><?php echo e($story->created_at->diffForHumans()); ?></span>
                                </div>
                            </div>
                            <a href="<?php echo e(url('v2/news/details/'.$story->id.'/'.$story->news_title_slug)); ?>" class="fb-story-link"></a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Create Post Section -->
        <div class="fb-create-post">
            <div class="fb-create-post-header">
                <div class="fb-create-avatar">
                    <?php
                        $siteLogo = \App\Models\SiteSetting::get('site_logo');
                        $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                    ?>
                    <img src="<?php echo e($logoPath); ?>" alt="<?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?>">
                </div>
                <div class="fb-create-input">
                    <input type="text" placeholder="What's happening in the news today?" readonly>
                </div>
            </div>
            <div class="fb-create-post-actions">
                <button class="fb-action-btn">
                    <i class="fas fa-video"></i>
                    <span>Live Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-image"></i>
                    <span>Photo/Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-smile"></i>
                    <span>Feeling/Activity</span>
                </button>
            </div>
        </div>

        <!-- Top Homepage Advertisements -->
        <?php if(isset($sponsoredAds['homepage_top']) && $sponsoredAds['homepage_top']->count() > 0): ?>
            <?php $__currentLoopData = $sponsoredAds['homepage_top']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'top'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- Regular Banner Advertisements (Top) -->
        <?php if(isset($advertisements)): ?>
            <?php $__currentLoopData = $advertisements->where('ad_type', 'banner')->where('position', 'top'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'top'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- News Feed Posts -->
        <div class="fb-news-feed" id="news-feed-container">
            <?php if(isset($newnewspost)): ?>
                <?php $__currentLoopData = $newnewspost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                    <!-- Insert Middle Advertisement after 3rd post -->
                    <?php if($index == 3): ?>
                        <?php if(isset($sponsoredAds['homepage_middle']) && $sponsoredAds['homepage_middle']->count() > 0): ?>
                            <?php $__currentLoopData = $sponsoredAds['homepage_middle']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'middle'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <!-- Regular Inline Advertisements (Middle) -->
                        <?php if(isset($advertisements)): ?>
                            <?php $__currentLoopData = $advertisements->where('ad_type', 'inline')->where('position', 'middle'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'middle'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                <article class="fb-post">
                    <!-- Post Header -->
                    <div class="fb-post-header">
                        <div class="fb-post-avatar">
                            <?php
                                $photoPath = 'upload/no_image.jpg';
                                if (!empty($news->user->photo)) {
                                    // Check if photo already contains path (starts with 'upload/')
                                    if (str_starts_with($news->user->photo, 'upload/')) {
                                        // Photo already contains full path (new format)
                                        $photoPath = $news->user->photo;
                                    } else {
                                        // Photo contains only filename (old format) - assume admin
                                        $photoPath = 'upload/admin_images/' . $news->user->photo;
                                    }
                                }
                            ?>
                            <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($news->user->name); ?>">
                        </div>
                        <div class="fb-post-info">
                            <h4 class="fb-post-author"><?php echo e($news->user->name ?? 'NitiKotha'); ?></h4>
                            <div class="fb-post-meta">
                                <span class="fb-post-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                <span class="fb-post-separator">·</span>
                                <span class="fb-post-category"><?php echo e($news->category->category_name); ?></span>
                                <span class="fb-post-separator">·</span>
                                <i class="fas fa-globe-americas"></i>
                            </div>
                        </div>
                        <div class="fb-post-options">
                            <button class="fb-options-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="fb-post-content">
                        <h3 class="fb-post-title">
                            <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                <?php echo e($news->news_title); ?>

                            </a>
                        </h3>
                        <p class="fb-post-excerpt">
                            <?php echo e(Str::limit(strip_tags($news->news_details), 200)); ?>

                        </p>
                    </div>

                    <!-- Post Image -->
                    <?php if($news->image): ?>
                    <div class="fb-post-image">
                        <img src="<?php echo e(url($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" onerror="this.src='<?php echo e(url('upload/no_image.jpg')); ?>'">
                        <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="fb-post-image">
                        <img src="<?php echo e(url('upload/no_image.jpg')); ?>" alt="<?php echo e($news->news_title); ?>">
                        <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Post Stats -->
                    <div class="fb-post-stats">
                        <div class="fb-post-reactions">
                            <div class="fb-reaction-icons">
                                <span class="fb-reaction like">👍</span>
                                <span class="fb-reaction love">❤️</span>
                                <span class="fb-reaction wow">😮</span>
                            </div>
                            <span class="fb-reaction-count" id="likes-count-<?php echo e($news->id); ?>"><?php echo e($news->likes_count ?? 0); ?></span>
                        </div>
                        <div class="fb-post-engagement">
                            <span class="fb-comments-count" id="comments-count-<?php echo e($news->id); ?>"><?php echo e($news->comments_count ?? 0); ?> comments</span>
                            <span class="fb-shares-count" id="shares-count-<?php echo e($news->id); ?>"><?php echo e($news->shares_count ?? 0); ?> shares</span>
                        </div>
                    </div>

                    <!-- Post Actions -->
                    <div class="fb-post-actions">
                        <button class="fb-action-btn fb-like-btn <?php echo e($news->user_liked ? 'liked' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-thumbs-up"></i>
                            <span><?php echo e($news->user_liked ? 'Liked' : 'Like'); ?></span>
                            <!-- Reaction Picker -->
                            <div class="fb-reaction-picker">
                                <div class="fb-reaction-option" data-reaction="like" title="Like">👍</div>
                                <div class="fb-reaction-option" data-reaction="love" title="Love">❤️</div>
                                <div class="fb-reaction-option" data-reaction="wow" title="Wow">😮</div>
                                <div class="fb-reaction-option" data-reaction="angry" title="Angry">😡</div>
                                <div class="fb-reaction-option" data-reaction="sad" title="Sad">😢</div>
                            </div>
                        </button>
                        <button class="fb-action-btn fb-comment-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-comment"></i>
                            <span>Comment</span>
                        </button>
                        <button class="fb-action-btn fb-share-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                        <button class="fb-action-btn fb-save-btn <?php echo e($news->user_saved ? 'saved' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-bookmark"></i>
                            <span><?php echo e($news->user_saved ? 'Saved' : 'Save'); ?></span>
                        </button>
                    </div>

                    <!-- Comments Section (Initially Hidden) -->
                    <div class="fb-comments-section" id="comments-<?php echo e($news->id); ?>" style="display: none;">
                        <!-- Write Comment -->
                        <div class="fb-write-comment">
                            <div class="fb-comment-avatar">
                                <?php if(auth()->guard()->check()): ?>
                                    <?php
                                        $userPhotoPath = 'upload/no_image.jpg';
                                        if (!empty(auth()->user()->photo)) {
                                            if (str_starts_with(auth()->user()->photo, 'upload/')) {
                                                $userPhotoPath = auth()->user()->photo;
                                            } else {
                                                $userPhotoPath = 'upload/admin_images/' . auth()->user()->photo;
                                            }
                                        }
                                    ?>
                                    <img src="<?php echo e(url($userPhotoPath)); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(url('upload/no_image.jpg')); ?>" alt="Guest">
                                <?php endif; ?>
                            </div>
                            <div class="fb-comment-input-wrapper">
                                <form class="fb-comment-form" data-news-id="<?php echo e($news->id); ?>">
                                    <?php echo csrf_field(); ?>
                                    <textarea class="fb-comment-input" placeholder="Write a comment..." rows="2"></textarea>
                                    <div class="fb-comment-actions">
                                        <button type="button" class="fb-comment-emoji">😊</button>
                                        <button type="button" class="fb-comment-photo">📷</button>
                                        <button type="submit" class="fb-comment-submit">Post</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Comments List -->
                        <div class="fb-comments-list" id="comments-list-<?php echo e($news->id); ?>">
                            <!-- Comments will be loaded here -->
                            <div class="fb-no-comments">
                                <p>No comments yet. Be the first to comment!</p>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            <!-- Load More Button -->
            <div class="fb-load-more-container text-center mt-4" id="load-more-container">
                <button class="btn btn-primary btn-lg" id="load-more-btn">
                    <i class="fas fa-plus-circle me-2"></i>
                    Load More Posts
                </button>
                <div class="fb-loading-spinner" id="loading-spinner" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading more posts...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Sponsored Section -->
        <div class="fb-sponsored-section">
            <h4 class="fb-section-title">Sponsored</h4>
            <div class="fb-sponsored-item">
                <div class="fb-sponsored-image">
                    <?php
                        $siteLogo = \App\Models\SiteSetting::get('site_logo');
                        $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                    ?>
                    <img src="<?php echo e($logoPath); ?>" alt="Sponsored">
                </div>
                <div class="fb-sponsored-content">
                    <h5 class="fb-sponsored-title"><?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?> Premium</h5>
                    <p class="fb-sponsored-text">Get unlimited access to premium news content</p>
                </div>
            </div>
        </div>

        <!-- Trending Topics -->
        <div class="fb-trending-section">
            <h4 class="fb-section-title">
                <i class="fas fa-fire text-danger me-2"></i>
                Trending Topics
            </h4>
            <div class="fb-trending-list">
                <?php if(isset($trendingPosts) && $trendingPosts->count() > 0): ?>
                    <?php $__currentLoopData = $trendingPosts->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $trending): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-trending-item">
                        <div class="fb-trending-rank"><?php echo e($index + 1); ?></div>
                        <div class="fb-trending-content">
                            <h5 class="fb-trending-title">
                                <a href="<?php echo e(url('v2/news/details/'.$trending->id.'/'.$trending->news_title_slug)); ?>">
                                    <?php echo e(Str::limit($trending->news_title, 60)); ?>

                                </a>
                            </h5>
                            <p class="fb-trending-meta">
                                <i class="fas fa-fire text-danger me-1"></i>
                                <?php echo e($trending->view_count ?? 0); ?> people talking about this
                            </p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php elseif(isset($newspopular)): ?>
                    <?php $__currentLoopData = $newspopular->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $trending): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-trending-item">
                        <div class="fb-trending-rank"><?php echo e($index + 1); ?></div>
                        <div class="fb-trending-content">
                            <h5 class="fb-trending-title">
                                <a href="<?php echo e(url('v2/news/details/'.$trending->id.'/'.$trending->news_title_slug)); ?>">
                                    <?php echo e(Str::limit($trending->news_title, 60)); ?>

                                </a>
                            </h5>
                            <p class="fb-trending-meta"><?php echo e($trending->view_count ?? 0); ?> people talking about this</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Featured Posts Section -->
        <?php if(isset($featuredPosts) && $featuredPosts->count() > 0): ?>
        <div class="fb-featured-section mt-4">
            <h4 class="fb-section-title">
                <i class="fas fa-star text-warning me-2"></i>
                Featured Posts
            </h4>
            <div class="fb-featured-list">
                <?php $__currentLoopData = $featuredPosts->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featured): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="fb-featured-item">
                    <div class="fb-featured-image">
                        <img src="<?php echo e(url($featured->image)); ?>" alt="<?php echo e($featured->news_title); ?>" loading="lazy" onerror="this.src='<?php echo e(url('upload/no_image.jpg')); ?>'">
                        <div class="fb-featured-badge">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <div class="fb-featured-content">
                        <h6 class="fb-featured-title">
                            <a href="<?php echo e(url('v2/news/details/'.$featured->id.'/'.$featured->news_title_slug)); ?>">
                                <?php echo e(Str::limit($featured->news_title, 50)); ?>

                            </a>
                        </h6>
                        <p class="fb-featured-meta">
                            <span class="text-muted"><?php echo e($featured->category->category_name); ?></span>
                        </p>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Pinned Posts Section -->
        <?php if(isset($pinnedPosts) && $pinnedPosts->count() > 0): ?>
        <div class="fb-pinned-section mt-4">
            <h4 class="fb-section-title">
                <i class="fas fa-thumbtack text-info me-2"></i>
                Pinned Posts
            </h4>
            <div class="fb-pinned-list">
                <?php $__currentLoopData = $pinnedPosts->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pinned): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="fb-pinned-item">
                    <div class="fb-pinned-icon">
                        <i class="fas fa-thumbtack text-info"></i>
                    </div>
                    <div class="fb-pinned-content">
                        <h6 class="fb-pinned-title">
                            <a href="<?php echo e(url('v2/news/details/'.$pinned->id.'/'.$pinned->news_title_slug)); ?>">
                                <?php echo e(Str::limit($pinned->news_title, 60)); ?>

                            </a>
                        </h6>
                        <p class="fb-pinned-meta">
                            <span class="text-muted">Pinned <?php echo e($pinned->pinned_at ? $pinned->pinned_at->diffForHumans() : 'recently'); ?></span>
                        </p>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sidebar Advertisements -->
        <?php if(isset($sponsoredAds['sidebar']) && $sponsoredAds['sidebar']->count() > 0): ?>
            <?php $__currentLoopData = $sponsoredAds['sidebar']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'sidebar'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- Regular Sidebar Advertisements -->
        <?php if(isset($advertisements)): ?>
            <?php $__currentLoopData = $advertisements->where('ad_type', 'sidebar')->where('position', 'right'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'sidebar'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- Online Friends -->
        <div class="fb-contacts-section">
            <h4 class="fb-section-title">Contacts</h4>
            <div class="fb-contacts-list">
                <?php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                ?>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e($logoPath); ?>" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">News Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e($logoPath); ?>" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">Editorial Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e($logoPath); ?>" alt="Contact">
                    </div>
                    <span class="fb-contact-name">Support Team</span>
                </div>
            </div>
        </div>

        <!-- Additional Content for Scroll Testing -->
        <div class="fb-additional-section">
            <h4 class="fb-section-title">Quick Links</h4>
            <div class="fb-quick-links">
                <a href="#" class="fb-quick-link">📰 Latest News</a>
                <a href="#" class="fb-quick-link">🔥 Breaking News</a>
                <a href="#" class="fb-quick-link">⚽ Sports</a>
                <a href="#" class="fb-quick-link">💼 Business</a>
                <a href="#" class="fb-quick-link">🌍 World</a>
                <a href="#" class="fb-quick-link">💻 Technology</a>
                <a href="#" class="fb-quick-link">🎬 Entertainment</a>
                <a href="#" class="fb-quick-link">🏥 Health</a>
                <a href="#" class="fb-quick-link">📚 Education</a>
                <a href="#" class="fb-quick-link">🚗 Lifestyle</a>
            </div>
        </div>

        <div class="fb-weather-section">
            <h4 class="fb-section-title">Weather</h4>
            <div class="fb-weather-widget">
                <div class="fb-weather-location">Dhaka, Bangladesh</div>
                <div class="fb-weather-temp">28°C</div>
                <div class="fb-weather-desc">Partly Cloudy</div>
                <div class="fb-weather-details">
                    <span>Humidity: 65%</span>
                    <span>Wind: 12 km/h</span>
                </div>
            </div>
        </div>

        <div class="fb-events-section">
            <h4 class="fb-section-title">Upcoming Events</h4>
            <div class="fb-events-list">
                <div class="fb-event-item">
                    <div class="fb-event-date">Dec 25</div>
                    <div class="fb-event-info">
                        <h5>Christmas Celebration</h5>
                        <p>Join us for festive celebrations</p>
                    </div>
                </div>
                <div class="fb-event-item">
                    <div class="fb-event-date">Jan 1</div>
                    <div class="fb-event-info">
                        <h5>New Year Special</h5>
                        <p>Welcome 2024 with us</p>
                    </div>
                </div>
                <div class="fb-event-item">
                    <div class="fb-event-date">Feb 14</div>
                    <div class="fb-event-info">
                        <h5>Valentine's Day</h5>
                        <p>Love is in the air</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    let currentPage = 2; // Start from page 2 since page 1 is already loaded
    let isLoading = false;
    let hasMorePosts = true;

    // Load More Button Click
    $('#load-more-btn').on('click', function() {
        loadMorePosts();
    });

    // Infinite Scroll (optional - triggers when user scrolls near bottom)
    $(window).on('scroll', function() {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 1000) {
            if (!isLoading && hasMorePosts) {
                loadMorePosts();
            }
        }
    });

    function loadMorePosts() {
        if (isLoading || !hasMorePosts) return;

        isLoading = true;
        $('#load-more-btn').hide();
        $('#loading-spinner').show();

        $.ajax({
            url: '<?php echo e(route("v2.load-more-posts")); ?>',
            method: 'GET',
            data: {
                page: currentPage
            },
            success: function(response) {
                if (response.success && response.posts.length > 0) {
                    // Append new posts to the feed
                    response.posts.forEach(function(post) {
                        const postHtml = createPostHtml(post);
                        $('#news-feed-container').append(postHtml);
                    });

                    currentPage = response.nextPage;
                    hasMorePosts = response.hasMore;

                    if (!hasMorePosts) {
                        $('#load-more-container').html('<p class="text-muted">No more posts to load.</p>');
                    }
                } else {
                    hasMorePosts = false;
                    $('#load-more-container').html('<p class="text-muted">No more posts to load.</p>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading more posts:', error);
                $('#load-more-container').html('<p class="text-danger">Error loading posts. Please try again.</p>');
            },
            complete: function() {
                isLoading = false;
                $('#loading-spinner').hide();
                if (hasMorePosts) {
                    $('#load-more-btn').show();
                }
            }
        });
    }

    function createPostHtml(post) {
        // Determine user photo path
        let photoPath = '<?php echo e(asset("upload/no_image.jpg")); ?>';
        if (post.user && post.user.photo) {
            // Check if photo already contains path (starts with 'upload/')
            if (post.user.photo.startsWith('upload/')) {
                // Photo already contains full path (new format)
                photoPath = '<?php echo e(url("")); ?>/' + post.user.photo;
            } else {
                // Photo contains only filename (old format) - assume admin
                photoPath = '<?php echo e(url("upload/admin_images")); ?>/' + post.user.photo;
            }
        }

        const postUrl = `<?php echo e(url('v2/news/details')); ?>/${post.id}/${post.news_title_slug}`;
        const imageHtml = post.image ? `
            <div class="fb-post-image">
                <img src="<?php echo e(url('')); ?>/${post.image}" alt="${post.news_title}">
                <a href="${postUrl}" class="fb-image-overlay">
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>
        ` : '';

        return `
            <article class="fb-post">
                <!-- Post Header -->
                <div class="fb-post-header">
                    <div class="fb-post-avatar">
                        <img src="${photoPath}" alt="${post.user ? post.user.name : 'User'}">
                    </div>
                    <div class="fb-post-info">
                        <h4 class="fb-post-author">${post.user ? post.user.name : 'NitiKotha'}</h4>
                        <div class="fb-post-meta">
                            <span class="fb-post-time">${formatDate(post.created_at)}</span>
                            <span class="fb-post-separator">·</span>
                            <span class="fb-post-category">${post.category ? post.category.category_name : 'News'}</span>
                            <span class="fb-post-separator">·</span>
                            <i class="fas fa-globe-americas"></i>
                        </div>
                    </div>
                    <div class="fb-post-options">
                        <button class="fb-options-btn">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- Post Content -->
                <div class="fb-post-content">
                    <h3 class="fb-post-title">
                        <a href="${postUrl}">
                            ${post.news_title}
                        </a>
                    </h3>
                    <p class="fb-post-excerpt">
                        ${post.news_details ? post.news_details.replace(/<[^>]*>/g, '').substring(0, 200) + '...' : ''}
                    </p>
                </div>

                ${imageHtml}

                <!-- Post Stats -->
                <div class="fb-post-stats">
                    <div class="fb-stats-left">
                        <span class="fb-stat-item">
                            <i class="fas fa-thumbs-up text-primary"></i>
                            <span class="fb-stat-count">${post.likes_count || 0}</span>
                        </span>
                    </div>
                    <div class="fb-stats-right">
                        <span class="fb-stat-item">${post.comments_count || 0} comments</span>
                        <span class="fb-stat-item">${post.shares_count || 0} shares</span>
                    </div>
                </div>

                <!-- Post Actions -->
                <div class="fb-post-actions">
                    <button class="fb-action-btn like-btn" data-post-id="${post.id}">
                        <i class="far fa-thumbs-up"></i>
                        <span>Like</span>
                    </button>
                    <button class="fb-action-btn comment-btn">
                        <i class="far fa-comment"></i>
                        <span>Comment</span>
                    </button>
                    <button class="fb-action-btn share-btn" data-post-id="${post.id}">
                        <i class="far fa-share"></i>
                        <span>Share</span>
                    </button>
                    <a href="${postUrl}" class="fb-action-btn read-btn">
                        <i class="far fa-eye"></i>
                        <span>Read More</span>
                    </a>
                </div>
            </article>
        `;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + ' days ago';

        return date.toLocaleDateString();
    }

    // Advertisement tracking functions
    window.trackAdView = function(adId) {
        fetch('/api/ads/track-view', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Ad view tracking failed:', error));
    };

    window.trackAdClick = function(adId) {
        fetch('/api/ads/track-click', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Ad click tracking failed:', error));
    };

    window.hideAd = function(adId) {
        const adElement = document.querySelector(`[data-ad-id="${adId}"]`);
        if (adElement) {
            adElement.style.display = 'none';
            // Track ad hide action
            fetch('/api/ads/track-hide', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ad_id: adId })
            }).catch(error => console.log('Ad hide tracking failed:', error));
        }
    };

    window.reportAd = function(adId) {
        if (confirm('Report this advertisement as inappropriate?')) {
            fetch('/api/ads/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ad_id: adId })
            }).then(response => {
                if (response.ok) {
                    alert('Advertisement reported successfully. Thank you for your feedback.');
                }
            }).catch(error => console.log('Ad report failed:', error));
        }
    };

    // Regular Advertisement tracking functions
    window.trackRegularAdView = function(adId) {
        fetch('/api/regular-ads/track-view', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Regular ad view tracking failed:', error));
    };

    window.trackRegularAdClick = function(adId) {
        fetch('/api/regular-ads/track-click', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ad_id: adId })
        }).catch(error => console.log('Regular ad click tracking failed:', error));
    };

    window.hideRegularAd = function(adId) {
        const adElement = document.querySelector(`[data-ad-id="${adId}"]`);
        if (adElement) {
            adElement.style.display = 'none';
        }
    };

    window.reportRegularAd = function(adId) {
        if (confirm('Report this advertisement as inappropriate?')) {
            fetch('/api/regular-ads/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ ad_id: adId })
            }).then(response => {
                if (response.ok) {
                    alert('Advertisement reported successfully. Thank you for your feedback.');
                }
            }).catch(error => console.log('Regular ad report failed:', error));
        }
    };

    window.closePopupAd = function(adId) {
        document.getElementById(`popup-ad-${adId}`).style.display = 'none';
    };
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout_facebook_style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/index_facebook_style.blade.php ENDPATH**/ ?>