<?php $__env->startSection('title'); ?>
Ni<PERSON><PERSON>otha - Modern News Portal (Facebook Style)
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="NitiKotha">
            </div>
            <div class="fb-profile-info">
                <h3 class="fb-profile-name">NitiKotha</h3>
                <p class="fb-profile-tagline">Premium News Portal</p>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="fb-nav-menu">
            <a href="<?php echo e(url('/')); ?>" class="fb-nav-item active">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-fire"></i>
                <span>Trending</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-calendar"></i>
                <span>Events</span>
            </a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                <?php if(isset($categories)): ?>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category" data-category-id="<?php echo e($category->id); ?>">
                            <div class="fb-category-left">
                                <div class="fb-category-icon"><?php echo e($category->category_icon); ?></div>
                                <span class="fb-category-name"><?php echo e($category->category_name); ?></span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count"><?php echo e($category->news_posts_count ?? 0); ?></span>
                                <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                                    <button class="fb-category-toggle" data-target="subcats-<?php echo e($category->id); ?>">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="<?php echo e(url('v2/news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                        <div class="fb-subcategories" id="subcats-<?php echo e($category->id); ?>">
                            <?php $__currentLoopData = $category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug)); ?>" class="fb-subcategory-item">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name"><?php echo e($subcategory->subcategory_name); ?></span>
                                <span class="fb-subcategory-count"><?php echo e($subcategory->news_posts_count ?? 0); ?></span>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- Stories Section -->
        <div class="fb-stories-container">
            <div class="fb-stories-wrapper">
                <!-- Create Story -->
                <div class="fb-story-card fb-create-story">
                    <div class="fb-story-image">
                        <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="Create Story">
                    </div>
                    <div class="fb-story-content">
                        <div class="fb-story-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <span class="fb-story-text">Create Story</span>
                    </div>
                </div>

                <!-- News Stories from Top Slider -->
                <?php if(isset($news_slider) && $news_slider->count() > 0): ?>
                    <?php $__currentLoopData = $news_slider; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-story-card" data-story-id="<?php echo e($story->id); ?>">
                        <div class="fb-story-image">
                            <img src="<?php echo e(asset($story->image)); ?>" alt="<?php echo e($story->news_title); ?>" loading="lazy">
                        </div>
                        <div class="fb-story-overlay"></div>
                        <div class="fb-story-content">
                            <div class="fb-story-avatar">
                                <?php
                                    $photoPath = 'upload/no_image.jpg';
                                    if (!empty($story->user->photo)) {
                                        // User photo already contains full path
                                        $photoPath = $story->user->photo;
                                    }
                                ?>
                                <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($story->user->name ?? 'NitiKotha'); ?>">
                            </div>
                            <span class="fb-story-title"><?php echo e(Str::limit($story->news_title, 35)); ?></span>
                            <div class="fb-story-meta">
                                <span class="fb-story-category"><?php echo e($story->category->category_name); ?></span>
                                <span class="fb-story-time"><?php echo e($story->created_at->diffForHumans()); ?></span>
                            </div>
                        </div>
                        <a href="<?php echo e(url('v2/news/details/'.$story->id.'/'.$story->news_title_slug)); ?>" class="fb-story-link"></a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <!-- Fallback stories if no top slider posts -->
                    <?php if(isset($newnewspost)): ?>
                        <?php $__currentLoopData = $newnewspost->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="fb-story-card" data-story-id="<?php echo e($story->id); ?>">
                            <div class="fb-story-image">
                                <img src="<?php echo e(asset($story->image)); ?>" alt="<?php echo e($story->news_title); ?>" loading="lazy">
                            </div>
                            <div class="fb-story-overlay"></div>
                            <div class="fb-story-content">
                                <div class="fb-story-avatar">
                                    <?php
                                        $photoPath = 'upload/no_image.jpg';
                                        if (!empty($story->user->photo)) {
                                            // User photo already contains full path
                                            $photoPath = $story->user->photo;
                                        }
                                    ?>
                                    <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($story->user->name ?? 'NitiKotha'); ?>">
                                </div>
                                <span class="fb-story-title"><?php echo e(Str::limit($story->news_title, 35)); ?></span>
                                <div class="fb-story-meta">
                                    <span class="fb-story-category"><?php echo e($story->category->category_name); ?></span>
                                    <span class="fb-story-time"><?php echo e($story->created_at->diffForHumans()); ?></span>
                                </div>
                            </div>
                            <a href="<?php echo e(url('v2/news/details/'.$story->id.'/'.$story->news_title_slug)); ?>" class="fb-story-link"></a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Create Post Section -->
        <div class="fb-create-post">
            <div class="fb-create-post-header">
                <div class="fb-create-avatar">
                    <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="NitiKotha">
                </div>
                <div class="fb-create-input">
                    <input type="text" placeholder="What's happening in the news today?" readonly>
                </div>
            </div>
            <div class="fb-create-post-actions">
                <button class="fb-action-btn">
                    <i class="fas fa-video"></i>
                    <span>Live Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-image"></i>
                    <span>Photo/Video</span>
                </button>
                <button class="fb-action-btn">
                    <i class="fas fa-smile"></i>
                    <span>Feeling/Activity</span>
                </button>
            </div>
        </div>

        <!-- News Feed Posts -->
        <div class="fb-news-feed">
            <?php if(isset($newnewspost)): ?>
                <?php $__currentLoopData = $newnewspost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <article class="fb-post">
                    <!-- Post Header -->
                    <div class="fb-post-header">
                        <div class="fb-post-avatar">
                            <?php
                                $photoPath = 'upload/no_image.jpg';
                                if (!empty($news->user->photo)) {
                                    // User photo already contains full path
                                    $photoPath = $news->user->photo;
                                }
                            ?>
                            <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($news->user->name); ?>">
                        </div>
                        <div class="fb-post-info">
                            <h4 class="fb-post-author"><?php echo e($news->user->name ?? 'NitiKotha'); ?></h4>
                            <div class="fb-post-meta">
                                <span class="fb-post-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                <span class="fb-post-separator">·</span>
                                <span class="fb-post-category"><?php echo e($news->category->category_name); ?></span>
                                <span class="fb-post-separator">·</span>
                                <i class="fas fa-globe-americas"></i>
                            </div>
                        </div>
                        <div class="fb-post-options">
                            <button class="fb-options-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="fb-post-content">
                        <h3 class="fb-post-title">
                            <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                <?php echo e($news->news_title); ?>

                            </a>
                        </h3>
                        <p class="fb-post-excerpt">
                            <?php echo e(Str::limit(strip_tags($news->news_details), 200)); ?>

                        </p>
                    </div>

                    <!-- Post Image -->
                    <?php if($news->image): ?>
                    <div class="fb-post-image">
                        <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>">
                        <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Post Stats -->
                    <div class="fb-post-stats">
                        <div class="fb-post-reactions">
                            <div class="fb-reaction-icons">
                                <span class="fb-reaction like">👍</span>
                                <span class="fb-reaction love">❤️</span>
                                <span class="fb-reaction wow">😮</span>
                            </div>
                            <span class="fb-reaction-count" id="likes-count-<?php echo e($news->id); ?>"><?php echo e($news->likes_count ?? 0); ?></span>
                        </div>
                        <div class="fb-post-engagement">
                            <span class="fb-comments-count" id="comments-count-<?php echo e($news->id); ?>"><?php echo e($news->comments_count ?? 0); ?> comments</span>
                            <span class="fb-shares-count" id="shares-count-<?php echo e($news->id); ?>"><?php echo e($news->shares_count ?? 0); ?> shares</span>
                        </div>
                    </div>

                    <!-- Post Actions -->
                    <div class="fb-post-actions">
                        <button class="fb-action-btn fb-like-btn <?php echo e($news->user_liked ? 'liked' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-thumbs-up"></i>
                            <span><?php echo e($news->user_liked ? 'Liked' : 'Like'); ?></span>
                        </button>
                        <button class="fb-action-btn fb-comment-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-comment"></i>
                            <span>Comment</span>
                        </button>
                        <button class="fb-action-btn fb-share-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                        <button class="fb-action-btn fb-save-btn <?php echo e($news->user_saved ? 'saved' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-bookmark"></i>
                            <span><?php echo e($news->user_saved ? 'Saved' : 'Save'); ?></span>
                        </button>
                    </div>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Sponsored Section -->
        <div class="fb-sponsored-section">
            <h4 class="fb-section-title">Sponsored</h4>
            <div class="fb-sponsored-item">
                <div class="fb-sponsored-image">
                    <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="Sponsored">
                </div>
                <div class="fb-sponsored-content">
                    <h5 class="fb-sponsored-title">NitiKotha Premium</h5>
                    <p class="fb-sponsored-text">Get unlimited access to premium news content</p>
                </div>
            </div>
        </div>

        <!-- Trending Topics -->
        <div class="fb-trending-section">
            <h4 class="fb-section-title">Trending Topics</h4>
            <div class="fb-trending-list">
                <?php if(isset($newspopular)): ?>
                    <?php $__currentLoopData = $newspopular->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $trending): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-trending-item">
                        <div class="fb-trending-rank"><?php echo e($index + 1); ?></div>
                        <div class="fb-trending-content">
                            <h5 class="fb-trending-title">
                                <a href="<?php echo e(url('v2/news/details/'.$trending->id.'/'.$trending->news_title_slug)); ?>">
                                    <?php echo e(Str::limit($trending->news_title, 60)); ?>

                                </a>
                            </h5>
                            <p class="fb-trending-meta"><?php echo e($trending->view_count ?? 0); ?> people talking about this</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Online Friends -->
        <div class="fb-contacts-section">
            <h4 class="fb-section-title">Contacts</h4>
            <div class="fb-contacts-list">
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">News Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="Contact">
                        <div class="fb-online-indicator"></div>
                    </div>
                    <span class="fb-contact-name">Editorial Team</span>
                </div>
                <div class="fb-contact-item">
                    <div class="fb-contact-avatar">
                        <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="Contact">
                    </div>
                    <span class="fb-contact-name">Support Team</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout_facebook_style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/index_facebook_style.blade.php ENDPATH**/ ?>