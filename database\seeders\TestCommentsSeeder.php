<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NewsComment;
use App\Models\NewsPost;
use App\Models\User;

class TestCommentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some news posts and users
        $posts = NewsPost::take(5)->get();
        $users = User::take(3)->get();

        if ($posts->isEmpty() || $users->isEmpty()) {
            $this->command->info('No posts or users found. Please create some first.');
            return;
        }

        $comments = [
            [
                'comment' => 'This is a great article! Very informative and well-written.',
                'is_approved' => true,
            ],
            [
                'comment' => 'I disagree with some points mentioned here. Could you provide more sources?',
                'is_approved' => false,
            ],
            [
                'comment' => 'Thanks for sharing this valuable information. It really helped me understand the topic better.',
                'is_approved' => true,
            ],
            [
                'comment' => 'This article needs more research. Some facts seem outdated.',
                'is_approved' => false,
            ],
            [
                'comment' => 'Excellent work! Looking forward to more articles like this.',
                'is_approved' => true,
            ],
            [
                'comment' => 'Could you please elaborate on the third point? It\'s not very clear.',
                'is_approved' => false,
            ],
            [
                'comment' => 'Very helpful article. Shared it with my colleagues.',
                'is_approved' => true,
            ],
            [
                'comment' => 'The examples provided are really good. Makes it easy to understand.',
                'is_approved' => false,
            ],
        ];

        foreach ($comments as $index => $commentData) {
            $post = $posts->random();
            $user = $users->random();

            NewsComment::create([
                'news_post_id' => $post->id,
                'user_id' => $user->id,
                'comment' => $commentData['comment'],
                'is_approved' => $commentData['is_approved'],
                'approved_by' => $commentData['is_approved'] ? 1 : null,
                'approved_at' => $commentData['is_approved'] ? now() : null,
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        // Add some guest comments
        $guestComments = [
            [
                'guest_name' => 'John Doe',
                'guest_email' => '<EMAIL>',
                'comment' => 'Great article! Keep up the good work.',
                'is_approved' => true,
            ],
            [
                'guest_name' => 'Jane Smith',
                'guest_email' => '<EMAIL>',
                'comment' => 'I have a question about this topic. Can someone help?',
                'is_approved' => false,
            ],
            [
                'guest_name' => 'Anonymous',
                'guest_email' => null,
                'comment' => 'This is very useful information. Thank you!',
                'is_approved' => true,
            ],
        ];

        foreach ($guestComments as $guestComment) {
            $post = $posts->random();

            NewsComment::create([
                'news_post_id' => $post->id,
                'user_id' => null,
                'guest_name' => $guestComment['guest_name'],
                'guest_email' => $guestComment['guest_email'],
                'comment' => $guestComment['comment'],
                'is_approved' => $guestComment['is_approved'],
                'approved_by' => $guestComment['is_approved'] ? 1 : null,
                'approved_at' => $guestComment['is_approved'] ? now() : null,
                'created_at' => now()->subDays(rand(1, 15)),
                'updated_at' => now()->subDays(rand(1, 15)),
            ]);
        }

        $this->command->info('Test comments created successfully!');
    }
}
