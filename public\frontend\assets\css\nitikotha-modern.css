/* ===================================
   NitiKotha Modern Header Design
   Glassmorphism + Neumorphism + Modern UI
   =================================== */

/* CSS Custom Properties */
:root {
    /* Light Theme Colors */
    --nk-primary: #667eea;
    --nk-primary-dark: #5a6fd8;
    --nk-secondary: #764ba2;
    --nk-accent: #f093fb;
    --nk-success: #4ade80;
    --nk-warning: #fbbf24;
    --nk-danger: #f87171;
    
    /* Neutral Colors */
    --nk-white: #ffffff;
    --nk-gray-50: #f9fafb;
    --nk-gray-100: #f3f4f6;
    --nk-gray-200: #e5e7eb;
    --nk-gray-300: #d1d5db;
    --nk-gray-400: #9ca3af;
    --nk-gray-500: #6b7280;
    --nk-gray-600: #4b5563;
    --nk-gray-700: #374151;
    --nk-gray-800: #1f2937;
    --nk-gray-900: #111827;
    
    /* Text Colors */
    --nk-text-primary: #1f2937;
    --nk-text-secondary: #6b7280;
    --nk-text-tertiary: #9ca3af;
    --nk-text-inverse: #ffffff;
    
    /* Background Colors */
    --nk-bg-primary: #ffffff;
    --nk-bg-secondary: #f9fafb;
    --nk-bg-tertiary: #f3f4f6;
    
    /* Glass Effect */
    --nk-glass-bg: rgba(255, 255, 255, 0.25);
    --nk-glass-border: rgba(255, 255, 255, 0.18);
    --nk-glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Neumorphism */
    --nk-neu-light: #ffffff;
    --nk-neu-dark: #d1d9e6;
    --nk-neu-shadow-light: 20px 20px 60px #d1d9e6;
    --nk-neu-shadow-dark: -20px -20px 60px #ffffff;
    
    /* Transitions */
    --nk-transition-fast: 0.15s ease-out;
    --nk-transition-normal: 0.3s ease-out;
    --nk-transition-slow: 0.5s ease-out;
    
    /* Spacing */
    --nk-header-height: 70px;
    --nk-border-radius: 16px;
    --nk-border-radius-sm: 8px;
    --nk-border-radius-lg: 24px;
}

/* Dark Theme */
[data-theme="dark"] {
    --nk-primary: #818cf8;
    --nk-primary-dark: #6366f1;
    --nk-secondary: #a78bfa;
    --nk-accent: #f472b6;
    
    --nk-text-primary: #f9fafb;
    --nk-text-secondary: #d1d5db;
    --nk-text-tertiary: #9ca3af;
    --nk-text-inverse: #1f2937;
    
    --nk-bg-primary: #1f2937;
    --nk-bg-secondary: #111827;
    --nk-bg-tertiary: #0f172a;
    
    --nk-glass-bg: rgba(31, 41, 55, 0.25);
    --nk-glass-border: rgba(255, 255, 255, 0.1);
    --nk-glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    
    --nk-neu-light: #2d3748;
    --nk-neu-dark: #1a202c;
    --nk-neu-shadow-light: 20px 20px 60px #1a202c;
    --nk-neu-shadow-dark: -20px -20px 60px #2d3748;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--nk-bg-secondary) 0%, var(--nk-bg-tertiary) 100%);
    color: var(--nk-text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
    transition: all var(--nk-transition-normal);
}

a {
    text-decoration: none;
    color: inherit;
    transition: all var(--nk-transition-fast);
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    transition: all var(--nk-transition-fast);
}

img {
    max-width: 100%;
    height: auto;
}

/* ===================================
   MODERN HEADER STYLES
   =================================== */

.nk-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--nk-header-height);
    z-index: 1000;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--nk-glass-border);
    transition: all var(--nk-transition-normal);
}

.nk-header-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: -1;
}

.nk-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
}

/* ===================================
   LEFT SECTION - BRAND
   =================================== */

.nk-header-left {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.nk-brand {
    display: flex;
    align-items: center;
}

.nk-brand-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    position: relative;
    overflow: hidden;
}

.nk-brand-link:hover {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(10px);
    transform: translateY(-1px);
}

.nk-logo-container {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.nk-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.nk-logo-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-accent));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
    filter: blur(8px);
}

.nk-brand-link:hover .nk-logo-glow {
    opacity: 0.6;
}

.nk-brand-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nk-brand-name {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.nk-brand-tagline {
    font-size: 11px;
    color: var(--nk-text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===================================
   CENTER SECTION - NAVIGATION
   =================================== */

.nk-header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
}

.nk-nav {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    padding: 8px;
    box-shadow: var(--nk-glass-shadow);
}

.nk-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    min-width: 80px;
    text-align: center;
}

.nk-nav-item:hover {
    background: var(--nk-glass-bg);
    transform: translateY(-2px);
}

.nk-nav-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-text-inverse);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nk-nav-icon {
    font-size: 18px;
    transition: all var(--nk-transition-fast);
}

.nk-nav-item:hover .nk-nav-icon {
    transform: scale(1.1);
}

.nk-nav-label {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.9;
}

.nk-nav-indicator {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--nk-primary);
    opacity: 0;
    transition: all var(--nk-transition-normal);
}

.nk-nav-item.active .nk-nav-indicator {
    opacity: 1;
    background: var(--nk-text-inverse);
}

/* ===================================
   RIGHT SECTION - SEARCH & ACTIONS
   =================================== */

.nk-header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 0 0 auto;
}

/* Search Container */
.nk-search-container {
    position: relative;
}

.nk-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    padding: 12px 20px;
    min-width: 280px;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-search-wrapper:hover,
.nk-search-wrapper:focus-within {
    background: var(--nk-bg-primary);
    border-color: var(--nk-primary);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.nk-search-icon {
    color: var(--nk-text-secondary);
    font-size: 16px;
    transition: all var(--nk-transition-fast);
}

.nk-search-wrapper:focus-within .nk-search-icon {
    color: var(--nk-primary);
}

.nk-search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--nk-text-primary);
    font-size: 14px;
    font-weight: 500;
    placeholder-color: var(--nk-text-tertiary);
}

.nk-search-input::placeholder {
    color: var(--nk-text-tertiary);
}

.nk-search-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-search-wrapper:focus-within .nk-search-backdrop {
    opacity: 0.05;
}

/* Theme Toggle */
.nk-theme-toggle {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-theme-toggle:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px) rotate(180deg);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-theme-icon {
    font-size: 18px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-normal);
}

.nk-theme-toggle:hover .nk-theme-icon {
    color: var(--nk-primary);
}

.nk-theme-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-theme-toggle:hover .nk-theme-indicator {
    opacity: 0.1;
}

/* User Actions */
.nk-user-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nk-action-btn {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-action-btn:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-action-btn i {
    font-size: 18px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-fast);
}

.nk-action-btn:hover i {
    color: var(--nk-primary);
    transform: scale(1.1);
}

.nk-notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, var(--nk-danger), #ff6b6b);
    color: var(--nk-text-inverse);
    font-size: 11px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(248, 113, 113, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.nk-action-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-action-btn:hover .nk-action-ripple {
    opacity: 0.1;
}

/* Profile Menu */
.nk-profile-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    transition: all var(--nk-transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.nk-profile-menu:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-profile-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.nk-profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.nk-profile-status {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--nk-success);
    border: 2px solid var(--nk-white);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nk-profile-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nk-profile-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nk-profile-arrow {
    font-size: 12px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-fast);
}

.nk-profile-menu:hover .nk-profile-arrow {
    color: var(--nk-primary);
    transform: rotate(180deg);
}

/* Auth Buttons */
.nk-auth-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: var(--nk-border-radius);
    font-size: 14px;
    font-weight: 600;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
    text-decoration: none;
}

.nk-login-btn {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    color: var(--nk-text-primary);
}

.nk-login-btn:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-register-btn {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-text-inverse);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nk-register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.nk-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--nk-accent), var(--nk-primary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-auth-btn:hover .nk-btn-glow {
    opacity: 0.1;
}

/* Mobile Menu Toggle */
.nk-mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    gap: 4px;
    transition: all var(--nk-transition-normal);
}

.nk-mobile-toggle:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-1px);
}

.nk-hamburger {
    width: 20px;
    height: 2px;
    background: var(--nk-text-primary);
    border-radius: 1px;
    transition: all var(--nk-transition-fast);
}

.nk-mobile-toggle:hover .nk-hamburger {
    background: var(--nk-primary);
}

/* ===================================
   MAIN CONTENT ADJUSTMENTS
   =================================== */

.nk-main {
    margin-top: var(--nk-header-height);
    min-height: calc(100vh - var(--nk-header-height));
    background: inherit;
}

/* ===================================
   TOOLTIPS
   =================================== */

[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--nk-gray-800);
    color: var(--nk-white);
    padding: 6px 12px;
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-fast);
    z-index: 1001;
    pointer-events: none;
}

[data-tooltip]:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--nk-gray-800);
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-fast);
    z-index: 1001;
    pointer-events: none;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    opacity: 1;
    visibility: visible;
}

/* ===================================
   ANIMATIONS & EFFECTS
   =================================== */

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

.nk-header {
    animation: slideInDown 0.6s ease-out;
}

.nk-nav-item {
    animation: fadeInUp 0.6s ease-out;
    animation-delay: calc(var(--i, 0) * 0.1s);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet */
@media (max-width: 1024px) {
    .nk-header-container {
        padding: 0 20px;
    }

    .nk-search-wrapper {
        min-width: 240px;
    }

    .nk-brand-text {
        display: none;
    }

    .nk-nav-label {
        display: none;
    }

    .nk-nav-item {
        min-width: 60px;
        padding: 12px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    :root {
        --nk-header-height: 60px;
    }

    .nk-header-container {
        padding: 0 16px;
    }

    .nk-header-center {
        display: none;
    }

    .nk-search-wrapper {
        min-width: 200px;
    }

    .nk-mobile-toggle {
        display: flex;
    }

    .nk-user-actions {
        gap: 8px;
    }

    .nk-action-btn,
    .nk-theme-toggle {
        width: 44px;
        height: 44px;
    }

    .nk-profile-menu {
        padding: 6px 12px;
    }

    .nk-profile-avatar {
        width: 36px;
        height: 36px;
    }

    .nk-profile-name {
        display: none;
    }

    .nk-auth-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .nk-auth-btn span {
        display: none;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .nk-header-container {
        padding: 0 12px;
    }

    .nk-search-wrapper {
        min-width: 160px;
        padding: 10px 16px;
    }

    .nk-brand-name {
        font-size: 18px;
    }

    .nk-logo-container {
        width: 40px;
        height: 40px;
    }
}

/* ===================================
   DARK MODE SPECIFIC STYLES
   =================================== */

[data-theme="dark"] .nk-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .nk-notification-badge {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

[data-theme="dark"] [data-tooltip]:before {
    background: var(--nk-gray-200);
    color: var(--nk-gray-800);
}

[data-theme="dark"] [data-tooltip]:after {
    border-bottom-color: var(--nk-gray-200);
}

/* ===================================
   PERFORMANCE OPTIMIZATIONS
   =================================== */

.nk-header {
    will-change: transform;
    contain: layout style paint;
}

.nk-nav-item,
.nk-action-btn,
.nk-theme-toggle,
.nk-auth-btn {
    will-change: transform;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .nk-notification-badge {
        animation: none;
    }
}

/* ===================================
   PRINT STYLES
   =================================== */

@media print {
    .nk-header {
        display: none !important;
    }

    .nk-main {
        margin-top: 0 !important;
    }
}

/* ===================================
   SEARCH DROPDOWN
   =================================== */

.nk-search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    box-shadow: var(--nk-glass-shadow);
    backdrop-filter: blur(20px);
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1002;
    display: none;
}

.nk-search-result {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
    border-bottom: 1px solid var(--nk-glass-border);
}

.nk-search-result:last-child {
    border-bottom: none;
}

.nk-search-result:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.nk-search-result i {
    color: var(--nk-text-secondary);
    font-size: 14px;
}

.nk-search-result:hover i {
    color: var(--nk-primary);
}

/* ===================================
   MOBILE OVERLAY & MENU
   =================================== */

.nk-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-normal);
}

.nk-mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

.nk-mobile-menu {
    position: absolute;
    top: var(--nk-header-height);
    left: 0;
    right: 0;
    background: var(--nk-bg-primary);
    border-bottom: 1px solid var(--nk-glass-border);
    box-shadow: var(--nk-glass-shadow);
    transform: translateY(-100%);
    transition: transform var(--nk-transition-normal);
}

.nk-mobile-overlay.active .nk-mobile-menu {
    transform: translateY(0);
}

.nk-mobile-nav {
    padding: 20px;
}

.nk-mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    color: var(--nk-text-primary);
    background: var(--nk-glass-bg);
    border-radius: var(--nk-border-radius);
    margin-bottom: 12px;
    transition: all var(--nk-transition-fast);
    font-weight: 500;
}

.nk-mobile-nav-item:hover {
    background: var(--nk-primary);
    color: var(--nk-text-inverse);
    transform: translateX(8px);
}

.nk-mobile-nav-item i {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* ===================================
   SCROLL EFFECTS
   =================================== */

.nk-header.scrolled {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(30px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nk-header.hidden {
    transform: translateY(-100%);
}

/* ===================================
   LOADING STATES
   =================================== */

.nk-search-wrapper.searching::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--nk-glass-border);
    border-top-color: var(--nk-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

/* ===================================
   ENHANCED TOOLTIPS
   =================================== */

.nk-tooltip {
    position: fixed;
    background: var(--nk-gray-800);
    color: var(--nk-white);
    padding: 8px 12px;
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 500;
    z-index: 1003;
    opacity: 0;
    transform: translateY(5px);
    transition: all var(--nk-transition-fast);
    pointer-events: none;
    white-space: nowrap;
}

.nk-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.nk-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--nk-gray-800);
}

/* ===================================
   FACEBOOK-STYLE LAYOUT COMPATIBILITY
   =================================== */

/* Main Container */
.fb-main-container {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    min-height: calc(100vh - var(--nk-header-height));
}

/* Left Sidebar */
.fb-left-sidebar {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: calc(var(--nk-header-height) + 24px);
    box-shadow: var(--nk-glass-shadow);
}

/* Profile Card */
.fb-profile-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--nk-bg-primary);
    border-radius: var(--nk-border-radius);
    margin-bottom: 20px;
    transition: all var(--nk-transition-normal);
}

.fb-profile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-profile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-profile-info {
    flex: 1;
}

.fb-profile-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
}

.fb-profile-tagline {
    font-size: 12px;
    color: var(--nk-text-secondary);
    margin: 0;
}

/* Navigation Links */
.fb-nav-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.fb-nav-links li {
    margin-bottom: 8px;
}

.fb-nav-links a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    font-weight: 500;
}

.fb-nav-links a:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-nav-links i {
    font-size: 18px;
    width: 20px;
    text-align: center;
    color: var(--nk-text-secondary);
    transition: color var(--nk-transition-fast);
}

.fb-nav-links a:hover i {
    color: var(--nk-primary);
}

/* Footer Links */
.fb-footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-footer-links a {
    font-size: 12px;
    color: var(--nk-text-tertiary);
    transition: color var(--nk-transition-fast);
}

.fb-footer-links a:hover {
    color: var(--nk-primary);
}

/* Main Feed */
.fb-main-feed {
    background: transparent;
    padding: 0;
}

/* Stories Container */
.fb-stories-container {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-stories-wrapper {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: thin;
    scrollbar-color: var(--nk-primary) transparent;
}

.fb-stories-wrapper::-webkit-scrollbar {
    height: 6px;
}

.fb-stories-wrapper::-webkit-scrollbar-track {
    background: var(--nk-glass-bg);
    border-radius: 3px;
}

.fb-stories-wrapper::-webkit-scrollbar-thumb {
    background: var(--nk-primary);
    border-radius: 3px;
}

/* Story Cards */
.fb-story-card {
    position: relative;
    width: 120px;
    height: 200px;
    border-radius: var(--nk-border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--nk-transition-normal);
    flex-shrink: 0;
}

.fb-story-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.fb-story-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.fb-story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-story-card:hover .fb-story-image img {
    transform: scale(1.05);
}

.fb-story-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    z-index: 1;
}

.fb-story-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px 12px;
    z-index: 2;
    color: var(--nk-white);
}

.fb-story-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 8px;
    border: 2px solid var(--nk-white);
}

.fb-story-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-story-title {
    font-size: 12px;
    font-weight: 600;
    line-height: 1.3;
    display: block;
}

.fb-story-meta {
    font-size: 10px;
    opacity: 0.8;
    margin-top: 4px;
}

/* Create Story */
.fb-create-story {
    background: var(--nk-bg-primary);
    border: 2px dashed var(--nk-glass-border);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--nk-text-primary);
}

.fb-create-story:hover {
    border-color: var(--nk-primary);
    background: var(--nk-glass-bg);
}

.fb-story-icon {
    width: 40px;
    height: 40px;
    background: var(--nk-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nk-white);
    font-size: 18px;
    margin-bottom: 8px;
}

.fb-story-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--nk-text-primary);
}

/* Create Post Section */
.fb-create-post {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-create-post-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.fb-create-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-create-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-create-input {
    flex: 1;
}

.fb-create-input input {
    width: 100%;
    padding: 12px 20px;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    background: var(--nk-bg-primary);
    color: var(--nk-text-primary);
    font-size: 14px;
    transition: all var(--nk-transition-normal);
}

.fb-create-input input:focus {
    outline: none;
    border-color: var(--nk-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.fb-create-input input::placeholder {
    color: var(--nk-text-tertiary);
}

.fb-create-post-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: transparent;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    flex: 1;
    justify-content: center;
}

.fb-action-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    border-color: var(--nk-primary);
}

.fb-action-btn i {
    font-size: 16px;
}

/* News Posts */
.fb-post {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--nk-glass-shadow);
    transition: all var(--nk-transition-normal);
}

.fb-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
}

.fb-post-header {
    padding: 20px 20px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.fb-post-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-post-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-post-info {
    flex: 1;
}

.fb-post-author {
    font-size: 15px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
}

.fb-post-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--nk-text-secondary);
}

.fb-post-category {
    background: var(--nk-primary);
    color: var(--nk-white);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.fb-post-content {
    padding: 16px 20px;
}

.fb-post-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.fb-post-title a {
    color: inherit;
    transition: color var(--nk-transition-fast);
}

.fb-post-title a:hover {
    color: var(--nk-primary);
}

.fb-post-excerpt {
    font-size: 14px;
    color: var(--nk-text-secondary);
    line-height: 1.6;
    margin: 0 0 16px 0;
}

.fb-post-image {
    position: relative;
    margin: 0 -20px 16px;
    overflow: hidden;
}

.fb-post-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-post:hover .fb-post-image img {
    transform: scale(1.02);
}

.fb-post-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-post-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--nk-text-secondary);
}

.fb-post-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.fb-post-buttons {
    display: flex;
    gap: 8px;
}

.fb-post-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-post-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    border-color: var(--nk-primary);
}

.fb-post-btn.liked {
    color: var(--nk-danger);
    border-color: var(--nk-danger);
}

.fb-post-btn.shared {
    color: var(--nk-success);
    border-color: var(--nk-success);
}

/* Right Sidebar */
.fb-right-sidebar {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    height: fit-content;
    position: sticky;
    top: calc(var(--nk-header-height) + 24px);
    box-shadow: var(--nk-glass-shadow);
}

/* Sponsored Ads */
.fb-sponsored-ad {
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 16px;
    margin-bottom: 20px;
    transition: all var(--nk-transition-normal);
}

.fb-sponsored-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-sponsored-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.fb-sponsored-label {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.fb-sponsored-menu {
    color: var(--nk-text-tertiary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--nk-transition-fast);
}

.fb-sponsored-menu:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-text-primary);
}

.fb-sponsored-content {
    text-align: center;
}

.fb-sponsored-image {
    width: 100%;
    height: 150px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    margin-bottom: 12px;
}

.fb-sponsored-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-sponsored-ad:hover .fb-sponsored-image img {
    transform: scale(1.05);
}

.fb-sponsored-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.fb-sponsored-description {
    font-size: 12px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0 0 12px 0;
}

.fb-sponsored-cta {
    display: inline-block;
    padding: 8px 16px;
    background: var(--nk-primary);
    color: var(--nk-white);
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--nk-transition-fast);
}

.fb-sponsored-cta:hover {
    background: var(--nk-primary-dark);
    transform: translateY(-1px);
}

/* Widget Sections */
.fb-widget {
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.fb-widget-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--nk-primary);
    position: relative;
}

.fb-widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--nk-secondary);
}

/* Trending News */
.fb-trending-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--nk-glass-border);
    transition: all var(--nk-transition-fast);
}

.fb-trending-item:last-child {
    border-bottom: none;
}

.fb-trending-item:hover {
    background: var(--nk-glass-bg);
    margin: 0 -16px;
    padding: 12px 16px;
    border-radius: var(--nk-border-radius-sm);
}

.fb-trending-image {
    width: 60px;
    height: 60px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-trending-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-trending-content {
    flex: 1;
}

.fb-trending-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--nk-text-primary);
    line-height: 1.4;
    margin: 0 0 6px 0;
}

.fb-trending-title a {
    color: inherit;
    transition: color var(--nk-transition-fast);
}

.fb-trending-title a:hover {
    color: var(--nk-primary);
}

.fb-trending-meta {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Load More Button */
.fb-load-more {
    display: flex;
    justify-content: center;
    margin: 32px 0;
}

.fb-load-more-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    color: var(--nk-text-primary);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-normal);
    box-shadow: var(--nk-glass-shadow);
}

.fb-load-more-btn:hover {
    background: var(--nk-primary);
    color: var(--nk-white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.fb-load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.fb-load-more-btn i {
    transition: transform var(--nk-transition-fast);
}

.fb-load-more-btn:hover i {
    transform: rotate(180deg);
}

/* ===================================
   RESPONSIVE LAYOUT DESIGN
   =================================== */

/* Large Desktop */
@media (min-width: 1400px) {
    .fb-main-container {
        grid-template-columns: 320px 1fr 360px;
        gap: 32px;
        padding: 32px;
    }
}

/* Desktop */
@media (max-width: 1200px) {
    .fb-main-container {
        grid-template-columns: 260px 1fr 300px;
        gap: 20px;
        padding: 20px;
    }

    .fb-left-sidebar,
    .fb-right-sidebar {
        padding: 16px;
    }
}

/* Tablet */
@media (max-width: 1024px) {
    .fb-main-container {
        grid-template-columns: 1fr 280px;
        gap: 20px;
    }

    .fb-left-sidebar {
        display: none;
    }

    .fb-stories-wrapper {
        gap: 12px;
    }

    .fb-story-card {
        width: 100px;
        height: 160px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .fb-main-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px;
    }

    .fb-right-sidebar {
        display: none;
    }

    .fb-stories-container {
        padding: 16px;
        margin-bottom: 16px;
    }

    .fb-story-card {
        width: 90px;
        height: 140px;
    }

    .fb-create-post {
        padding: 16px;
        margin-bottom: 16px;
    }

    .fb-post {
        margin-bottom: 16px;
    }

    .fb-post-header,
    .fb-post-content,
    .fb-post-actions {
        padding-left: 16px;
        padding-right: 16px;
    }

    .fb-post-image {
        margin-left: -16px;
        margin-right: -16px;
    }

    .fb-post-title {
        font-size: 16px;
    }

    .fb-post-actions {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .fb-post-buttons {
        justify-content: space-between;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .fb-main-container {
        padding: 12px;
        gap: 12px;
    }

    .fb-stories-container {
        padding: 12px;
    }

    .fb-story-card {
        width: 80px;
        height: 120px;
    }

    .fb-create-post,
    .fb-post {
        padding: 12px;
        margin-bottom: 12px;
    }

    .fb-post-header,
    .fb-post-content,
    .fb-post-actions {
        padding-left: 12px;
        padding-right: 12px;
    }

    .fb-post-image {
        margin-left: -12px;
        margin-right: -12px;
    }

    .fb-create-post-actions {
        flex-direction: column;
        gap: 8px;
    }

    .fb-action-btn {
        justify-content: flex-start;
    }
}

/* ===================================
   CATEGORY & DETAIL PAGES COMPATIBILITY
   =================================== */

/* Ensure v2 pages use modern layout */
.nk-main .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.nk-main .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
}

.nk-main .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding: 0 12px;
}

.nk-main .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 12px;
}

@media (max-width: 992px) {
    .nk-main .col-lg-8,
    .nk-main .col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Modern styling for category and detail pages */
.category-header,
.article-header {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.news-grid,
.news-card,
.article-content {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    box-shadow: var(--nk-glass-shadow);
}

/* Ensure consistent spacing */
.nk-main > * {
    margin-top: 24px;
}

.nk-main > *:first-child {
    margin-top: 0;
}

/* ===================================
   CATEGORY PAGE SPECIFIC STYLES
   =================================== */

/* Categories Section */
.fb-categories-section {
    margin-bottom: 24px;
}

.fb-section-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--nk-primary);
}

.fb-categories-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-category-group {
    position: relative;
}

.fb-category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    cursor: pointer;
    position: relative;
}

.fb-category-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-category-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-color: var(--nk-primary);
}

.fb-category-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fb-category-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.fb-category-name {
    font-size: 14px;
    font-weight: 600;
}

.fb-category-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-category-count {
    font-size: 12px;
    background: var(--nk-glass-bg);
    padding: 4px 8px;
    border-radius: 12px;
    color: var(--nk-text-secondary);
}

.fb-category-item.active .fb-category-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--nk-white);
}

.fb-category-toggle {
    background: none;
    border: none;
    color: var(--nk-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--nk-transition-fast);
}

.fb-category-toggle:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.fb-category-item.active .fb-category-toggle {
    color: var(--nk-white);
}

.fb-category-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.fb-category-toggle {
    position: relative;
    z-index: 2;
}

/* Subcategories */
.fb-subcategories {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--nk-transition-normal);
    background: var(--nk-bg-secondary);
    border-radius: 0 0 var(--nk-border-radius-sm) var(--nk-border-radius-sm);
    margin-top: 4px;
}

.fb-subcategories.expanded {
    max-height: 500px;
    padding: 8px 0;
}

.fb-subcategory-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 24px;
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
    border-left: 3px solid transparent;
}

.fb-subcategory-item:hover {
    background: var(--nk-glass-bg);
    border-left-color: var(--nk-primary);
    color: var(--nk-primary);
}

.fb-subcategory-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.fb-subcategory-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
}

.fb-subcategory-count {
    font-size: 11px;
    background: var(--nk-glass-bg);
    padding: 2px 6px;
    border-radius: 8px;
    color: var(--nk-text-tertiary);
}

/* Navigation Menu */
.fb-nav-menu {
    margin-bottom: 24px;
}

.fb-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    margin-bottom: 4px;
    font-weight: 500;
}

.fb-nav-item:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-nav-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
}

.fb-nav-item i {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Page Header */
.fb-page-header {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
}

.fb-page-description {
    font-size: 16px;
    color: var(--nk-text-secondary);
    margin: 0;
}

.fb-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
}

.fb-breadcrumb a {
    color: var(--nk-primary);
    transition: color var(--nk-transition-fast);
}

.fb-breadcrumb a:hover {
    color: var(--nk-primary-dark);
}

.fb-breadcrumb i {
    color: var(--nk-text-tertiary);
    font-size: 12px;
}

/* ===================================
   RIGHT SIDEBAR SPECIFIC STYLES
   =================================== */

/* Sponsored Section */
.fb-sponsored-section {
    margin-bottom: 24px;
}

.fb-sponsored-item {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    cursor: pointer;
}

.fb-sponsored-item:hover {
    background: var(--nk-glass-bg);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-sponsored-image {
    width: 60px;
    height: 60px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-sponsored-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-sponsored-content {
    flex: 1;
}

.fb-sponsored-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-sponsored-text {
    font-size: 12px;
    color: var(--nk-text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* Trending Section */
.fb-trending-section {
    margin-bottom: 24px;
}

.fb-trending-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fb-trending-item {
    display: flex;
    gap: 12px;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
}

.fb-trending-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
}

.fb-trending-rank {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
}

.fb-trending-content {
    flex: 1;
}

.fb-trending-title {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-trending-title a {
    color: var(--nk-text-primary);
    transition: color var(--nk-transition-fast);
}

.fb-trending-title a:hover {
    color: var(--nk-primary);
}

.fb-trending-meta {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Contact Info Section */
.fb-contact-section {
    margin-bottom: 24px;
}

.fb-contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    margin-bottom: 8px;
    transition: all var(--nk-transition-fast);
}

.fb-contact-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
}

.fb-contact-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.fb-contact-info {
    flex: 1;
}

.fb-contact-label {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    margin: 0 0 2px 0;
}

.fb-contact-value {
    font-size: 13px;
    color: var(--nk-text-primary);
    font-weight: 500;
    margin: 0;
}

/* Related Categories */
.fb-related-section {
    margin-bottom: 24px;
}

.fb-related-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-related-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
}

.fb-related-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-related-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.fb-related-info {
    flex: 1;
}

.fb-related-name {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 2px 0;
}

.fb-related-count {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    margin: 0;
}
