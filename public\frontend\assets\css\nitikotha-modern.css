/* ===================================
   NitiKotha Modern Header Design
   Glassmorphism + Neumorphism + Modern UI
   =================================== */

/* CSS Custom Properties */
:root {
    /* Light Theme Colors */
    --nk-primary: #667eea;
    --nk-primary-dark: #5a6fd8;
    --nk-secondary: #764ba2;
    --nk-accent: #f093fb;
    --nk-success: #4ade80;
    --nk-warning: #fbbf24;
    --nk-danger: #f87171;
    
    /* Neutral Colors */
    --nk-white: #ffffff;
    --nk-gray-50: #f9fafb;
    --nk-gray-100: #f3f4f6;
    --nk-gray-200: #e5e7eb;
    --nk-gray-300: #d1d5db;
    --nk-gray-400: #9ca3af;
    --nk-gray-500: #6b7280;
    --nk-gray-600: #4b5563;
    --nk-gray-700: #374151;
    --nk-gray-800: #1f2937;
    --nk-gray-900: #111827;
    
    /* Text Colors */
    --nk-text-primary: #1f2937;
    --nk-text-secondary: #6b7280;
    --nk-text-tertiary: #9ca3af;
    --nk-text-inverse: #ffffff;
    
    /* Background Colors */
    --nk-bg-primary: #ffffff;
    --nk-bg-secondary: #f9fafb;
    --nk-bg-tertiary: #f3f4f6;
    
    /* Glass Effect */
    --nk-glass-bg: rgba(255, 255, 255, 0.25);
    --nk-glass-border: rgba(255, 255, 255, 0.18);
    --nk-glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Neumorphism */
    --nk-neu-light: #ffffff;
    --nk-neu-dark: #d1d9e6;
    --nk-neu-shadow-light: 20px 20px 60px #d1d9e6;
    --nk-neu-shadow-dark: -20px -20px 60px #ffffff;
    
    /* Transitions */
    --nk-transition-fast: 0.15s ease-out;
    --nk-transition-normal: 0.3s ease-out;
    --nk-transition-slow: 0.5s ease-out;
    
    /* Spacing */
    --nk-header-height: 70px;
    --nk-border-radius: 16px;
    --nk-border-radius-sm: 8px;
    --nk-border-radius-lg: 24px;
}

/* Dark Theme */
[data-theme="dark"] {
    --nk-primary: #818cf8;
    --nk-primary-dark: #6366f1;
    --nk-secondary: #a78bfa;
    --nk-accent: #f472b6;
    
    --nk-text-primary: #f9fafb;
    --nk-text-secondary: #d1d5db;
    --nk-text-tertiary: #9ca3af;
    --nk-text-inverse: #1f2937;
    
    --nk-bg-primary: #1f2937;
    --nk-bg-secondary: #111827;
    --nk-bg-tertiary: #0f172a;
    
    --nk-glass-bg: rgba(31, 41, 55, 0.25);
    --nk-glass-border: rgba(255, 255, 255, 0.1);
    --nk-glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    
    --nk-neu-light: #2d3748;
    --nk-neu-dark: #1a202c;
    --nk-neu-shadow-light: 20px 20px 60px #1a202c;
    --nk-neu-shadow-dark: -20px -20px 60px #2d3748;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--nk-bg-secondary) 0%, var(--nk-bg-tertiary) 100%);
    color: var(--nk-text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
    transition: all var(--nk-transition-normal);
}

a {
    text-decoration: none;
    color: inherit;
    transition: all var(--nk-transition-fast);
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    transition: all var(--nk-transition-fast);
}

img {
    max-width: 100%;
    height: auto;
}

/* ===================================
   MODERN HEADER STYLES
   =================================== */

.nk-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--nk-header-height);
    z-index: 1000;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--nk-glass-border);
    transition: all var(--nk-transition-normal);
}

.nk-header-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: -1;
}

.nk-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
}

/* ===================================
   LEFT SECTION - BRAND
   =================================== */

.nk-header-left {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.nk-brand {
    display: flex;
    align-items: center;
}

.nk-brand-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    position: relative;
    overflow: hidden;
}

.nk-brand-link:hover {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(10px);
    transform: translateY(-1px);
}

.nk-logo-container {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.nk-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.nk-logo-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-accent));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
    filter: blur(8px);
}

.nk-brand-link:hover .nk-logo-glow {
    opacity: 0.6;
}

.nk-brand-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nk-brand-name {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.nk-brand-tagline {
    font-size: 11px;
    color: var(--nk-text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===================================
   CENTER SECTION - NAVIGATION
   =================================== */

.nk-header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
}

.nk-nav {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    padding: 8px;
    box-shadow: var(--nk-glass-shadow);
}

.nk-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    min-width: 80px;
    text-align: center;
}

.nk-nav-item:hover {
    background: var(--nk-glass-bg);
    transform: translateY(-2px);
}

.nk-nav-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-text-inverse);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nk-nav-icon {
    font-size: 18px;
    transition: all var(--nk-transition-fast);
}

.nk-nav-item:hover .nk-nav-icon {
    transform: scale(1.1);
}

.nk-nav-label {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.9;
}

.nk-nav-indicator {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--nk-primary);
    opacity: 0;
    transition: all var(--nk-transition-normal);
}

.nk-nav-item.active .nk-nav-indicator {
    opacity: 1;
    background: var(--nk-text-inverse);
}

/* ===================================
   RIGHT SECTION - SEARCH & ACTIONS
   =================================== */

.nk-header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 0 0 auto;
}

/* Search Container */
.nk-search-container {
    position: relative;
}

.nk-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    padding: 12px 20px;
    min-width: 280px;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-search-wrapper:hover,
.nk-search-wrapper:focus-within {
    background: var(--nk-bg-primary);
    border-color: var(--nk-primary);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.nk-search-icon {
    color: var(--nk-text-secondary);
    font-size: 16px;
    transition: all var(--nk-transition-fast);
}

.nk-search-wrapper:focus-within .nk-search-icon {
    color: var(--nk-primary);
}

.nk-search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    color: var(--nk-text-primary);
    font-size: 14px;
    font-weight: 500;
    placeholder-color: var(--nk-text-tertiary);
}

.nk-search-input::placeholder {
    color: var(--nk-text-tertiary);
}

.nk-search-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-search-wrapper:focus-within .nk-search-backdrop {
    opacity: 0.05;
}

/* Theme Toggle */
.nk-theme-toggle {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-theme-toggle:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px) rotate(180deg);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-theme-icon {
    font-size: 18px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-normal);
}

.nk-theme-toggle:hover .nk-theme-icon {
    color: var(--nk-primary);
}

.nk-theme-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-theme-toggle:hover .nk-theme-indicator {
    opacity: 0.1;
}

/* User Actions */
.nk-user-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nk-action-btn {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
}

.nk-action-btn:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-action-btn i {
    font-size: 18px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-fast);
}

.nk-action-btn:hover i {
    color: var(--nk-primary);
    transform: scale(1.1);
}

.nk-notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, var(--nk-danger), #ff6b6b);
    color: var(--nk-text-inverse);
    font-size: 11px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(248, 113, 113, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.nk-action-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-action-btn:hover .nk-action-ripple {
    opacity: 0.1;
}

/* Profile Menu */
.nk-profile-menu {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    transition: all var(--nk-transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.nk-profile-menu:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-profile-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.nk-profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.nk-profile-status {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--nk-success);
    border: 2px solid var(--nk-white);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nk-profile-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.nk-profile-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nk-profile-arrow {
    font-size: 12px;
    color: var(--nk-text-secondary);
    transition: all var(--nk-transition-fast);
}

.nk-profile-menu:hover .nk-profile-arrow {
    color: var(--nk-primary);
    transform: rotate(180deg);
}

/* Auth Buttons */
.nk-auth-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: var(--nk-border-radius);
    font-size: 14px;
    font-weight: 600;
    transition: all var(--nk-transition-normal);
    overflow: hidden;
    text-decoration: none;
}

.nk-login-btn {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    color: var(--nk-text-primary);
}

.nk-login-btn:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nk-register-btn {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-text-inverse);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nk-register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.nk-btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--nk-accent), var(--nk-primary));
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: -1;
}

.nk-auth-btn:hover .nk-btn-glow {
    opacity: 0.1;
}

/* Mobile Menu Toggle */
.nk-mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    gap: 4px;
    transition: all var(--nk-transition-normal);
}

.nk-mobile-toggle:hover {
    background: var(--nk-bg-primary);
    transform: translateY(-1px);
}

.nk-hamburger {
    width: 20px;
    height: 2px;
    background: var(--nk-text-primary);
    border-radius: 1px;
    transition: all var(--nk-transition-fast);
}

.nk-mobile-toggle:hover .nk-hamburger {
    background: var(--nk-primary);
}

/* ===================================
   MAIN CONTENT ADJUSTMENTS
   =================================== */

.nk-main {
    margin-top: var(--nk-header-height);
    min-height: calc(100vh - var(--nk-header-height));
    background: inherit;
}

/* ===================================
   TOOLTIPS
   =================================== */

[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--nk-gray-800);
    color: var(--nk-white);
    padding: 6px 12px;
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-fast);
    z-index: 1001;
    pointer-events: none;
}

[data-tooltip]:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--nk-gray-800);
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-fast);
    z-index: 1001;
    pointer-events: none;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    opacity: 1;
    visibility: visible;
}

/* ===================================
   ANIMATIONS & EFFECTS
   =================================== */

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

.nk-header {
    animation: slideInDown 0.6s ease-out;
}

.nk-nav-item {
    animation: fadeInUp 0.6s ease-out;
    animation-delay: calc(var(--i, 0) * 0.1s);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Tablet */
@media (max-width: 1024px) {
    .nk-header-container {
        padding: 0 20px;
    }

    .nk-search-wrapper {
        min-width: 240px;
    }

    .nk-brand-text {
        display: none;
    }

    .nk-nav-label {
        display: none;
    }

    .nk-nav-item {
        min-width: 60px;
        padding: 12px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    :root {
        --nk-header-height: 60px;
    }

    .nk-header-container {
        padding: 0 16px;
    }

    .nk-header-center {
        display: none;
    }

    .nk-search-wrapper {
        min-width: 200px;
    }

    .nk-mobile-toggle {
        display: flex;
    }

    .nk-user-actions {
        gap: 8px;
    }

    .nk-action-btn,
    .nk-theme-toggle {
        width: 44px;
        height: 44px;
    }

    .nk-profile-menu {
        padding: 6px 12px;
    }

    .nk-profile-avatar {
        width: 36px;
        height: 36px;
    }

    .nk-profile-name {
        display: none;
    }

    .nk-auth-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .nk-auth-btn span {
        display: none;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .nk-header-container {
        padding: 0 12px;
    }

    .nk-search-wrapper {
        min-width: 160px;
        padding: 10px 16px;
    }

    .nk-brand-name {
        font-size: 18px;
    }

    .nk-logo-container {
        width: 40px;
        height: 40px;
    }
}

/* ===================================
   DARK MODE SPECIFIC STYLES
   =================================== */

[data-theme="dark"] .nk-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .nk-notification-badge {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

[data-theme="dark"] [data-tooltip]:before {
    background: var(--nk-gray-200);
    color: var(--nk-gray-800);
}

[data-theme="dark"] [data-tooltip]:after {
    border-bottom-color: var(--nk-gray-200);
}

/* ===================================
   PERFORMANCE OPTIMIZATIONS
   =================================== */

.nk-header {
    will-change: transform;
    contain: layout style paint;
}

.nk-nav-item,
.nk-action-btn,
.nk-theme-toggle,
.nk-auth-btn {
    will-change: transform;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .nk-notification-badge {
        animation: none;
    }
}

/* ===================================
   PRINT STYLES
   =================================== */

@media print {
    .nk-header {
        display: none !important;
    }

    .nk-main {
        margin-top: 0 !important;
    }
}

/* ===================================
   SEARCH DROPDOWN
   =================================== */

.nk-search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    box-shadow: var(--nk-glass-shadow);
    backdrop-filter: blur(20px);
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1002;
    display: none;
}

.nk-search-result {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
    border-bottom: 1px solid var(--nk-glass-border);
}

.nk-search-result:last-child {
    border-bottom: none;
}

.nk-search-result:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.nk-search-result i {
    color: var(--nk-text-secondary);
    font-size: 14px;
}

.nk-search-result:hover i {
    color: var(--nk-primary);
}

/* ===================================
   MOBILE OVERLAY & MENU
   =================================== */

.nk-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-normal);
}

.nk-mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

.nk-mobile-menu {
    position: absolute;
    top: var(--nk-header-height);
    left: 0;
    right: 0;
    background: var(--nk-bg-primary);
    border-bottom: 1px solid var(--nk-glass-border);
    box-shadow: var(--nk-glass-shadow);
    transform: translateY(-100%);
    transition: transform var(--nk-transition-normal);
}

.nk-mobile-overlay.active .nk-mobile-menu {
    transform: translateY(0);
}

.nk-mobile-nav {
    padding: 20px;
}

.nk-mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    color: var(--nk-text-primary);
    background: var(--nk-glass-bg);
    border-radius: var(--nk-border-radius);
    margin-bottom: 12px;
    transition: all var(--nk-transition-fast);
    font-weight: 500;
}

.nk-mobile-nav-item:hover {
    background: var(--nk-primary);
    color: var(--nk-text-inverse);
    transform: translateX(8px);
}

.nk-mobile-nav-item i {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* ===================================
   SCROLL EFFECTS
   =================================== */

.nk-header.scrolled {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(30px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nk-header.hidden {
    transform: translateY(-100%);
}

/* ===================================
   LOADING STATES
   =================================== */

.nk-search-wrapper.searching::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--nk-glass-border);
    border-top-color: var(--nk-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

/* ===================================
   ENHANCED TOOLTIPS
   =================================== */

.nk-tooltip {
    position: fixed;
    background: var(--nk-gray-800);
    color: var(--nk-white);
    padding: 8px 12px;
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 500;
    z-index: 1003;
    opacity: 0;
    transform: translateY(5px);
    transition: all var(--nk-transition-fast);
    pointer-events: none;
    white-space: nowrap;
}

.nk-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.nk-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--nk-gray-800);
}

/* ===================================
   FACEBOOK-STYLE LAYOUT COMPATIBILITY
   =================================== */

/* Main Container */
.fb-main-container {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    min-height: calc(100vh - var(--nk-header-height));
    grid-template-areas: "sidebar main rightbar";
}

/* Ensure all children are visible */
.fb-main-container > * {
    min-height: 100px; /* Ensure visibility for debugging */
}

/* Ensure proper visibility and force display */
.fb-left-sidebar,
.fb-right-sidebar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 250px;
}

/* Force grid layout with explicit sizing and proper spacing */
.fb-main-container {
    display: grid !important;
    grid-template-columns: 280px 1fr 320px !important;
    grid-template-areas: "sidebar main rightbar" !important;
    width: 100% !important;
    min-height: calc(100vh - var(--nk-header-height)) !important;
    gap: 24px !important;
    padding: 24px !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
}

/* Ensure grid items are properly assigned with explicit positioning */
.fb-left-sidebar {
    grid-area: sidebar !important;
    grid-column: 1 !important;
    grid-row: 1 !important;
}

.fb-main-feed {
    grid-area: main !important;
    grid-column: 2 !important;
    grid-row: 1 !important;
}

.fb-right-sidebar {
    grid-area: rightbar !important;
    grid-column: 3 !important;
    grid-row: 1 !important;
}

/* Force sidebar visibility - clean production layout */
.fb-left-sidebar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 300px !important;
    background: transparent;
    padding: 0;
}

.fb-right-sidebar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 300px !important;
    background: transparent;
    padding: 0;
}

.fb-main-feed {
    min-height: 300px !important;
    background: transparent;
    padding: 0;
}

/* Left Sidebar */
.fb-left-sidebar {
    grid-area: sidebar;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    height: fit-content;
    max-height: calc(100vh - var(--nk-header-height) - 48px);
    overflow-y: auto;
    position: sticky;
    top: calc(var(--nk-header-height) + 24px);
    box-shadow: var(--nk-glass-shadow);

    /* Custom scrollbar */
    scrollbar-width: thin;
    scrollbar-color: var(--nk-primary) transparent;
}

.fb-left-sidebar::-webkit-scrollbar {
    width: 6px;
}

.fb-left-sidebar::-webkit-scrollbar-track {
    background: var(--nk-glass-bg);
    border-radius: 3px;
}

.fb-left-sidebar::-webkit-scrollbar-thumb {
    background: var(--nk-primary);
    border-radius: 3px;
}

.fb-left-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--nk-primary-dark);
}

/* Profile Card */
.fb-profile-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--nk-bg-primary);
    border-radius: var(--nk-border-radius);
    margin-bottom: 20px;
    transition: all var(--nk-transition-normal);
}

.fb-profile-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-profile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-profile-info {
    flex: 1;
}

.fb-profile-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
}

.fb-profile-tagline {
    font-size: 12px;
    color: var(--nk-text-secondary);
    margin: 0;
}

/* Navigation Links */
.fb-nav-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.fb-nav-links li {
    margin-bottom: 8px;
}

.fb-nav-links a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    font-weight: 500;
}

.fb-nav-links a:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-nav-links i {
    font-size: 18px;
    width: 20px;
    text-align: center;
    color: var(--nk-text-secondary);
    transition: color var(--nk-transition-fast);
}

.fb-nav-links a:hover i {
    color: var(--nk-primary);
}

/* Footer Links */
.fb-footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-footer-links a {
    font-size: 12px;
    color: var(--nk-text-tertiary);
    transition: color var(--nk-transition-fast);
}

.fb-footer-links a:hover {
    color: var(--nk-primary);
}

/* Main Feed */
.fb-main-feed {
    grid-area: main;
    background: transparent;
    padding: 0;
    min-height: 100vh;
}

/* Stories Container */
.fb-stories-container {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-stories-wrapper {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: thin;
    scrollbar-color: var(--nk-primary) transparent;
}

.fb-stories-wrapper::-webkit-scrollbar {
    height: 6px;
}

.fb-stories-wrapper::-webkit-scrollbar-track {
    background: var(--nk-glass-bg);
    border-radius: 3px;
}

.fb-stories-wrapper::-webkit-scrollbar-thumb {
    background: var(--nk-primary);
    border-radius: 3px;
}

/* Story Cards */
.fb-story-card {
    position: relative;
    width: 120px;
    height: 200px;
    border-radius: var(--nk-border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--nk-transition-normal);
    flex-shrink: 0;
}

.fb-story-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.fb-story-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.fb-story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-story-card:hover .fb-story-image img {
    transform: scale(1.05);
}

.fb-story-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    z-index: 1;
}

.fb-story-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px 12px;
    z-index: 2;
    color: var(--nk-white);
}

.fb-story-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 8px;
    border: 2px solid var(--nk-white);
}

.fb-story-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-story-title {
    font-size: 12px;
    font-weight: 600;
    line-height: 1.3;
    display: block;
}

.fb-story-meta {
    font-size: 10px;
    opacity: 0.8;
    margin-top: 4px;
}

/* Create Story */
.fb-create-story {
    background: var(--nk-bg-primary);
    border: 2px dashed var(--nk-glass-border);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--nk-text-primary);
}

.fb-create-story:hover {
    border-color: var(--nk-primary);
    background: var(--nk-glass-bg);
}

.fb-story-icon {
    width: 40px;
    height: 40px;
    background: var(--nk-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nk-white);
    font-size: 18px;
    margin-bottom: 8px;
}

.fb-story-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--nk-text-primary);
}

/* Story Scroll Buttons */
.fb-scroll-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nk-text-primary);
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    z-index: 10;
    box-shadow: var(--nk-glass-shadow);
}

.fb-scroll-btn:hover {
    background: var(--nk-primary);
    color: var(--nk-white);
    transform: translateY(-50%) scale(1.1);
}

.fb-scroll-left {
    left: 10px;
}

.fb-scroll-right {
    right: 10px;
}

/* Story Link Overlay */
.fb-story-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 5;
}

/* Create Post Section */
.fb-create-post {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-create-post-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.fb-create-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-create-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-create-input {
    flex: 1;
}

.fb-create-input input {
    width: 100%;
    padding: 12px 20px;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    background: var(--nk-bg-primary);
    color: var(--nk-text-primary);
    font-size: 14px;
    transition: all var(--nk-transition-normal);
}

.fb-create-input input:focus {
    outline: none;
    border-color: var(--nk-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.fb-create-input input::placeholder {
    color: var(--nk-text-tertiary);
}

.fb-create-post-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: transparent;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    flex: 1;
    justify-content: center;
}

.fb-action-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    border-color: var(--nk-primary);
}

.fb-action-btn i {
    font-size: 16px;
}

/* News Posts */
.fb-post {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--nk-glass-shadow);
    transition: all var(--nk-transition-normal);
}

.fb-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
}

.fb-post-header {
    padding: 20px 20px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.fb-post-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-post-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-post-info {
    flex: 1;
}

.fb-post-author {
    font-size: 15px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
}

.fb-post-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--nk-text-secondary);
}

.fb-post-category {
    background: var(--nk-primary);
    color: var(--nk-white);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.fb-post-content {
    padding: 16px 20px;
}

.fb-post-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.fb-post-title a {
    color: inherit;
    transition: color var(--nk-transition-fast);
}

.fb-post-title a:hover {
    color: var(--nk-primary);
}

.fb-post-excerpt {
    font-size: 14px;
    color: var(--nk-text-secondary);
    line-height: 1.6;
    margin: 0 0 16px 0;
}

.fb-post-image {
    position: relative;
    margin: 0 -20px 16px;
    overflow: hidden;
}

.fb-post-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-post:hover .fb-post-image img {
    transform: scale(1.02);
}

.fb-post-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-post-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--nk-text-secondary);
}

.fb-post-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.fb-post-buttons {
    display: flex;
    gap: 8px;
}

.fb-post-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-post-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    border-color: var(--nk-primary);
}

.fb-post-btn.liked {
    color: var(--nk-danger);
    border-color: var(--nk-danger);
}

.fb-post-btn.shared {
    color: var(--nk-success);
    border-color: var(--nk-success);
}

/* Right Sidebar */
.fb-right-sidebar {
    grid-area: rightbar;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    height: fit-content;
    max-height: calc(100vh - var(--nk-header-height) - 48px);
    overflow-y: auto;
    position: sticky;
    top: calc(var(--nk-header-height) + 24px);
    box-shadow: var(--nk-glass-shadow);

    /* Custom scrollbar */
    scrollbar-width: thin;
    scrollbar-color: var(--nk-primary) transparent;
}

.fb-right-sidebar::-webkit-scrollbar {
    width: 6px;
}

.fb-right-sidebar::-webkit-scrollbar-track {
    background: var(--nk-glass-bg);
    border-radius: 3px;
}

.fb-right-sidebar::-webkit-scrollbar-thumb {
    background: var(--nk-primary);
    border-radius: 3px;
}

.fb-right-sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--nk-primary-dark);
}

/* Sponsored Ads */
.fb-sponsored-ad {
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 16px;
    margin-bottom: 20px;
    transition: all var(--nk-transition-normal);
}

.fb-sponsored-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-sponsored-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.fb-sponsored-label {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.fb-sponsored-menu {
    color: var(--nk-text-tertiary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--nk-transition-fast);
}

.fb-sponsored-menu:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-text-primary);
}

.fb-sponsored-content {
    text-align: center;
}

.fb-sponsored-image {
    width: 100%;
    height: 150px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    margin-bottom: 12px;
}

.fb-sponsored-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-sponsored-ad:hover .fb-sponsored-image img {
    transform: scale(1.05);
}

.fb-sponsored-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.fb-sponsored-description {
    font-size: 12px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0 0 12px 0;
}

.fb-sponsored-cta {
    display: inline-block;
    padding: 8px 16px;
    background: var(--nk-primary);
    color: var(--nk-white);
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--nk-transition-fast);
}

.fb-sponsored-cta:hover {
    background: var(--nk-primary-dark);
    transform: translateY(-1px);
}

/* Widget Sections */
.fb-widget {
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.fb-widget-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--nk-primary);
    position: relative;
}

.fb-widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--nk-secondary);
}

/* Trending News */
.fb-trending-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--nk-glass-border);
    transition: all var(--nk-transition-fast);
}

.fb-trending-item:last-child {
    border-bottom: none;
}

.fb-trending-item:hover {
    background: var(--nk-glass-bg);
    margin: 0 -16px;
    padding: 12px 16px;
    border-radius: var(--nk-border-radius-sm);
}

.fb-trending-image {
    width: 60px;
    height: 60px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-trending-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-trending-content {
    flex: 1;
}

.fb-trending-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--nk-text-primary);
    line-height: 1.4;
    margin: 0 0 6px 0;
}

.fb-trending-title a {
    color: inherit;
    transition: color var(--nk-transition-fast);
}

.fb-trending-title a:hover {
    color: var(--nk-primary);
}

.fb-trending-meta {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Load More Button */
.fb-load-more {
    display: flex;
    justify-content: center;
    margin: 32px 0;
}

.fb-load-more-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    color: var(--nk-text-primary);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-normal);
    box-shadow: var(--nk-glass-shadow);
}

.fb-load-more-btn:hover {
    background: var(--nk-primary);
    color: var(--nk-white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.fb-load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.fb-load-more-btn i {
    transition: transform var(--nk-transition-fast);
}

.fb-load-more-btn:hover i {
    transform: rotate(180deg);
}

/* ===================================
   RESPONSIVE LAYOUT DESIGN
   =================================== */

/* Large Desktop */
@media (min-width: 1400px) {
    .fb-main-container {
        grid-template-columns: 320px 1fr 360px;
        gap: 32px;
        padding: 32px;
        grid-template-areas: "sidebar main rightbar";
    }
}

/* Desktop */
@media (max-width: 1200px) and (min-width: 1025px) {
    .fb-main-container {
        grid-template-columns: 260px 1fr 300px;
        gap: 20px;
        padding: 20px;
        grid-template-areas: "sidebar main rightbar";
    }

    .fb-left-sidebar,
    .fb-right-sidebar {
        padding: 16px;
    }
}

/* Tablet */
@media (max-width: 1024px) and (min-width: 769px) {
    .fb-main-container {
        grid-template-columns: 1fr 280px;
        gap: 20px;
        grid-template-areas: "main rightbar";
    }

    .fb-left-sidebar {
        display: none;
    }

    .fb-stories-wrapper {
        gap: 12px;
    }

    .fb-story-card {
        width: 100px;
        height: 160px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .fb-main-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px;
        grid-template-areas: "main";
    }

    .fb-left-sidebar,
    .fb-right-sidebar {
        display: none;
    }

    .fb-stories-container {
        padding: 16px;
        margin-bottom: 16px;
    }

    .fb-story-card {
        width: 90px;
        height: 140px;
    }

    .fb-create-post {
        padding: 16px;
        margin-bottom: 16px;
    }

    .fb-post {
        margin-bottom: 16px;
    }

    .fb-post-header,
    .fb-post-content,
    .fb-post-actions {
        padding-left: 16px;
        padding-right: 16px;
    }

    .fb-post-image {
        margin-left: -16px;
        margin-right: -16px;
    }

    .fb-post-title {
        font-size: 16px;
    }

    .fb-post-actions {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .fb-post-buttons {
        justify-content: space-between;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .fb-main-container {
        padding: 12px;
        gap: 12px;
    }

    .fb-stories-container {
        padding: 12px;
    }

    .fb-story-card {
        width: 80px;
        height: 120px;
    }

    .fb-create-post,
    .fb-post {
        padding: 12px;
        margin-bottom: 12px;
    }

    .fb-post-header,
    .fb-post-content,
    .fb-post-actions {
        padding-left: 12px;
        padding-right: 12px;
    }

    .fb-post-image {
        margin-left: -12px;
        margin-right: -12px;
    }

    .fb-create-post-actions {
        flex-direction: column;
        gap: 8px;
    }

    .fb-action-btn {
        justify-content: flex-start;
    }
}

/* ===================================
   CATEGORY & DETAIL PAGES COMPATIBILITY
   =================================== */

/* Ensure v2 pages use modern layout */
.nk-main .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.nk-main .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -12px;
}

.nk-main .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding: 0 12px;
}

.nk-main .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 12px;
}

@media (max-width: 992px) {
    .nk-main .col-lg-8,
    .nk-main .col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Modern styling for category and detail pages */
.category-header,
.article-header {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.news-grid,
.news-card,
.article-content {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    box-shadow: var(--nk-glass-shadow);
}

/* Ensure consistent spacing */
.nk-main > * {
    margin-top: 24px;
}

.nk-main > *:first-child {
    margin-top: 0;
}

/* ===================================
   CATEGORY PAGE SPECIFIC STYLES
   =================================== */

/* Categories Section */
.fb-categories-section {
    margin-bottom: 24px;
}

.fb-section-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--nk-primary);
}

.fb-categories-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-category-group {
    position: relative;
}

.fb-category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    cursor: pointer;
    position: relative;
}

.fb-category-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-category-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-color: var(--nk-primary);
}

.fb-category-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fb-category-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.fb-category-name {
    font-size: 14px;
    font-weight: 600;
}

.fb-category-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-category-count {
    font-size: 12px;
    background: var(--nk-glass-bg);
    padding: 4px 8px;
    border-radius: 12px;
    color: var(--nk-text-secondary);
}

.fb-category-item.active .fb-category-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--nk-white);
}

.fb-category-toggle {
    background: none;
    border: none;
    color: var(--nk-text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--nk-transition-fast);
}

.fb-category-toggle:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.fb-category-item.active .fb-category-toggle {
    color: var(--nk-white);
}

.fb-category-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.fb-category-toggle {
    position: relative;
    z-index: 2;
}

/* Subcategories */
.fb-subcategories {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--nk-transition-normal);
    background: var(--nk-bg-secondary);
    border-radius: 0 0 var(--nk-border-radius-sm) var(--nk-border-radius-sm);
    margin-top: 4px;
}

.fb-subcategories.expanded {
    max-height: 500px;
    padding: 8px 0;
}

.fb-subcategory-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 24px;
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
    border-left: 3px solid transparent;
}

.fb-subcategory-item:hover {
    background: var(--nk-glass-bg);
    border-left-color: var(--nk-primary);
    color: var(--nk-primary);
}

.fb-subcategory-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.fb-subcategory-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
}

.fb-subcategory-count {
    font-size: 11px;
    background: var(--nk-glass-bg);
    padding: 2px 6px;
    border-radius: 8px;
    color: var(--nk-text-tertiary);
}

/* Navigation Menu */
.fb-nav-menu {
    margin-bottom: 24px;
}

.fb-nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--nk-text-primary);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    margin-bottom: 4px;
    font-weight: 500;
}

.fb-nav-item:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-nav-item.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
}

.fb-nav-item i {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Page Header */
.fb-page-header {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
}

.fb-page-description {
    font-size: 16px;
    color: var(--nk-text-secondary);
    margin: 0;
}

.fb-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
}

.fb-breadcrumb a {
    color: var(--nk-primary);
    transition: color var(--nk-transition-fast);
}

.fb-breadcrumb a:hover {
    color: var(--nk-primary-dark);
}

.fb-breadcrumb i {
    color: var(--nk-text-tertiary);
    font-size: 12px;
}

/* ===================================
   RIGHT SIDEBAR SPECIFIC STYLES
   =================================== */

/* Sponsored Section */
.fb-sponsored-section {
    margin-bottom: 24px;
}

.fb-sponsored-item {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    transition: all var(--nk-transition-normal);
    cursor: pointer;
}

.fb-sponsored-item:hover {
    background: var(--nk-glass-bg);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.fb-sponsored-image {
    width: 60px;
    height: 60px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-sponsored-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-sponsored-content {
    flex: 1;
}

.fb-sponsored-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-sponsored-text {
    font-size: 12px;
    color: var(--nk-text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* Trending Section */
.fb-trending-section {
    margin-bottom: 24px;
}

.fb-trending-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.fb-trending-item {
    display: flex;
    gap: 12px;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
}

.fb-trending-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
}

.fb-trending-rank {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
}

.fb-trending-content {
    flex: 1;
}

.fb-trending-title {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.fb-trending-title a {
    color: var(--nk-text-primary);
    transition: color var(--nk-transition-fast);
}

.fb-trending-title a:hover {
    color: var(--nk-primary);
}

.fb-trending-meta {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Contact Info Section */
.fb-contact-section {
    margin-bottom: 24px;
}

.fb-contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    margin-bottom: 8px;
    transition: all var(--nk-transition-fast);
}

.fb-contact-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
}

.fb-contact-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.fb-contact-info {
    flex: 1;
}

.fb-contact-label {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    margin: 0 0 2px 0;
}

.fb-contact-value {
    font-size: 13px;
    color: var(--nk-text-primary);
    font-weight: 500;
    margin: 0;
}

/* Related Categories */
.fb-related-section {
    margin-bottom: 24px;
}

.fb-related-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-related-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-primary);
    transition: all var(--nk-transition-fast);
}

.fb-related-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
    color: var(--nk-primary);
    transform: translateX(4px);
}

.fb-related-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.fb-related-info {
    flex: 1;
}

.fb-related-name {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 2px 0;
}

.fb-related-count {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    margin: 0;
}

/* ===================================
   CATEGORY PAGE HEADER & TABS
   =================================== */

/* Category Header */
.fb-category-header {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin-bottom: 24px;
    overflow: hidden;
    box-shadow: var(--nk-glass-shadow);
}

.fb-category-cover {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.fb-category-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
}

.fb-category-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px;
    color: var(--nk-white);
    z-index: 2;
}

.fb-category-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.fb-category-description {
    font-size: 16px;
    margin: 0 0 12px 0;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.fb-category-stats {
    display: flex;
    gap: 16px;
    font-size: 14px;
    font-weight: 600;
}

.fb-category-posts,
.fb-category-followers {
    display: flex;
    align-items: center;
    gap: 4px;
}

.fb-category-posts::before {
    content: "📰";
}

.fb-category-followers::before {
    content: "👥";
}

/* Category Actions */
.fb-category-actions {
    display: flex;
    gap: 12px;
    padding: 16px 24px;
    background: var(--nk-bg-primary);
    border-top: 1px solid var(--nk-glass-border);
}

.fb-follow-btn,
.fb-share-btn,
.fb-more-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    background: var(--nk-bg-primary);
    color: var(--nk-text-primary);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-follow-btn:hover,
.fb-share-btn:hover,
.fb-more-btn:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
    color: var(--nk-primary);
}

.fb-follow-btn {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-color: var(--nk-primary);
}

.fb-follow-btn:hover {
    background: linear-gradient(135deg, var(--nk-primary-dark), var(--nk-secondary));
    color: var(--nk-white);
}

/* Filter Tabs */
.fb-filter-tabs {
    display: flex;
    gap: 8px;
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 8px;
    margin-bottom: 24px;
    box-shadow: var(--nk-glass-shadow);
}

.fb-filter-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    flex: 1;
    justify-content: center;
}

.fb-filter-tab:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.fb-filter-tab.active {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.fb-filter-tab i {
    font-size: 16px;
}

/* Filter Content */
.fb-filter-content {
    display: none;
}

.fb-filter-content.active {
    display: block;
}

/* News Grid for Category Pages */
.fb-category-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.fb-category-news-item {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    overflow: hidden;
    transition: all var(--nk-transition-normal);
    box-shadow: var(--nk-glass-shadow);
}

.fb-category-news-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.fb-category-news-image,
.fb-post-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
    position: relative;
    background: var(--nk-glass-bg);
    border-radius: var(--nk-border-radius-sm);
}

.fb-category-news-image img,
.fb-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
    display: block;
}

/* Image loading states and fallbacks */
.fb-post-image img[src=""],
.fb-post-image img:not([src]),
.fb-category-news-image img[src=""],
.fb-category-news-image img:not([src]) {
    display: none;
}

.fb-post-image::before,
.fb-category-news-image::before {
    content: '📰';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--nk-glass-bg), var(--nk-bg-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nk-text-tertiary);
    font-size: 48px;
    z-index: 1;
}

.fb-post-image:has(img),
.fb-category-news-image:has(img) {
    background: transparent;
}

.fb-post-image:has(img)::before,
.fb-category-news-image:has(img)::before {
    display: none;
}

/* Image error handling */
.fb-post-image img,
.fb-category-news-image img,
.fb-story-image img {
    background: linear-gradient(135deg, var(--nk-glass-bg), var(--nk-bg-secondary));
}

/* Category cover fallback */
.fb-category-cover {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-primary-dark));
    min-height: 200px;
    position: relative;
}

.fb-category-cover img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

/* Image overlay for links */
.fb-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--nk-white);
    font-size: 24px;
    opacity: 0;
    transition: opacity var(--nk-transition-normal);
    z-index: 2;
}

.fb-post-image:hover .fb-image-overlay {
    opacity: 1;
}

.fb-category-news-item:hover .fb-category-news-image img {
    transform: scale(1.05);
}

.fb-category-news-content {
    padding: 16px;
}

.fb-category-news-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.fb-category-news-title a {
    color: inherit;
    transition: color var(--nk-transition-fast);
}

.fb-category-news-title a:hover {
    color: var(--nk-primary);
}

.fb-category-news-excerpt {
    font-size: 13px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0 0 12px 0;
}

.fb-category-news-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: var(--nk-text-tertiary);
}

.fb-category-news-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.fb-category-news-stats {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fb-category-news-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* ===================================
   ADVERTISEMENT STYLING
   =================================== */

/* Sponsored Ads */
.sponsored-ad {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin: 24px 0;
    overflow: hidden;
    box-shadow: var(--nk-glass-shadow);
    transition: all var(--nk-transition-normal);
}

.sponsored-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.sponsored-ad-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--nk-bg-primary);
    border-bottom: 1px solid var(--nk-glass-border);
}

.sponsored-ad-label {
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sponsored-ad-menu {
    color: var(--nk-text-tertiary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--nk-transition-fast);
}

.sponsored-ad-menu:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-text-primary);
}

.sponsored-ad-content {
    padding: 16px;
}

.sponsored-ad-image {
    width: 100%;
    height: 200px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    margin-bottom: 12px;
}

.sponsored-ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.sponsored-ad:hover .sponsored-ad-image img {
    transform: scale(1.05);
}

.sponsored-ad-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.sponsored-ad-description {
    font-size: 14px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0 0 16px 0;
}

.sponsored-ad-cta {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: var(--nk-border-radius-sm);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--nk-transition-fast);
}

.sponsored-ad-cta:hover {
    background: linear-gradient(135deg, var(--nk-primary-dark), var(--nk-secondary));
    transform: translateY(-1px);
    color: var(--nk-white);
}

/* Regular Advertisements */
.advertisement,
.fb-advertisement,
.fb-advertisement-container {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin: 24px 0;
    overflow: hidden;
    box-shadow: var(--nk-glass-shadow);
    transition: all var(--nk-transition-normal);
}

/* Advertisement Header */
.fb-ad-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    background: var(--nk-bg-primary);
    border-bottom: 1px solid var(--nk-glass-border);
}

.fb-ad-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: var(--nk-text-tertiary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.fb-ad-actions {
    display: flex;
    gap: 4px;
}

.fb-ad-action {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--nk-text-tertiary);
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.fb-ad-action:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-text-primary);
}

/* Advertisement Content */
.fb-ad-content {
    padding: 16px;
}

/* Banner Ads */
.fb-ad-banner {
    display: flex;
    gap: 16px;
    align-items: center;
}

.fb-ad-banner .fb-ad-image {
    width: 80px;
    height: 80px;
    border-radius: var(--nk-border-radius-sm);
    object-fit: cover;
    flex-shrink: 0;
}

.fb-ad-text {
    flex: 1;
}

.fb-ad-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.fb-ad-description {
    font-size: 14px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0 0 12px 0;
}

.fb-ad-link,
.fb-ad-cta,
.fb-ad-button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: var(--nk-border-radius-sm);
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--nk-transition-fast);
}

.fb-ad-link:hover,
.fb-ad-cta:hover,
.fb-ad-button:hover {
    background: linear-gradient(135deg, var(--nk-primary-dark), var(--nk-secondary));
    transform: translateY(-1px);
    color: var(--nk-white);
}

/* Sidebar Ads */
.fb-ad-sidebar {
    text-align: center;
}

.fb-ad-image-container {
    width: 100%;
    height: 120px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    margin-bottom: 12px;
}

.fb-ad-sidebar .fb-ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.fb-advertisement:hover .fb-ad-sidebar .fb-ad-image {
    transform: scale(1.05);
}

/* Inline Ads */
.fb-ad-inline {
    background: linear-gradient(135deg, var(--nk-glass-bg), var(--nk-bg-primary));
    border-radius: var(--nk-border-radius);
    padding: 20px;
}

.fb-ad-inline-content {
    display: flex;
    gap: 16px;
    align-items: center;
}

.fb-ad-image-wrapper {
    width: 100px;
    height: 100px;
    border-radius: var(--nk-border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.fb-ad-inline .fb-ad-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-ad-text-wrapper {
    flex: 1;
}

/* Popup Ads */
.fb-ad-popup-trigger {
    cursor: pointer;
    transition: all var(--nk-transition-normal);
}

.fb-ad-popup-trigger:hover {
    transform: translateY(-2px);
}

.fb-ad-popup-preview {
    display: flex;
    gap: 12px;
    align-items: center;
}

.fb-ad-preview-image {
    width: 60px;
    height: 60px;
    border-radius: var(--nk-border-radius-sm);
    object-fit: cover;
}

.fb-ad-preview-text {
    flex: 1;
}

.fb-ad-popup-btn {
    padding: 6px 12px;
    background: var(--nk-primary);
    color: var(--nk-white);
    border: none;
    border-radius: var(--nk-border-radius-sm);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-ad-popup-btn:hover {
    background: var(--nk-primary-dark);
}

/* Popup Modal */
.fb-ad-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fb-ad-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.fb-ad-modal-content {
    position: relative;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--nk-glass-shadow);
}

.fb-ad-modal-close {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border: none;
    background: var(--nk-glass-bg);
    color: var(--nk-text-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    z-index: 1;
}

.fb-ad-modal-close:hover {
    background: var(--nk-danger);
    color: var(--nk-white);
}

.fb-ad-modal-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.fb-ad-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-ad-modal-text {
    padding: 20px;
}

.fb-ad-modal-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--nk-text-primary);
    margin: 0 0 12px 0;
}

.fb-ad-modal-description {
    font-size: 16px;
    color: var(--nk-text-secondary);
    line-height: 1.6;
    margin: 0 0 20px 0;
}

.fb-ad-modal-cta {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-radius: var(--nk-border-radius);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    transition: all var(--nk-transition-fast);
}

.fb-ad-modal-cta:hover {
    background: linear-gradient(135deg, var(--nk-primary-dark), var(--nk-secondary));
    transform: translateY(-1px);
    color: var(--nk-white);
}

.advertisement:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.advertisement-content {
    padding: 16px;
    text-align: center;
}

.advertisement-image {
    width: 100%;
    height: 150px;
    border-radius: var(--nk-border-radius-sm);
    overflow: hidden;
    margin-bottom: 12px;
}

.advertisement-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--nk-transition-slow);
}

.advertisement:hover .advertisement-image img {
    transform: scale(1.05);
}

.advertisement-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.advertisement-description {
    font-size: 12px;
    color: var(--nk-text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* Inline Advertisements */
.advertisement.inline {
    margin: 32px 0;
    background: linear-gradient(135deg, var(--nk-glass-bg), var(--nk-bg-primary));
}

.advertisement.inline .advertisement-content {
    padding: 24px;
}

/* Banner Advertisements */
.advertisement.banner {
    margin: 16px 0;
    border-radius: var(--nk-border-radius-lg);
}

.advertisement.banner .advertisement-image {
    height: 120px;
    margin-bottom: 0;
    border-radius: var(--nk-border-radius-lg);
}

/* Sidebar Advertisements */
.advertisement.sidebar {
    margin: 16px 0;
}

.advertisement.sidebar .advertisement-image {
    height: 120px;
}

/* Contact Items */
.fb-contacts-section {
    margin-bottom: 24px;
}

.fb-contacts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fb-contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    transition: all var(--nk-transition-fast);
    cursor: pointer;
}

.fb-contact-item:hover {
    background: var(--nk-glass-bg);
    border-color: var(--nk-primary);
}

.fb-contact-avatar {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.fb-contact-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fb-online-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: var(--nk-success);
    border: 2px solid var(--nk-white);
    border-radius: 50%;
}

.fb-contact-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--nk-text-primary);
}

/* ===================================
   COMMENTS SECTION STYLING
   =================================== */

.fb-comments-section {
    background: var(--nk-glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    margin: 24px 0;
    padding: 20px;
    box-shadow: var(--nk-glass-shadow);
}

/* Write Comment */
.fb-write-comment {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--nk-glass-border);
}

.fb-comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    padding: 2px;
}

.fb-comment-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--nk-white);
}

.fb-comment-input-wrapper {
    flex: 1;
}

.fb-comment-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    background: var(--nk-bg-primary);
    color: var(--nk-text-primary);
    font-size: 14px;
    transition: all var(--nk-transition-normal);
    resize: none;
    min-height: 44px;
}

.fb-comment-input:focus {
    outline: none;
    border-color: var(--nk-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.fb-comment-input::placeholder {
    color: var(--nk-text-tertiary);
}

.fb-comment-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.fb-comment-emoji,
.fb-comment-photo,
.fb-comment-submit {
    padding: 6px 12px;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    background: var(--nk-bg-primary);
    color: var(--nk-text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-comment-emoji:hover,
.fb-comment-photo:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.fb-comment-submit {
    background: linear-gradient(135deg, var(--nk-primary), var(--nk-secondary));
    color: var(--nk-white);
    border-color: var(--nk-primary);
    font-weight: 600;
}

.fb-comment-submit:hover {
    background: linear-gradient(135deg, var(--nk-primary-dark), var(--nk-secondary));
}

/* Comments List */
.fb-comments-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.fb-comment {
    display: flex;
    gap: 12px;
}

.fb-comment-content {
    flex: 1;
}

.fb-comment-bubble {
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius);
    padding: 12px 16px;
    margin-bottom: 8px;
}

.fb-comment-author {
    font-size: 13px;
    font-weight: 600;
    color: var(--nk-text-primary);
    margin: 0 0 4px 0;
}

.fb-comment-text {
    font-size: 14px;
    color: var(--nk-text-primary);
    line-height: 1.4;
    margin: 0;
}

.fb-comment-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: var(--nk-text-tertiary);
}

.fb-comment-like,
.fb-comment-reply {
    background: none;
    border: none;
    color: var(--nk-text-tertiary);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: color var(--nk-transition-fast);
}

.fb-comment-like:hover,
.fb-comment-reply:hover {
    color: var(--nk-primary);
}

.fb-comment-time {
    color: var(--nk-text-tertiary);
}

.fb-comment-likes {
    color: var(--nk-text-secondary);
}

/* Comment Replies */
.fb-comment-replies {
    margin-top: 12px;
    margin-left: 20px;
    padding-left: 16px;
    border-left: 2px solid var(--nk-glass-border);
}

.fb-comment-reply {
    margin-bottom: 12px;
}

.fb-comment-reply .fb-comment-avatar {
    width: 32px;
    height: 32px;
}

/* No Comments */
.fb-no-comments {
    text-align: center;
    padding: 40px 20px;
    color: var(--nk-text-tertiary);
}

.fb-no-comments p {
    font-size: 16px;
    margin: 0;
}

/* Load More Comments */
.fb-load-more-comments {
    text-align: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--nk-glass-border);
}

.fb-load-more-comments .fb-load-more-btn {
    background: var(--nk-glass-bg);
    border: 1px solid var(--nk-glass-border);
    color: var(--nk-text-primary);
    padding: 10px 20px;
    border-radius: var(--nk-border-radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-load-more-comments .fb-load-more-btn:hover {
    background: var(--nk-primary);
    color: var(--nk-white);
    border-color: var(--nk-primary);
}

/* ===================================
   REACTION BUTTONS & INTERACTION STYLING
   =================================== */

/* Post Reactions Section */
.fb-post-reactions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    border-bottom: 1px solid var(--nk-glass-border);
}

.fb-reactions-summary {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fb-reactions-icons {
    display: flex;
    align-items: center;
    gap: 2px;
}

.fb-reaction {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    position: relative;
    z-index: 1;
}

.fb-reaction:hover {
    transform: scale(1.2);
    z-index: 2;
}

.fb-reaction.like {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
}

.fb-reaction.love {
    background: linear-gradient(135deg, #e91e63, #f06292);
}

.fb-reaction.wow {
    background: linear-gradient(135deg, #ffc107, #ffeb3b);
}

.fb-reaction.angry {
    background: linear-gradient(135deg, #f44336, #ef5350);
}

.fb-reaction.sad {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
}

.fb-reaction-count {
    font-size: 14px;
    color: var(--nk-text-secondary);
    font-weight: 500;
}

/* Post Engagement Stats */
.fb-post-engagement {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: var(--nk-text-secondary);
}

.fb-comments-count,
.fb-shares-count,
.fb-views-count {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: color var(--nk-transition-fast);
}

.fb-comments-count:hover,
.fb-shares-count:hover {
    color: var(--nk-primary);
}

/* Action Buttons */
.fb-post-actions {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 8px 20px;
    border-bottom: 1px solid var(--nk-glass-border);
}

.fb-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 16px;
    background: transparent;
    border: none;
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
    flex: 1;
    position: relative;
    overflow: hidden;
}

.fb-action-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
}

.fb-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--nk-transition-normal);
}

.fb-action-btn:hover::before {
    left: 100%;
}

.fb-action-btn i {
    font-size: 16px;
    transition: all var(--nk-transition-fast);
}

.fb-action-btn:hover i {
    transform: scale(1.1);
}

/* Specific Action Button States */
.fb-like-btn.liked {
    color: #1877f2;
}

.fb-like-btn.liked i {
    color: #1877f2;
}

.fb-comment-btn:hover {
    color: #42a5f5;
}

.fb-share-btn:hover {
    color: #4caf50;
}

.fb-share-btn.shared {
    color: #4caf50;
}

/* Reaction Picker */
.fb-reaction-picker {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--nk-bg-primary);
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-lg);
    padding: 8px 12px;
    display: flex;
    gap: 4px;
    box-shadow: var(--nk-glass-shadow);
    opacity: 0;
    visibility: hidden;
    transition: all var(--nk-transition-normal);
    z-index: 10;
}

.fb-like-btn:hover .fb-reaction-picker {
    opacity: 1;
    visibility: visible;
}

.fb-reaction-option {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-reaction-option:hover {
    transform: scale(1.3);
}

/* Interaction Counts */
.fb-post-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: var(--nk-text-tertiary);
}

.fb-post-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
    transition: color var(--nk-transition-fast);
}

.fb-post-stats span:hover {
    color: var(--nk-primary);
}

.fb-post-stats i {
    font-size: 12px;
}

/* Post Buttons */
.fb-post-buttons {
    display: flex;
    gap: 8px;
}

.fb-post-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: transparent;
    border: 1px solid var(--nk-glass-border);
    border-radius: var(--nk-border-radius-sm);
    color: var(--nk-text-secondary);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--nk-transition-fast);
}

.fb-post-btn:hover {
    background: var(--nk-glass-bg);
    color: var(--nk-primary);
    border-color: var(--nk-primary);
}

.fb-post-btn.liked {
    color: #1877f2;
    border-color: #1877f2;
    background: rgba(24, 119, 242, 0.1);
}

.fb-post-btn.shared {
    color: #4caf50;
    border-color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
}

.fb-post-btn i {
    font-size: 14px;
}

/* Count Animations */
@keyframes countUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

.count-updated {
    animation: countUp 0.3s ease-out;
}
