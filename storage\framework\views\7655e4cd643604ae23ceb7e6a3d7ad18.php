
<?php $__env->startSection('admin'); ?>

<!-- Quill Editor CSS -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

<!-- jQuery (already loaded in admin dashboard, but keeping for compatibility) -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- Quill Editor JS -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">
                        
                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            
                                            <li class="breadcrumb-item active">Add News Post</li>
                                        </ol>
                                    </div>
                                    <h4 class="page-title">Add News Post</h4>
                                </div>
                            </div>
                        </div>     
                        <!-- end page title --> 
  
                        <!-- Form row -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                         
    <form id="myForm" method="post" action="<?php echo e(route('store.news.post')); ?>" enctype="multipart/form-data">
    	<?php echo csrf_field(); ?> 
    	
        <div class="row">
            <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label">Category Name </label>
               <select name="category_id" class="form-select" id="example-select">
                <option>Select Category </option>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($category->id); ?>"><?php echo e($category->category_name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            </div>

             <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label"> Sub Category </label>
               <select name="subcategory_id" class="form-select" id="example-select">
                <option> </option>
                
            </select>
            </div>


             <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label">Writer  </label>
                <select name="user_id" class="form-select" id="example-select">
                <option>Select Writer </option>
                <?php $__currentLoopData = $adminuser; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            </div>


             <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label">News Title  </label>
                <input type="text" name="news_title" class="form-control" id="inputEmail4" >
            </div>

             <div class="form-group col-md-6 mb-3">
          <label for="example-fileinput" class="form-label">News Post Photo</label>
        <input type="file" name="image" id="image" class="form-control">
            </div>

             <div class="form-group col-md-6 mb-3">
                <label for="example-fileinput" class="form-label"> </label>
        <img id="showImage" src="<?php echo e(url('upload/no_image.jpg')); ?> " class="rounded-circle avatar-lg img-thumbnail" alt="profile-image">
            </div>



 <div class="col-12 mb-3">
     <label for="inputEmail4" class="form-label">
         <i class="fas fa-edit"></i> News Details (WYSIWYG Editor)
         <small class="text-muted">- Use the toolbar to format your content</small>
     </label>
     <div class="wysiwyg-container">
         <div id="wysiwyg_editor" style="height: 400px;"></div>
         <textarea name="news_details" id="news_details_hidden" style="display: none;"></textarea>
         <div class="wysiwyg-help mt-2">
             <small class="text-muted">
                 <i class="fas fa-info-circle"></i>
                 Tips: Use <strong>Ctrl+B</strong> for bold, <strong>Ctrl+I</strong> for italic,
                 click the image icon to upload pictures
             </small>
         </div>
     </div>
 </div>


 <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label">Tags  </label>
                <input type="text" name="tags" class="selectize-close-btn" value="awesome">
            </div>



    <div class="row">
        <div class="col-lg-6">
            <div class="form-check mb-2 form-check-primary">
                <input class="form-check-input" type="checkbox" name="breaking_news" value="1" id="customckeck1"  >
                <label class="form-check-label" for="customckeck1">Breaking News</label>
            </div>

            <div class="form-check mb-2 form-check-primary">
                <input class="form-check-input" type="checkbox" name="top_slider" value="1" id="customckeck2"  >
                <label class="form-check-label" for="customckeck2">Top Slider</label>
            </div>
            
        </div>




             <div class="col-lg-6">
           <div class="form-check mb-2 form-check-danger">
            <input class="form-check-input" name="first_section_three" type="checkbox" value="1" id="customckeck3"  >
            <label class="form-check-label" for="customckeck3">First Section Three</label>
        </div>

            <div class="form-check mb-2 form-check-danger">
            <input class="form-check-input"  name="first_section_nine" type="checkbox" value="1" id="customckeck4" >
            <label class="form-check-label" for="customckeck4">First Section Nine</label>
        </div>
            
        </div>
        
    </div>

 

             
        </div>
 
                                          

   <button type="submit" class="btn btn-primary waves-effect waves-light">Save Changes</button>

                                        </form>

                                    </div> <!-- end card-body -->
                                </div> <!-- end card-->
                            </div> <!-- end col -->
                        </div>
                        <!-- end row -->

 
                        
                    </div> <!-- container -->

                </div> <!-- content -->

<script type="text/javascript">
    $(document).ready(function (){
        $('#myForm').validate({
            rules: {
                news_title: {
                    required : true,
                }, 
            },
            messages :{
                news_title: {
                    required : 'Please Enter News Title',
                }, 
            },
            errorElement : 'span', 
            errorPlacement: function (error,element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight : function(element, errorClass, validClass){
                $(element).addClass('is-invalid');
            },
            unhighlight : function(element, errorClass, validClass){
                $(element).removeClass('is-invalid');
            },
        });
    });
    
</script>



<script type="text/javascript">
    $(document).ready(function(){
        $('#image').change(function(e){
            var reader = new FileReader();
            reader.onload = function(e){
                $('#showImage').attr('src',e.target.result);
            }
            reader.readAsDataURL(e.target.files['0']);
        });
    });
</script>

 
    <script type="text/javascript">

        $(document).ready(function(){
            $('select[name="category_id"]').on('change', function(){
                var category_id = $(this).val();
                if (category_id) {
                    $.ajax({
                        url: "<?php echo e(url('/subcategory/ajax')); ?>/"+category_id,
                        type: "GET",  
                        dataType: "json", 
                        success:function(data){
                            $('select[name="subcategory_id"]').html('');
                            var d =$('select[name="subcategory_id"]').empty();
                            $.each(data, function(key, value){
                                $('select[name="subcategory_id"]').append('<option value="'+ value.id +'"> ' + value.subcategory_name + '</option>');
                            });
                        },
                    });
                } else{
                    alert('danger');
                }
            });
        });
        


    </script>

<script>
    $(document).ready(function() {
        // Initialize Quill WYSIWYG Editor with enhanced features
        var quill = new Quill('#wysiwyg_editor', {
            theme: 'snow',
            modules: {
                toolbar: {
                    container: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        [{ 'font': [] }],
                        [{ 'size': ['small', false, 'large', 'huge'] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'script': 'sub'}, { 'script': 'super' }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                        [{ 'direction': 'rtl' }],
                        [{ 'align': [] }],
                        ['blockquote', 'code-block'],
                        ['link', 'image', 'video'],
                        ['clean']
                    ],
                    handlers: {
                        'image': function() {
                            selectLocalImage();
                        }
                    }
                },
                history: {
                    delay: 2000,
                    maxStack: 500,
                    userOnly: true
                }
            },
            placeholder: '✍️ Start writing your news content here... Use the toolbar above to format your text, add images, and create engaging content for your readers.',
        });

        // Add word count feature
        var wordCountDiv = $('<div class="word-count text-muted mt-2"><small><i class="fas fa-calculator"></i> Words: <span id="word-count">0</span> | Characters: <span id="char-count">0</span></small></div>');
        $('.wysiwyg-container').append(wordCountDiv);

        // Update word count
        function updateWordCount() {
            var text = quill.getText();
            var words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
            var chars = text.length;
            $('#word-count').text(words);
            $('#char-count').text(chars);
        }

        // Custom image upload handler
        function selectLocalImage() {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();

            input.onchange = () => {
                const file = input.files[0];
                if (file) {
                    uploadImageToServer(file);
                }
            };
        }

        // Upload image to server
        function uploadImageToServer(file) {
            const formData = new FormData();
            formData.append('upload', file);
            formData.append('_token', '<?php echo e(csrf_token()); ?>');

            $.ajax({
                url: '<?php echo e(route("ckeditor.upload")); ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Insert image into editor
                    const range = quill.getSelection();
                    quill.insertEmbed(range.index, 'image', response.url);
                },
                error: function(xhr, status, error) {
                    console.error('Image upload failed:', error);
                    alert('Image upload failed. Please try again.');
                }
            });
        }

        // Sync editor content with hidden textarea before form submission
        $('form').on('submit', function() {
            const content = quill.root.innerHTML;
            $('#news_details_hidden').val(content);
        });

        // Update hidden field when content changes
        quill.on('text-change', function() {
            const content = quill.root.innerHTML;
            $('#news_details_hidden').val(content);
            updateWordCount();
        });

        // Initial word count
        updateWordCount();

        // Add auto-save feature (optional)
        var autoSaveTimer;
        quill.on('text-change', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(function() {
                // Auto-save to localStorage
                const content = quill.root.innerHTML;
                localStorage.setItem('news_draft_' + Date.now(), content);
                console.log('Content auto-saved to local storage');
            }, 5000); // Auto-save after 5 seconds of inactivity
        });
    });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/news/add_news_post.blade.php ENDPATH**/ ?>