<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($news->news_title); ?> | NitiKotha - Modern News Portal</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Footer CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/modern-footer.css')); ?>">
    <!-- Header CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/modern-header-fixed.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/modern-base.css')); ?>">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.7;
            color: #2d3748;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            font-size: 16px;
        }

        /* .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        } */

        .container {
            width: 100%;
            max-width: var(--container-xl);
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        /* Aggressive Smart Layout */
        /* .smart-article-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
            margin-top: 2rem;
            margin-bottom: 3rem;
        } */


        /* Aggressive Smart Layout */
        .smart-article-container {
            width: 100%;
            max-width: var(--container-xl);
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        .smart-grid-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .main-article-section {
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            position: relative;
        }

        .sidebar-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Smart Sidebar Widgets */
        .smart-widget {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .smart-widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
        }

        .smart-widget-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .smart-widget-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .smart-widget-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 2;
        }

        .smart-widget-content {
            padding: 1.5rem;
        }

        /* Trending News Widget */
        .trending-news-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .trending-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .trending-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .trending-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .trending-item:hover .trending-image {
            transform: scale(1.05);
        }

        .trending-content {
            padding: 1rem;
        }

        .trending-title {
            font-size: 0.875rem;
            font-weight: 600;
            line-height: 1.4;
            margin: 0 0 0.5rem 0;
        }

        .trending-title a {
            color: #1f2937;
            text-decoration: none;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .trending-title a:hover {
            color: #667eea;
        }

        .trending-meta {
            font-size: 0.75rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .trending-meta i {
            color: #667eea;
        }

        /* Newsletter Widget */
        .newsletter-widget {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .newsletter-widget::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .newsletter-content {
            position: relative;
            z-index: 2;
            padding: 2rem;
        }

        .newsletter-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .newsletter-subtitle {
            opacity: 0.9;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .newsletter-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .newsletter-input {
            padding: 1rem;
            border: none;
            border-radius: 50px;
            font-size: 0.9rem;
            outline: none;
            background: rgba(255, 255, 255, 0.9);
            color: #1f2937;
        }

        .newsletter-input::placeholder {
            color: #6b7280;
        }

        .newsletter-btn {
            padding: 1rem 2rem;
            background: white;
            color: #667eea;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .newsletter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* Categories Widget */
        .categories-smart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .category-smart-item {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            text-decoration: none;
            color: #1f2937;
        }

        .category-smart-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .category-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .category-name {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .category-count {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Social Follow Widget */
        .social-follow-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .social-follow-item {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
            text-decoration: none;
            color: #1f2937;
        }

        .social-follow-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .social-follow-item.facebook:hover { background: #1877f2; color: white; }
        .social-follow-item.twitter:hover { background: #1da1f2; color: white; }
        .social-follow-item.youtube:hover { background: #ff0000; color: white; }
        .social-follow-item.instagram:hover { background: #e4405f; color: white; }

        .social-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .social-platform {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .social-followers {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Header Top Content Fine-tuning */
        .header-top-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .header-date {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .header-date i {
            color: #fbbf24;
            font-size: 1rem;
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .weather-info i {
            color: #60a5fa;
            font-size: 1rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;
        }

        .language-selector select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .language-selector select:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .language-selector select option {
            background: #1f2937;
            color: white;
        }

        .header-social {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .social-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: white;
        }

        .social-link.facebook:hover {
            background: #1877f2;
            border-color: #1877f2;
        }

        .social-link.twitter:hover {
            background: #1da1f2;
            border-color: #1da1f2;
        }

        .social-link.youtube:hover {
            background: #ff0000;
            border-color: #ff0000;
        }

        .social-link.instagram:hover {
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            border-color: #e6683c;
        }

        /* News Details Hero Section */
        .news-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 0;
        }

        .news-breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .breadcrumb-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .breadcrumb-link:hover {
            color: white;
        }

        .breadcrumb-current {
            color: white;
            font-weight: 600;
        }

        .news-hero-title {
            font-size: 2.5rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1rem;
            color: white;
        }

        .news-hero-meta {
            display: flex;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        .hero-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .hero-meta-item i {
            color: #fbbf24;
        }

        /* Main Content Area */
        .main-content {
            background: #ffffff;
            padding: 3rem 0;
        }

        .content-wrapper {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 1rem;
            display: block;
        }

        /* Hide sidebar for single column layout */
        .sidebar {
            display: none !important;
        }

        /* Modern Blog Article Styles */
        .blog-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 0;
            position: relative;
            overflow: hidden;
        }

        .blog-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .blog-breadcrumb {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
            justify-content: center;
        }

        /* Modern Article Header */
        .modern-article-header {
            padding: 3rem 3rem 2rem 3rem;
            text-align: center;
            background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
        }

        .modern-category-badge {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .modern-category-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .modern-article-title {
            font-size: 3rem;
            font-weight: 800;
            line-height: 1.2;
            color: #1a202c;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modern-article-meta {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .modern-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #4a5568;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .modern-meta-item i {
            color: #667eea;
            font-size: 1rem;
        }

        /* Modern Featured Image */
        .modern-featured-image {
            position: relative;
            width: 100%;
            height: 500px;
            overflow: hidden;
            border-radius: 0;
            margin-bottom: 0;
        }

        .modern-featured-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .modern-featured-image:hover img {
            transform: scale(1.02);
        }

        .modern-image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
            padding: 2rem;
            color: white;
        }

        /* Modern Article Content */
        .modern-article-content {
            padding: 3rem;
            background: white;
        }

        .modern-social-share {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
        }

        .modern-share-label {
            font-weight: 600;
            color: #4a5568;
            margin-right: 1rem;
        }

        .modern-social-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modern-social-btn.facebook { background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%); }
        .modern-social-btn.twitter { background: linear-gradient(135deg, #1da1f2 0%, #42a5f5 100%); }
        .modern-social-btn.linkedin { background: linear-gradient(135deg, #0077b5 0%, #00a0dc 100%); }
        .modern-social-btn.whatsapp { background: linear-gradient(135deg, #25d366 0%, #4caf50 100%); }

        .modern-social-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* Modern Font Controls */
        .modern-font-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .modern-font-label {
            font-weight: 600;
            color: #4a5568;
        }

        .modern-font-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 700;
            color: #4a5568;
        }

        .modern-font-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: scale(1.1);
        }

        /* Modern Article Text */
        .modern-article-text {
            font-size: 1.125rem;
            line-height: 1.8;
            color: #2d3748;
            margin-bottom: 3rem;
        }

        .modern-article-text p {
            margin-bottom: 1.5rem;
        }

        .modern-article-text h2,
        .modern-article-text h3 {
            color: #1a202c;
            margin: 2.5rem 0 1.5rem 0;
            font-weight: 700;
        }

        .modern-article-text h2 {
            font-size: 1.75rem;
            border-left: 4px solid #667eea;
            padding-left: 1rem;
        }

        .modern-article-text h3 {
            font-size: 1.5rem;
        }

        .modern-article-text blockquote {
            background: #f8fafc;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 10px 10px 0;
            font-style: italic;
            color: #4a5568;
        }

        /* Modern Author Section */
        .modern-author-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 2.5rem;
            border-radius: 20px;
            margin: 3rem 0;
            border: 1px solid #e2e8f0;
        }

        .modern-author-card {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .modern-author-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .modern-author-info h4 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1a202c;
            margin: 0 0 0.5rem 0;
        }

        .modern-author-role {
            font-size: 0.9rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .modern-author-bio {
            font-size: 0.95rem;
            color: #4a5568;
            line-height: 1.6;
        }

        /* Modern Related News */
        .modern-related-news {
            margin: 3rem 0;
            padding: 2.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }

        .modern-related-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }

        .modern-related-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .modern-related-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .modern-related-item {
            display: flex;
            gap: 1rem;
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .modern-related-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .modern-related-image {
            width: 120px;
            height: 90px;
            border-radius: 10px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .modern-related-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .modern-related-item:hover .modern-related-image img {
            transform: scale(1.1);
        }

        .modern-related-content {
            flex: 1;
        }

        .modern-related-item-title {
            margin: 0 0 0.75rem 0;
        }

        .modern-related-item-title a {
            color: #1a202c;
            text-decoration: none;
            font-weight: 700;
            font-size: 1rem;
            line-height: 1.4;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modern-related-item-title a:hover {
            color: #667eea;
        }

        .modern-related-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: #6b7280;
        }

        .modern-related-date,
        .modern-related-views {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .modern-related-date i,
        .modern-related-views i {
            color: #667eea;
            font-size: 0.8rem;
        }

        /* Modern Footer */
        .modern-footer {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        .modern-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
        }

        .modern-footer-main {
            padding: 4rem 0 3rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }

        .modern-footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .modern-footer-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1.5fr;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .modern-footer-column {
            display: flex;
            flex-direction: column;
        }

        .modern-footer-brand {
            color: #ffffff;
            font-size: 1.75rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .modern-footer-description {
            color: #cbd5e0;
            line-height: 1.7;
            margin-bottom: 2rem;
            font-size: 0.95rem;
        }

        .modern-footer-title {
            color: white;
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .modern-footer-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 30px;
            height: 2px;
            background: #667eea;
            border-radius: 1px;
        }

        .modern-footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .modern-footer-links li {
            margin-bottom: 0.75rem;
        }

        .modern-footer-links a {
            color: #cbd5e0;
            text-decoration: none;
            transition: all 0.3s ease;
            display: block;
            padding: 0.25rem 0;
            font-size: 0.9rem;
        }

        .modern-footer-links a:hover {
            color: #667eea;
            padding-left: 0.5rem;
        }

        .modern-footer-contact {
            color: #cbd5e0;
            line-height: 1.6;
        }

        .modern-footer-contact p {
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.9rem;
        }

        .modern-footer-contact i {
            color: #667eea;
            width: 18px;
            font-size: 1rem;
        }

        .modern-social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .modern-social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modern-social-links a:hover {
            background: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            border-color: #667eea;
        }

        .modern-footer-bottom {
            padding: 2rem 0;
            background: rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .modern-footer-bottom-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .modern-footer-copyright {
            color: #cbd5e0;
            font-size: 0.9rem;
        }

        .modern-footer-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .modern-footer-menu a {
            color: #cbd5e0;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .modern-footer-menu a:hover {
            color: #667eea;
        }

        /* Smart Responsive Design */
        @media (max-width: 1200px) {
            .smart-grid-layout {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .sidebar-section {
                order: -1;
            }

            .trending-news-grid,
            .categories-smart-grid,
            .social-follow-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1024px) {
            .smart-article-container {
                margin: 1rem;
            }

            .main-article-section {
                border-radius: 15px;
            }

            .modern-article-header {
                padding: 2rem;
            }

            .modern-article-title {
                font-size: 2.5rem;
            }

            .modern-footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .trending-news-grid,
            .categories-smart-grid,
            .social-follow-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .blog-article-wrapper {
                margin: 0.5rem;
                border-radius: 10px;
            }

            .modern-article-header {
                padding: 1.5rem;
            }

            .modern-article-title {
                font-size: 2rem;
            }

            .modern-article-meta {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .modern-featured-image {
                height: 300px;
            }

            .modern-article-content {
                padding: 2rem 1.5rem;
            }

            .modern-social-share {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .modern-author-card {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .modern-related-item {
                flex-direction: column;
                padding: 1rem;
            }

            .modern-related-image {
                width: 100%;
                height: 200px;
            }

            .modern-footer-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .modern-footer-bottom-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .modern-footer-menu {
                justify-content: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            /* Header responsive */
            .header-top-content {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
                padding: 0.5rem 0;
            }

            .header-left {
                gap: 1rem;
                justify-content: center;
            }

            .header-right {
                gap: 1rem;
                justify-content: center;
            }

            .header-date,
            .weather-info {
                font-size: 0.8rem;
            }

            .social-link {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .modern-article-title {
                font-size: 1.75rem;
            }

            .modern-article-content {
                padding: 1.5rem 1rem;
            }

            .modern-author-section,
            .modern-related-news {
                padding: 1.5rem 1rem;
                margin: 2rem 0;
            }

            .modern-social-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* Article Container */
        .article-container {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Article Header */
        .article-header {
            padding: 2rem 0.5rem 1rem 0.5rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .article-title {
            font-size: 2.5rem;
            font-weight: 800;
            line-height: 1.2;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .article-meta-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            color: #6b7280;
            font-size: 0.9rem;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .meta-item i {
            color: #667eea;
        }

        .category-badge {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
        }

        /* Featured Image */
        .featured-image-container {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }

        .featured-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .featured-image:hover {
            transform: scale(1.02);
        }

        /* Social & Font Controls */
        .article-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
            background: #f8fafc;
            border-bottom: 1px solid #f1f5f9;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .social-share-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .share-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .social-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .social-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .social-btn.facebook { background: #1877f2; }
        .social-btn.twitter { background: #1da1f2; }
        .social-btn.linkedin { background: #0077b5; }
        .social-btn.whatsapp { background: #25d366; }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .font-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .font-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .font-buttons {
            display: flex;
            gap: 0.25rem;
        }

        .font-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #374151;
        }

        .font-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Article Content */
        .article-body {
            padding: 2rem;
        }

        /* Article Text Content */
        .article-text {
            font-size: 1.125rem;
            line-height: 1.8;
            color: #374151;
            margin-bottom: 2rem;
        }

        .article-text p {
            margin-bottom: 1.5rem;
        }

        .article-text h2,
        .article-text h3 {
            color: #1f2937;
            margin: 2rem 0 1rem 0;
            font-weight: 700;
        }

        .article-text h2 {
            font-size: 1.5rem;
        }

        .article-text h3 {
            font-size: 1.25rem;
        }

        /* Author Info Section */
        .author-info-section {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 0.75rem;
            margin-bottom: 2rem;
            border: 1px solid #f1f5f9;
        }

        .author-card {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #667eea;
        }

        .author-details h4 {
            font-size: 1.125rem;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 0.25rem 0;
        }

        .author-role {
            font-size: 0.875rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .author-bio {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.5;
        }

        .article-meta-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .publish-info {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .meta-item-bottom {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .meta-item-bottom i {
            color: #667eea;
        }

        /* Related News Section */
        .related-news-section {
            margin-top: 3rem;
            padding: 2rem 0;
            border-top: 2px solid #f1f5f9;
        }

        .related-news-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }

        .related-news-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: #667eea;
            border-radius: 2px;
        }

        .related-news-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .related-news-card {
            background: white;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .related-news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .related-card-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .related-news-card:hover .related-card-image {
            transform: scale(1.05);
        }

        .related-card-content {
            padding: 1.5rem;
        }

        .related-card-category {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .related-card-title {
            margin: 0 0 1rem 0;
        }

        .related-card-title a {
            color: #1f2937;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.125rem;
            line-height: 1.4;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .related-card-title a:hover {
            color: #667eea;
        }

        .related-card-excerpt {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .related-card-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: #6b7280;
        }

        .related-card-date {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .related-card-date i {
            color: #667eea;
        }

        .related-card-views {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .related-card-views i {
            color: #10b981;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        /* Google Ads Widget */
        .ads-widget {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .ads-header {
            background: #f8fafc;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f1f5f9;
            text-align: center;
        }

        .ads-label {
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ads-content {
            padding: 1rem;
            text-align: center;
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            color: #6b7280;
            font-size: 0.9rem;
        }

        /* News Tabs Widget */
        .news-tabs-widget {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .news-tabs-nav {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #f1f5f9;
        }

        .news-tab-btn {
            flex: 1;
            padding: 1rem;
            background: none;
            border: none;
            font-weight: 600;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .news-tab-btn.active {
            color: #667eea;
            background: white;
        }

        .news-tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #667eea;
        }

        .news-tab-btn:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .news-tab-content {
            padding: 1.5rem;
            min-height: 300px;
        }

        .news-tab-pane {
            display: none;
        }

        .news-tab-pane.active {
            display: block;
        }

        /* Categories Widget */
        .categories-widget {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .categories-header {
            background: #667eea;
            color: white;
            padding: 1rem 1.5rem;
            font-weight: 700;
            font-size: 1.125rem;
        }

        .categories-content {
            padding: 1.5rem;
        }

        /* News Items */
        .news-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .news-item:hover {
            background: #f8fafc;
            margin: 0 -1.5rem;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-item-image {
            width: 80px;
            height: 60px;
            border-radius: 0.5rem;
            object-fit: cover;
            flex-shrink: 0;
        }

        .news-item-content {
            flex: 1;
        }

        .news-item-title {
            margin: 0 0 0.5rem 0;
        }

        .news-item-title a {
            color: #374151;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.4;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .news-item-title a:hover {
            color: #667eea;
        }

        .news-item-date {
            font-size: 0.75rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .news-item-date i {
            font-size: 0.7rem;
        }

        /* Categories Widget */
        .categories-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .category-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .category-link:hover {
            background: #667eea;
            color: white;
            transform: translateX(3px);
        }

        .category-link.active {
            background: #667eea;
            color: white;
        }

        .category-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Footer Styles - Fixed Version */
        .old-footer {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
            color: #e5e7eb !important;
            margin-top: 4rem !important;
            width: 100% !important;
        }

        .footer-main {
            padding: 3rem 0 2rem !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .footer-container {
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 2rem !important;
        }

        .footer-grid {
            display: grid !important;
            grid-template-columns: 2fr 1fr 1fr 1.5fr !important;
            gap: 2.5rem !important;
            margin-bottom: 2rem !important;
        }

        .footer-column {
            display: flex !important;
            flex-direction: column !important;
        }

        .footer-brand-name {
            color: #ffffff !important;
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            margin-bottom: 1rem !important;
        }

        .footer-description {
            color: #e2e8f0 !important;
            line-height: 1.6 !important;
            margin-bottom: 1.5rem !important;
        }

        .footer-title {
            color: white !important;
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            margin-bottom: 1.5rem !important;
        }

        .footer-links {
            list-style: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .footer-links li {
            margin-bottom: 0.75rem !important;
        }

        .footer-links a {
            color: #d1d5db !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            display: block !important;
            padding: 0.25rem 0 !important;
        }

        .footer-links a:hover {
            color: #3b82f6 !important;
            padding-left: 0.5rem !important;
        }

        .footer-contact-info {
            color: #e2e8f0 !important;
            line-height: 1.6 !important;
        }

        .footer-contact-info p {
            margin-bottom: 0.75rem !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.75rem !important;
        }

        .footer-contact-info i {
            color: #3b82f6 !important;
            width: 18px !important;
            font-size: 1rem !important;
        }

        .social-links {
            display: flex !important;
            gap: 0.75rem !important;
            margin-top: 1rem !important;
        }

        .social-links a {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 40px !important;
            height: 40px !important;
            background: rgba(255, 255, 255, 0.1) !important;
            border-radius: 50% !important;
            color: white !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            font-size: 1.1rem !important;
        }

        .social-links a:hover {
            background: #3b82f6 !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4) !important;
        }

        .footer-bottom {
            padding: 1.5rem 0 !important;
            background: rgba(0, 0, 0, 0.2) !important;
        }

        .footer-bottom-content {
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 2rem !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            gap: 1rem !important;
        }

        .footer-copyright {
            color: #d1d5db !important;
            font-size: 0.9rem !important;
        }

        .footer-menu-bottom {
            display: flex !important;
            gap: 2rem !important;
            list-style: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .footer-menu-bottom a {
            color: #d1d5db !important;
            text-decoration: none !important;
            font-size: 0.9rem !important;
            transition: color 0.3s ease !important;
        }

        .footer-menu-bottom a:hover {
            color: #3b82f6 !important;
        }

        /* Categories Widget */
        .categories-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .category-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .category-link:hover {
            background: #667eea;
            color: white;
            transform: translateX(5px);
        }

        .category-link.active {
            background: #667eea;
            color: white;
        }

        .category-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Tags Widget */
        .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tag {
            background: #f3f4f6;
            color: #374151;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tag:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        /* Footer CSS Fixes */
        .old-footer-backup {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
            color: #e5e7eb !important;
            margin-top: 2rem !important;
            display: block !important;
            width: 100% !important;
            position: relative !important;
        }

        .footer-main {
            padding: 3rem 0 2rem !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .footer-container {
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 2rem !important;
        }

        .footer-grid {
            display: grid !important;
            grid-template-columns: 2fr 1fr 1fr 1.5fr !important;
            gap: 3rem !important;
            margin-bottom: 2rem !important;
        }

        .footer-column {
            display: flex !important;
            flex-direction: column !important;
            background: transparent !important;
        }

        .footer-brand-name {
            color: #ffffff !important;
            font-size: 1.5rem !important;
            font-weight: 700 !important;
        }

        .footer-description {
            color: #e2e8f0 !important;
            line-height: 1.6 !important;
        }

        .footer-title {
            color: white !important;
            font-size: 1.25rem !important;
            font-weight: 600 !important;
            margin-bottom: 1.5rem !important;
        }

        .footer-links a {
            color: #d1d5db !important;
            text-decoration: none !important;
            transition: color 0.3s ease !important;
        }

        .footer-links a:hover {
            color: #3b82f6 !important;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .news-hero-title,
            .article-title {
                font-size: 2rem;
            }

            .related-news-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .footer-grid {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 2rem !important;
            }
        }

        @media (max-width: 768px) {
            .news-hero {
                padding: 1.5rem 0;
            }

            .news-hero-title,
            .article-title {
                font-size: 1.75rem;
            }

            .news-hero-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .article-header {
                padding: 1.5rem;
            }

            .article-body {
                padding: 1.5rem;
            }

            .article-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1rem 1.5rem;
            }

            .related-news-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .footer-grid {
                grid-template-columns: 1fr !important;
                gap: 2rem !important;
                padding: 0 1rem !important;
            }

            /* Header responsive */
            .header-top-content {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
                padding: 0.5rem 0;
            }

            .header-left {
                gap: 1rem;
                justify-content: center;
            }

            .header-right {
                gap: 1rem;
                justify-content: center;
            }

            .header-date,
            .weather-info {
                font-size: 0.8rem;
            }

            .social-link {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }

            /* Sidebar responsive */
            .news-tab-content,
            .categories-content {
                padding: 1rem;
            }

            .news-item {
                padding: 0.75rem 0;
            }

            .news-item-image {
                width: 70px;
                height: 55px;
            }

            .ads-content {
                min-height: 200px;
            }
        }

        @media (max-width: 480px) {
            .article-title {
                font-size: 1.5rem;
            }

            .article-content p {
                font-size: 1rem;
            }

            .sidebar-widget {
                padding: 1rem;
            }

            .related-item,
            .popular-item,
            .recent-item {
                flex-direction: column;
                gap: 0.75rem;
            }

            .related-image,
            .popular-image,
            .recent-image {
                width: 100%;
                height: 150px;
            }
        }

        /* Override existing styles to match our new design */
        .news-details-main {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .news-article .article-header {
            padding: 2rem 2rem 1rem 2rem !important;
            border-bottom: 1px solid #f1f5f9 !important;
        }

        .news-article .article-title {
            font-size: 2.5rem !important;
            font-weight: 800 !important;
            line-height: 1.2 !important;
            color: #1f2937 !important;
            margin-bottom: 1rem !important;
        }

        .news-article .article-meta {
            display: flex !important;
            align-items: center !important;
            gap: 1.5rem !important;
            color: #6b7280 !important;
            font-size: 0.9rem !important;
            flex-wrap: wrap !important;
            margin-bottom: 1rem !important;
        }

        .news-article .article-meta span {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        .news-article .article-meta i {
            color: #667eea !important;
        }

        .news-article .article-category a {
            background: #667eea !important;
            color: white !important;
            padding: 0.5rem 1rem !important;
            border-radius: 2rem !important;
            font-size: 0.875rem !important;
            font-weight: 600 !important;
            text-decoration: none !important;
        }

        .news-article .article-image {
            position: relative !important;
            width: 100% !important;
            height: 400px !important;
            overflow: hidden !important;
            margin-bottom: 1.5rem !important;
        }

        .news-article .article-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: transform 0.3s ease !important;
        }

        .news-article .article-image:hover img {
            transform: scale(1.02) !important;
        }

        .news-article .content-controls {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 1rem 2rem !important;
            background: #f8fafc !important;
            border-bottom: 1px solid #f1f5f9 !important;
            flex-wrap: wrap !important;
            gap: 1rem !important;
        }

        .news-article .content-body {
            padding: 2rem !important;
            font-size: 1.125rem !important;
            line-height: 1.8 !important;
            color: #374151 !important;
        }

        .news-article .content-body p {
            margin-bottom: 1.5rem !important;
        }

        .news-article .article-author {
            background: #f8fafc !important;
            padding: 2rem !important;
            border-radius: 0.75rem !important;
            margin: 2rem !important;
            border: 1px solid #f1f5f9 !important;
            display: flex !important;
            align-items: center !important;
            gap: 1rem !important;
        }

        .news-article .author-avatar img {
            width: 60px !important;
            height: 60px !important;
            border-radius: 50% !important;
            object-fit: cover !important;
            border: 3px solid #667eea !important;
        }

        .news-article .author-name strong {
            font-size: 1.125rem !important;
            font-weight: 700 !important;
            color: #1f2937 !important;
        }

        .news-article .author-role {
            font-size: 0.875rem !important;
            color: #667eea !important;
            font-weight: 600 !important;
        }

        /* Reorganize layout order */
        .news-article .article-header {
            order: 1 !important;
        }

        .news-article .article-image {
            order: 1 !important;
            margin-bottom: 1rem !important;
        }

        .news-article .content-controls {
            order: 4 !important;
        }

        .news-article .content-body {
            order: 5 !important;
        }

        .news-article .article-author {
            order: 6 !important;
        }

        .news-article {
            display: flex !important;
            flex-direction: column !important;
        }

        /* Move article-meta after image */
        .news-article .article-meta {
            order: 2 !important;
            margin-bottom: 1rem !important;
            padding-bottom: 1rem !important;
            border-bottom: 1px solid #f1f5f9 !important;
        }

        .news-article .article-title {
            order: 0 !important;
            margin-bottom: 1rem !important;
        }

        /* Hide author section from header, we'll show it later */
        .news-article .article-header .article-author {
            display: none !important;
        }

        /* Style the article meta section */
        .news-article .article-meta {
            background: #f8fafc !important;
            padding: 1rem 2rem !important;
            border-radius: 0.5rem !important;
            margin: 0 2rem 1rem 2rem !important;
        }

        /* Related News Widget in Sidebar */
        .related-news-widget {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }

        .related-news-widget .widget-header {
            background: #667eea;
            color: white;
            padding: 1rem 1.5rem;
        }

        .related-news-widget .widget-title {
            font-size: 1.125rem;
            font-weight: 700;
            margin: 0;
        }

        .related-news-widget .widget-content {
            padding: 1.5rem;
        }

        .related-news-item {
            display: flex;
            gap: 0.875rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f5f9;
            transition: all 0.3s ease;
        }

        .related-news-item:hover {
            background: #f8fafc;
            margin: 0 -1.5rem;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
        }

        .related-news-item:last-child {
            border-bottom: none;
        }

        .related-item-image {
            width: 85px;
            height: 65px;
            border-radius: 0.75rem;
            overflow: hidden;
            flex-shrink: 0;
        }

        .related-item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .related-news-item:hover .related-item-image img {
            transform: scale(1.05);
        }

        .related-item-content {
            flex: 1;
        }

        .related-item-title {
            margin: 0 0 0.5rem 0;
        }

        .related-item-title a {
            color: #374151;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            line-height: 1.4;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .related-item-title a:hover {
            color: #667eea;
        }

        .related-item-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .related-item-date,
        .related-item-category {
            font-size: 0.75rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .related-item-date i,
        .related-item-category i {
            font-size: 0.7rem;
            color: #667eea;
        }

        .no-related-news {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 2rem 0;
        }

        /* Related News in Main Content */
        .main-related-news {
            order: 3 !important;
            margin: 0 0rem 1rem 0rem !important;
            background: #f8fafc !important;
            border-radius: 0.75rem !important;
            padding: 1.5rem !important;
            border: 1px solid #f1f5f9 !important;
        }

        .main-related-news .modern-related-title {
            font-size: 1.25rem !important;
            font-weight: 700 !important;
            color: #1f2937 !important;
            margin-bottom: 1rem !important;
            padding-bottom: 0.5rem !important;
            border-bottom: 2px solid #667eea !important;
        }

        .main-related-news .related-grid {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .main-related-news .related-item {
            display: flex !important;
            gap: 0.75rem !important;
            background: white !important;
            padding: 1rem !important;
            border-radius: 0.5rem !important;
            transition: all 0.3s ease !important;
            border: 1px solid #e5e7eb !important;
        }

        .main-related-news .related-item:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        .main-related-news .related-image {
            width: 60px !important;
            height: 45px !important;
            border-radius: 0.375rem !important;
            overflow: hidden !important;
            flex-shrink: 0 !important;
        }

        .main-related-news .related-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
        }

        .main-related-news .related-content {
            flex: 1 !important;
        }

        .main-related-news .related-item-title {
            margin: 0 0 0.25rem 0 !important;
        }

        .main-related-news .related-item-title a {
            color: #374151 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            font-size: 0.8rem !important;
            line-height: 1.3 !important;
            transition: color 0.3s ease !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
        }

        .main-related-news .related-item-title a:hover {
            color: #667eea !important;
        }

        .main-related-news .related-meta {
            display: flex !important;
            gap: 0.5rem !important;
            font-size: 0.7rem !important;
            color: #6b7280 !important;
        }

        .main-related-news .related-date,
        .main-related-news .related-views {
            display: flex !important;
            align-items: center !important;
            gap: 0.25rem !important;
        }

        .main-related-news .related-date i,
        .main-related-news .related-views i {
            color: #667eea !important;
            font-size: 0.6rem !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .content-wrapper {
                max-width: 100%;
                padding: 0 1rem;
            }

            .main-related-news {
                margin: 0 1rem 1rem 1rem !important;
                padding: 1rem !important;
            }

            .news-article .article-meta {
                margin: 0 1rem 1rem 1rem !important;
                padding: 1rem !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php echo $__env->make('frontend.body.header_ultra_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Modern Blog Hero -->
    <section class="blog-hero">
        <div class="container">
            <div class="blog-breadcrumb">
                <a href="<?php echo e(url('/')); ?>" class="breadcrumb-link">
                    <i class="fas fa-home"></i>
                    Home
                </a>
                <i class="fas fa-chevron-right"></i>
                <a href="<?php echo e(url('news/category/'.$news->category->id.'/'.$news->category->category_slug)); ?>" class="breadcrumb-link">
                    <?php echo e($news->category->category_name); ?>

                </a>
                <i class="fas fa-chevron-right"></i>
                <span class="breadcrumb-current"><?php echo e(Str::limit($news->news_title, 50)); ?></span>
            </div>
        </div>
    </section>
  
    <!-- Smart Article Layout -->
    <section class="main-content">
      <div class="container">
        <div class="smart-article-container">
            <div class="smart-grid-layout">
                <!-- Main Article Section -->
                <div class="main-article-section">
                <!-- Modern Blog Article -->
                <article class="modern-blog-article">
                    <!-- Article Header -->
                    <header class="article-header">
                        <div class="article-meta">
                            <span class="article-category">
                                <i class="fas fa-tag"></i>
                                <a href="<?php echo e(url('news/category/'.$news->category->id.'/'.$news->category->category_slug)); ?>">
                                    <?php echo e($news->category->category_name); ?>

                                </a>
                            </span>
                            <?php if($news->subcategory_id): ?>
                            <span class="article-subcategory">
                                <i class="fas fa-tags"></i>
                                <?php echo e($news->subcategory->subcategory_name); ?>

                            </span>
                            <?php endif; ?>
                            <span class="article-date">
                                <i class="fas fa-calendar-alt"></i>
                                <?php echo e($news->created_at->format('F d, Y')); ?>

                            </span>
                            <span class="article-views">
                                <i class="fas fa-eye"></i>
                                <?php echo e($news->view_count ?? 0); ?> views
                            </span>
                        </div>

                        <h1 class="article-title"><?php echo e($news->news_title); ?></h1>

                        <div class="article-author">
                            <div class="author-avatar">
                                <img src="<?php echo e((!empty($news->user->photo)) ? url('upload/admin_images/'.$news->user->photo): url('upload/no_image.jpg')); ?>"
                                     alt="<?php echo e($news->user->name); ?>" loading="lazy">
                            </div>
                                <div class="author-name">
                                    <strong><?php echo e($news->user->name); ?></strong>
                                    <span class="author-role">Reporter</span>
                                </div>
                                <div class="author-meta">
                                    <span class="publish-date">
                                        <i class="fas fa-clock"></i>
                                        Published <?php echo e($news->created_at->format('M d, Y \a\t g:i A')); ?>

                                    </span>
                                    <?php if($news->updated_at != $news->created_at): ?>
                                    <span class="update-date">
                                        <i class="fas fa-edit"></i>
                                        Updated <?php echo e($news->updated_at->format('M d, Y \a\t g:i A')); ?>

                                    </span>
                                    <?php endif; ?>
                                </div>

                            </div>
                             <!-- Featured Image -->
                    <div class="article-image">
                        <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" loading="lazy">
                        <div class="image-caption">
                            <?php echo e($news->news_title); ?>

                        </div>

                          <!-- Article Content -->
                    <div class="article-content">
                        <!-- Font Size Controls -->
                        <div class="content-controls">
                            <div class="font-controls">
                                <span class="control-label">Font Size:</span>
                                <button class="font-btn" id="decreaseFont" title="Decrease font size">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="font-btn" id="increaseFont" title="Increase font size">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="share-controls">
                                <span class="control-label">Share:</span>
                                <a href="#" class="share-btn facebook" title="Share on Facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="share-btn twitter" title="Share on Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="share-btn linkedin" title="Share on LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="share-btn whatsapp" title="Share on WhatsApp">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>

                        <!-- Main Content -->
                        <div class="content-body" id="articleContent">
                            <?php echo $news->news_details; ?>

                        </div>

                        <!-- Tags -->
                        <?php if(isset($tags_all) && count($tags_all) > 0): ?>
                        <div class="article-tags">
                            <span class="tags-label">
                                <i class="fas fa-tags"></i>
                                Tags:
                            </span>
                            <div class="tags-list">
                                <?php $__currentLoopData = $tags_all; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="#" class="tag-link"><?php echo e(ucwords($tag)); ?></a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    </div>
                   
                        </div>
                       
                
                    </header>

                    

                    <!-- Related News in Main Content -->
                    <?php if(isset($related_news) && $related_news->count() > 0): ?>
                    <div class="main-related-news">
                        <h3 class="modern-related-title">Related News</h3>
                        <div class="related-grid">
                            <?php $__currentLoopData = $related_news->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="related-item">
                                <div class="related-image">
                                    <img src="<?php echo e(asset($item->image)); ?>" alt="<?php echo e($item->news_title); ?>" loading="lazy">
                                </div>
                                <div class="related-content">
                                    <h4 class="related-item-title">
                                        <a href="<?php echo e(url('news/details/'.$item->id.'/'.$item->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($item->news_title, 60)); ?>

                                        </a>
                                    </h4>
                                    <div class="related-meta">
                                        <span class="related-date">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo e($item->created_at->format('M d, Y')); ?>

                                        </span>
                                        <span class="related-views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo e($item->view_count ?? 0); ?>

                                        </span>
                                    </div>
                                </div>
                            </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                  


                </article>
                </div>

                <!-- Smart Sidebar Section -->
                <div class="sidebar-section">
                    <!-- Trending News Widget -->
                    <div class="smart-widget">
                        <div class="smart-widget-header">
                            <h3 class="smart-widget-title">🔥 Trending Now</h3>
                        </div>
                        <div class="smart-widget-content">
                            <div class="trending-news-grid">
                                <?php if(isset($popular_news)): ?>
                                    <?php $__currentLoopData = $popular_news->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trending): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="trending-item">
                                        <?php if($trending->image): ?>
                                        <img src="<?php echo e(asset($trending->image)); ?>" alt="<?php echo e($trending->news_title); ?>" class="trending-image" loading="lazy">
                                        <?php endif; ?>
                                        <div class="trending-content">
                                            <h4 class="trending-title">
                                                <a href="<?php echo e(url('news/details/'.$trending->id.'/'.$trending->news_title_slug)); ?>">
                                                    <?php echo e(Str::limit($trending->news_title, 50)); ?>

                                                </a>
                                            </h4>
                                            <div class="trending-meta">
                                                <i class="fas fa-eye"></i>
                                                <span><?php echo e($trending->view_count ?? '0'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Newsletter Widget -->
                    <div class="smart-widget newsletter-widget">
                        <div class="newsletter-content">
                            <h3 class="newsletter-title">📧 Stay Updated</h3>
                            <p class="newsletter-subtitle">Get the latest news delivered to your inbox</p>
                            <form class="newsletter-form" action="#" method="POST">
                                <?php echo csrf_field(); ?>
                                <input type="email" name="email" placeholder="Enter your email address" class="newsletter-input" required>
                                <button type="submit" class="newsletter-btn">Subscribe Now</button>
                            </form>
                        </div>
                    </div>

                    <!-- Categories Widget -->
                    <div class="smart-widget">
                        <div class="smart-widget-header">
                            <h3 class="smart-widget-title">📂 Categories</h3>
                        </div>
                        <div class="smart-widget-content">
                            <div class="categories-smart-grid">
                                <?php if(isset($categories)): ?>
                                    <?php $__currentLoopData = $categories->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="category-smart-item">
                                        <span class="category-icon">📰</span>
                                        <div class="category-name"><?php echo e($category->category_name); ?></div>
                                        <div class="category-count"><?php echo e($category->news_posts_count ?? 0); ?> posts</div>
                                    </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Social Follow Widget -->
                    <div class="smart-widget">
                        <div class="smart-widget-header">
                            <h3 class="smart-widget-title">🌐 Follow Us</h3>
                        </div>
                        <div class="smart-widget-content">
                            <div class="social-follow-grid">
                                <a href="#" class="social-follow-item facebook">
                                    <span class="social-icon">📘</span>
                                    <div class="social-platform">Facebook</div>
                                    <div class="social-followers">12.5K followers</div>
                                </a>
                                <a href="#" class="social-follow-item twitter">
                                    <span class="social-icon">🐦</span>
                                    <div class="social-platform">Twitter</div>
                                    <div class="social-followers">8.2K followers</div>
                                </a>
                                <a href="#" class="social-follow-item youtube">
                                    <span class="social-icon">📺</span>
                                    <div class="social-platform">YouTube</div>
                                    <div class="social-followers">25K subscribers</div>
                                </a>
                                <a href="#" class="social-follow-item instagram">
                                    <span class="social-icon">📷</span>
                                    <div class="social-platform">Instagram</div>
                                    <div class="social-followers">15K followers</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
     
        
       
    </section>
     

<!-- Modern Footer -->
<footer class="modern-footer">
    <div class="modern-footer-main">
        <div class="modern-footer-container">
            <div class="modern-footer-grid">
                <!-- Company Info -->
                <div class="modern-footer-column">
                    <h2 class="modern-footer-brand">NitiKotha</h2>
                    <p class="modern-footer-description">
                        Your trusted source for the latest news and insights. We deliver accurate, timely, and comprehensive coverage of events that matter to you.
                    </p>
                    <div class="modern-social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="modern-footer-column">
                    <h3 class="modern-footer-title">Quick Links</h3>
                    <ul class="modern-footer-links">
                        <li><a href="<?php echo e(url('/')); ?>">Home</a></li>
                        <li><a href="<?php echo e(url('/about')); ?>">About Us</a></li>
                        <li><a href="<?php echo e(url('/contact')); ?>">Contact</a></li>
                        <li><a href="<?php echo e(url('/privacy')); ?>">Privacy Policy</a></li>
                        <li><a href="<?php echo e(url('/terms')); ?>">Terms of Service</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div class="modern-footer-column">
                    <h3 class="modern-footer-title">Categories</h3>
                    <ul class="modern-footer-links">
                        <?php if(isset($categories)): ?>
                            <?php $__currentLoopData = $categories->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>"><?php echo e($category->category_name); ?></a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div class="modern-footer-column">
                    <h3 class="modern-footer-title">Contact Info</h3>
                    <div class="modern-footer-contact">
                        <p><i class="fas fa-map-marker-alt"></i> Road -15, House -18, sector -12, Uttara Dhaka</p>
                        <p><i class="fas fa-phone"></i> 01736990123</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-globe"></i> www.nitikotha.com</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modern-footer-bottom">
        <div class="modern-footer-bottom-content">
            <div class="modern-footer-copyright">
                <p>&copy; <?php echo e(date('Y')); ?> NitiKotha. All rights reserved. Developed by Arif</p>
            </div>
            <ul class="modern-footer-menu">
                <li><a href="<?php echo e(url('/privacy')); ?>">Privacy Policy</a></li>
                <li><a href="<?php echo e(url('/terms')); ?>">Terms of Service</a></li>
                <li><a href="<?php echo e(url('/sitemap')); ?>">Sitemap</a></li>
            </ul>
        </div>
    </div>
</footer>

<style>
/* Modern News Details Styles */
.news-details-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.news-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    flex-wrap: wrap;
}

.breadcrumb-link {
    color: #3b82f6;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.breadcrumb-link:hover {
    color: #1e40af;
}

.breadcrumb-current {
    color: #6b7280;
}

.news-details-content {
    padding: 3rem 0;
}

.news-details-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.news-details-main {
    background: #ffffff;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Article Styles */
.news-article {
    padding: 2rem;
}

.article-header {
    margin-bottom: 2rem;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.article-category a {
    color: #3b82f6;
    text-decoration: none;
}

.article-category a:hover {
    color: #1e40af;
}

.article-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1.2;
    margin-bottom: 2rem;
}

.article-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border-left: 4px solid #3b82f6;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info {
    flex: 1;
}

.author-name {
    margin-bottom: 0.5rem;
}

.author-name strong {
    color: #1f2937;
    font-size: 1.125rem;
}

.author-role {
    color: #6b7280;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.author-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.author-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Article Image */
.article-image {
    margin: 2rem 0;
    border-radius: 0.75rem;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: auto;
    display: block;
}

.image-caption {
    padding: 1rem;
    background: #f8fafc;
    color: #6b7280;
    font-size: 0.875rem;
    font-style: italic;
    text-align: center;
}

/* Article Content */
.article-content {
    margin-top: 2rem;
}

.content-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.font-controls,
.share-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.font-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.font-btn:hover {
    background: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
}

.share-btn {
    width: 32px;
    height: 32px;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.share-btn.facebook { background: #1877f2; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.whatsapp { background: #25d366; }

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.content-body {
    line-height: 1.8;
    color: #374151;
    font-size: 1.125rem;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body h1,
.content-body h2,
.content-body h3,
.content-body h4,
.content-body h5,
.content-body h6 {
    color: #1f2937;
    font-weight: 600;
    margin: 2rem 0 1rem;
}

.content-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.content-body blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #6b7280;
}

/* Tags */
.article-tags {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.tags-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-link {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.tag-link:hover {
    background: #3b82f6;
    color: #ffffff;
}

/* Related News */
.related-news {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

/* Removed old related-title class */

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-item {
    background: #ffffff;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.related-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.related-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.related-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-content {
    padding: 1rem;
}

.related-item-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.related-item-title a {
    color: #1f2937;
    text-decoration: none;
}

.related-item-title a:hover {
    color: #3b82f6;
}

.related-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #6b7280;
}

.related-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Sidebar */
.news-details-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-widget {
    background: #ffffff;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.widget-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3b82f6;
}

/* Popular/Recent News Lists */
.popular-news-list,
.recent-news-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.popular-item,
.recent-item {
    display: flex;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.popular-item:last-child,
.recent-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.popular-image,
.recent-image {
    width: 80px;
    height: 60px;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
}

.popular-image img,
.recent-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-content,
.recent-content {
    flex: 1;
}

.popular-title,
.recent-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.popular-title a,
.recent-title a {
    color: #1f2937;
    text-decoration: none;
}

.popular-title a:hover,
.recent-title a:hover {
    color: #3b82f6;
}

.popular-meta,
.recent-meta {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Categories List */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    text-decoration: none;
    color: #1f2937;
    transition: all 0.3s ease;
}

.category-link:hover,
.category-link.active {
    background: #3b82f6;
    color: #ffffff;
}

.category-count {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.category-link:hover .category-count,
.category-link.active .category-count {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .news-details-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-title {
        font-size: 1.75rem;
    }

    .article-meta {
        gap: 1rem;
    }

    .content-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .font-controls,
    .share-controls {
        justify-content: center;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .news-details-header {
        padding: 1.5rem 0;
    }

    .news-details-content {
        padding: 2rem 0;
    }

    .news-article {
        padding: 1.5rem;
    }

    .article-title {
        font-size: 1.5rem;
    }

    .content-body {
        font-size: 1rem;
    }

    .article-author {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
// Font size controls
document.addEventListener('DOMContentLoaded', function() {
    const increaseBtn = document.getElementById('increaseFont');
    const decreaseBtn = document.getElementById('decreaseFont');
    const content = document.getElementById('articleContent');

    let currentSize = 18; // Base font size in pixels

    if (increaseBtn && decreaseBtn && content) {
        increaseBtn.addEventListener('click', function() {
            if (currentSize < 24) {
                currentSize += 2;
                content.style.fontSize = currentSize + 'px';
            }
        });

        decreaseBtn.addEventListener('click', function() {
            if (currentSize > 14) {
                currentSize -= 2;
                content.style.fontSize = currentSize + 'px';
            }
        });
    }

    // Share functionality
    const shareButtons = document.querySelectorAll('.share-btn');
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);

            let shareUrl = '';
            if (this.classList.contains('facebook')) {
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            } else if (this.classList.contains('twitter')) {
                shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
            } else if (this.classList.contains('linkedin')) {
                shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
            } else if (this.classList.contains('whatsapp')) {
                shareUrl = `https://wa.me/?text=${title} ${url}`;
            }

            if (shareUrl) {
                window.open(shareUrl, '_blank', 'width=600,height=400');
            }
        });
    });
});
</script>

    <!-- Footer include removed - using inline modern footer instead -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab panes
            document.querySelectorAll('.news-tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.news-tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab pane
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Font size controls
        let currentFontSize = 1.125; // Default font size in rem

        function changeFontSize(action) {
            const articleText = document.getElementById('article-text');
            if (!articleText) return;

            if (action === 'increase') {
                currentFontSize += 0.125;
                if (currentFontSize > 1.5) currentFontSize = 1.5; // Max size
            } else if (action === 'decrease') {
                currentFontSize -= 0.125;
                if (currentFontSize < 0.875) currentFontSize = 0.875; // Min size
            }

            articleText.style.fontSize = currentFontSize + 'rem';
        }

        // Social sharing functions
        function shareOnFacebook(url, title) {
            window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(url), '_blank', 'width=600,height=400');
        }

        function shareOnTwitter(url, title) {
            window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(url) + '&text=' + encodeURIComponent(title), '_blank', 'width=600,height=400');
        }

        function shareOnLinkedIn(url) {
            window.open('https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(url), '_blank', 'width=600,height=400');
        }

        function shareOnWhatsApp(url, title) {
            window.open('https://wa.me/?text=' + encodeURIComponent(title + ' ' + url), '_blank');
        }
    </script>
</body>
</html>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/news/news_details.blade.php ENDPATH**/ ?>