<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Subcategory;

class CategoryController extends Controller
{
    public function AllCategory(){

        $categories = Category::orderBy('display_order', 'asc')->orderBy('created_at', 'desc')->get();
        return view('backend.category.category_all',compact('categories'));

    } // End Mehtod



    public function AddCategory(){
         return view('backend.category.category_add');
    }// End Mehtod 


    public function StoreCategory(Request $request){

        Category::insert([
            'category_name' => $request->category_name,
            'category_slug' => strtolower(str_replace(' ', '-', $request->category_name)),
            'icon' => $request->icon,

        ]);


         $notification = array(
            'message' => 'Category Inserted Successfully',
            'alert-type' => 'success'

        );

        return redirect()->route('all.category')->with($notification);


    }// End Mehtod


    public function EditCategory($id){
        $category = Category::findOrFail($id);
        return view('backend.category.category_edit',compact('category'));
    }// End Mehtod 


    public function UpdateCategory(Request $request){

        $cat_id = $request->id;

        Category::findOrFail($cat_id)->update([
            'category_name' => $request->category_name,
            'category_slug' => strtolower(str_replace(' ', '-', $request->category_name)),
            'icon' => $request->icon,

        ]);


         $notification = array(
            'message' => 'Category Updated Successfully',
            'alert-type' => 'success'

        );

        return redirect()->route('all.category')->with($notification);


    }// End Mehtod


    public function DeleteCategory($id){

        Category::findOrFail($id)->delete();

         $notification = array(
            'message' => 'Category Deleted Successfully',
            'alert-type' => 'success'

        );

        return redirect()->back()->with($notification);

    }// End Mehtod 

    ////////////// Sub Category All ///////////////


     public function AllSubCategory(){

        // Check if display_order column exists, if not use basic ordering
        try {
            $subcategories = Subcategory::with('category')
                ->orderBy('display_order', 'asc')
                ->orderBy('created_at', 'desc')
                ->get();
        } catch (\Exception $e) {
            // Fallback if display_order column doesn't exist
            $subcategories = Subcategory::with('category')
                ->orderBy('created_at', 'desc')
                ->get();

            // Add default values for missing columns
            foreach ($subcategories as $subcategory) {
                $subcategory->display_order = $subcategory->id;
                $subcategory->is_featured = false;
                $subcategory->is_active = true;
            }
        }

        return view('backend.subcategory.subcategory_all',compact('subcategories'));

    } // End Mehtod


    public function AddSubCategory(){

        $categories = Category::latest()->get();
        return view('backend.subcategory.subcategory_add',compact('categories'));

    }// End Mehtod 



     public function StoreSubCategory(Request $request){

        Subcategory::insert([
            'category_id' => $request->category_id,
            'subcategory_name' => $request->subcategory_name,
            'subcategory_slug' => strtolower(str_replace(' ', '-', $request->subcategory_name)),

        ]); 


         $notification = array(
            'message' => 'SubCategory Inserted Successfully',
            'alert-type' => 'success'

        );

        return redirect()->route('all.subcategory')->with($notification);


    }// End Mehtod 


    public function EditSubCategory($id){

        $categories = Category::latest()->get();
        $subcategory = Subcategory::findOrFail($id);
        return view('backend.subcategory.subcategory_edit',compact('categories','subcategory'));
    }// End Mehtod


 public function UpdateSubCategory(Request $request){

       $subcat_id = $request->id;

        Subcategory::findOrFail($subcat_id)->update([
            'category_id' => $request->category_id,
            'subcategory_name' => $request->subcategory_name,
            'subcategory_slug' => strtolower(str_replace(' ', '-', $request->subcategory_name)),

        ]); 


         $notification = array(
            'message' => 'SubCategory Updated Successfully',
            'alert-type' => 'success'

        );

        return redirect()->route('all.subcategory')->with($notification);


    }// End Mehtod 

     public function DeleteSubCategory($id){

        Subcategory::findOrFail($id)->delete();

         $notification = array(
            'message' => 'SubCategory Deleted Successfully',
            'alert-type' => 'success'

        );

        return redirect()->back()->with($notification);

    }// End Mehtod 

 
    public function GetSubCategory($category_id){

        $subcat = Subcategory::where('category_id',$category_id)->orderBy('subcategory_name','ASC')->get();
            return json_encode($subcat);

    }// End Mehtod

    /**
     * Update category order (single or bulk)
     */
    public function updateOrder(Request $request)
    {
        // Handle both single category and bulk updates
        if ($request->has('category_id')) {
            // Single category update
            $request->validate([
                'category_id' => 'required|integer|exists:categories,id',
                'order' => 'required|integer|min:0'
            ]);

            $category = Category::findOrFail($request->category_id);
            $category->update(['display_order' => $request->order]);

            return response()->json([
                'success' => true,
                'message' => 'Category order updated successfully!',
                'new_order' => $category->display_order
            ]);
        } else {
            // Bulk update (existing functionality)
            $request->validate([
                'categories' => 'required|array',
                'categories.*.id' => 'required|exists:categories,id',
                'categories.*.order' => 'required|integer|min:0'
            ]);

            foreach ($request->categories as $categoryData) {
                Category::where('id', $categoryData['id'])
                       ->update(['display_order' => $categoryData['order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Categories order updated successfully!'
            ]);
        }
    }

    /**
     * Toggle category featured status
     */
    public function toggleFeatured(Request $request, $id)
    {
        $category = Category::findOrFail($id);

        $category->update(['is_featured' => !$category->is_featured]);

        return response()->json([
            'success' => true,
            'is_featured' => $category->is_featured,
            'message' => $category->is_featured ? 'Category marked as featured!' : 'Category removed from featured!'
        ]);
    }

    /**
     * Toggle category active status
     */
    public function toggleActive(Request $request, $id)
    {
        $category = Category::findOrFail($id);

        $category->update(['is_active' => !$category->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $category->is_active,
            'message' => $category->is_active ? 'Category activated!' : 'Category deactivated!'
        ]);
    }

    /**
     * Update subcategory display order (single or bulk)
     */
    public function updateSubcategoryOrder(Request $request)
    {
        // Handle both single subcategory and bulk updates
        if ($request->has('subcategory_id')) {
            // Single subcategory update
            $request->validate([
                'subcategory_id' => 'required|integer|exists:subcategories,id',
                'order' => 'required|integer|min:0'
            ]);

            $subcategory = Subcategory::findOrFail($request->subcategory_id);
            $subcategory->update(['display_order' => $request->order]);

            return response()->json([
                'success' => true,
                'message' => 'Subcategory order updated successfully!',
                'new_order' => $subcategory->display_order
            ]);
        } else {
            // Bulk update (existing functionality)
            $request->validate([
                'subcategories' => 'required|array',
                'subcategories.*.id' => 'required|exists:subcategories,id',
                'subcategories.*.order' => 'required|integer|min:0'
            ]);

            foreach ($request->subcategories as $subcategoryData) {
                Subcategory::where('id', $subcategoryData['id'])
                           ->update(['display_order' => $subcategoryData['order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subcategories order updated successfully!'
            ]);
        }
    }

    /**
     * Toggle subcategory featured status
     */
    public function toggleSubcategoryFeatured(Request $request, $id)
    {
        $subcategory = Subcategory::findOrFail($id);
        $subcategory->is_featured = !$subcategory->is_featured;
        $subcategory->save();

        return response()->json([
            'success' => true,
            'message' => $subcategory->is_featured ? 'Subcategory marked as featured!' : 'Subcategory removed from featured!',
            'is_featured' => $subcategory->is_featured
        ]);
    }

    /**
     * Toggle subcategory active status
     */
    public function toggleSubcategoryActive(Request $request, $id)
    {
        $subcategory = Subcategory::findOrFail($id);
        $subcategory->is_active = !$subcategory->is_active;
        $subcategory->save();

        return response()->json([
            'success' => true,
            'message' => $subcategory->is_active ? 'Subcategory activated!' : 'Subcategory deactivated!',
            'is_active' => $subcategory->is_active
        ]);
    }

}
 