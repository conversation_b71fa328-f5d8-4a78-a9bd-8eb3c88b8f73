<?php $__env->startSection('admin'); ?>
<div class="page-content">
    <nav class="page-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Pending Posts</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="card-title">Pending Posts for Approval</h6>
                        <div>
                            <a href="<?php echo e(route('admin.posts.approved')); ?>" class="btn btn-success btn-sm">Approved Posts</a>
                            <a href="<?php echo e(route('admin.posts.rejected')); ?>" class="btn btn-danger btn-sm">Rejected Posts</a>
                        </div>
                    </div>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="select-all"></th>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Author</th>
                                    <th>Category</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><input type="checkbox" name="post_ids[]" value="<?php echo e($post->id); ?>" class="post-checkbox"></td>
                                        <td>
                                            <img src="<?php echo e(asset($post->image)); ?>" alt="<?php echo e($post->news_title); ?>" 
                                                 style="width: 60px; height: 40px; object-fit: cover;">
                                        </td>
                                        <td>
                                            <strong><?php echo e(Str::limit($post->news_title, 50)); ?></strong>
                                        </td>
                                        <td><?php echo e($post->user->name ?? 'N/A'); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($post->category->category_name ?? 'N/A'); ?></span>
                                        </td>
                                        <td><?php echo e($post->created_at->format('M d, Y H:i')); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.posts.show', $post->id)); ?>" 
                                                   class="btn btn-sm btn-info">
                                                    <i data-feather="eye"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.posts.approve', $post->id)); ?>" 
                                                      method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-success" 
                                                            onclick="return confirm('Are you sure you want to approve this post?')">
                                                        <i data-feather="check"></i>
                                                    </button>
                                                </form>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" data-bs-target="#rejectModal<?php echo e($post->id); ?>">
                                                    <i data-feather="x"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- Reject Modal -->
                                    <div class="modal fade" id="rejectModal<?php echo e($post->id); ?>" tabindex="-1">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">Reject Post</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <form action="<?php echo e(route('admin.posts.reject', $post->id)); ?>" method="POST">
                                                    <?php echo csrf_field(); ?>
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <label for="rejection_reason" class="form-label">Reason for Rejection</label>
                                                            <textarea name="rejection_reason" class="form-control" rows="3" required 
                                                                      placeholder="Please provide a reason for rejecting this post..."></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="submit" class="btn btn-danger">Reject Post</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <p class="mb-0">No pending posts found.</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($posts->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($posts->links()); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($posts->count() > 0): ?>
                        <div class="mt-3">
                            <button type="button" class="btn btn-success" onclick="bulkApprove()">
                                <i data-feather="check"></i> Bulk Approve Selected
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#bulkRejectModal">
                                <i data-feather="x"></i> Bulk Reject Selected
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Reject Modal -->
<div class="modal fade" id="bulkRejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Reject Posts</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('admin.posts.bulk-reject')); ?>" method="POST" id="bulkRejectForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulk_rejection_reason" class="form-label">Reason for Rejection</label>
                        <textarea name="rejection_reason" id="bulk_rejection_reason" class="form-control" rows="3" required 
                                  placeholder="Please provide a reason for rejecting these posts..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Selected Posts</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.post-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk approve function
function bulkApprove() {
    const selectedPosts = document.querySelectorAll('.post-checkbox:checked');
    if (selectedPosts.length === 0) {
        alert('Please select at least one post to approve.');
        return;
    }
    
    if (confirm(`Are you sure you want to approve ${selectedPosts.length} selected post(s)?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("admin.posts.bulk-approve")); ?>';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);
        
        selectedPosts.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'post_ids[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk reject form submission
document.getElementById('bulkRejectForm').addEventListener('submit', function(e) {
    const selectedPosts = document.querySelectorAll('.post-checkbox:checked');
    if (selectedPosts.length === 0) {
        e.preventDefault();
        alert('Please select at least one post to reject.');
        return;
    }
    
    selectedPosts.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'post_ids[]';
        input.value = checkbox.value;
        this.appendChild(input);
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/approval/pending.blade.php ENDPATH**/ ?>