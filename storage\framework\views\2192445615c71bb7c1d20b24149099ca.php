<footer class="premium-footer">
    <div class="footer-main">
        <!-- Container for Footer Content -->
        <div class="footer-container">
            <div class="footer-grid">
                <!-- Company Information -->
                <div class="footer-column company-column">
                    <div class="footer-brand">
                        <div class="footer-logo">
                            <img src="<?php echo e(asset('frontend/assets/images/logo.png')); ?>" alt="NitiKotha" class="footer-logo-img">
                            <div class="footer-logo-text">
                                <h3 class="footer-brand-name">NitiKotha</h3>
                                <span class="footer-brand-tagline">Premium News Portal</span>
                            </div>
                        </div>
                        <p class="footer-description">
                            Your trusted source for breaking news, in-depth analysis, and comprehensive coverage of current events. Stay informed with our reliable journalism and expert insights.
                        </p>
                        <div class="footer-contact">
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Road -15, House -18, sector -12, Uttara Dhaka</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <a href="tel:+8801736990123">01736990123</a>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-clock"></i>
                                <span>24/7 News Coverage</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="footer-column links-column">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li>
                            <a href="<?php echo e(url('/')); ?>">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-info-circle"></i>
                                <span>About Us</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-envelope"></i>
                                <span>Contact</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-shield-alt"></i>
                                <span>Privacy Policy</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-file-contract"></i>
                                <span>Terms of Service</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-question-circle"></i>
                                <span>FAQ</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-ad"></i>
                                <span>Advertise</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <i class="fas fa-rss"></i>
                                <span>RSS Feed</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Categories -->
                <div class="footer-column categories-column">
                    <h3 class="footer-title">Categories</h3>
                    <ul class="footer-links">
                        <?php if(isset($categories)): ?>
                            <?php $__currentLoopData = $categories->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>">
                                    <i class="fas fa-angle-right"></i>
                                    <span><?php echo e($category->category_name); ?></span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php $sampleCategories = ['Politics', 'Sports', 'Technology', 'Business', 'Health', 'Education', 'Entertainment', 'Opinion']; ?>
                            <?php $__currentLoopData = $sampleCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="#">
                                    <i class="fas fa-angle-right"></i>
                                    <span><?php echo e($cat); ?></span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Newsletter, Statistics, Social & Apps -->
                <div class="footer-column stats-column">
                    <!-- Newsletter Form -->
                    <div class="footer-newsletter">
                        <form class="newsletter-form" action="#" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="newsletter-input-group">
                                <input type="email" name="email" placeholder="Enter your email" class="newsletter-input" required>
                                <button type="submit" class="newsletter-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Live Statistics -->
                    <h3 class="footer-title">Live Statistics</h3>
                    <div class="footer-stats">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\NewsPost::where('status', 1)->count() ?? 1250); ?>+</div>
                                <div class="stat-label">News Articles</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\User::where('role', 'subscriber')->count() ?? 850); ?>+</div>
                                <div class="stat-label">Subscribers</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?php echo e(\App\Models\Category::count() ?? 12); ?>+</div>
                                <div class="stat-label">Categories</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">Coverage</div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Links -->
                    <div class="footer-social">
                        <div class="social-links">
                            <a href="#" class="social-link facebook" title="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link twitter" title="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link youtube" title="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link instagram" title="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link linkedin" title="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>

                    <!-- App Download Buttons -->
                    <div class="footer-apps">
                        <div class="app-buttons">
                            <a href="#" class="app-btn">
                                <img src="<?php echo e(asset('frontend/assets/images/google-play.png')); ?>" alt="Google Play" onerror="this.style.display='none'">
                                <div class="app-info">
                                    <span class="app-store">Google Play</span>
                                    <span class="app-text">Get it on</span>
                                </div>
                            </a>
                            <a href="#" class="app-btn">
                                <img src="<?php echo e(asset('frontend/assets/images/app-store.png')); ?>" alt="App Store" onerror="this.style.display='none'">
                                <div class="app-info">
                                    <span class="app-store">App Store</span>
                                    <span class="app-text">Download on the</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="footer-bottom-container">
            <div class="footer-bottom-content">
                <div class="copyright">
                    <p>&copy; <?php echo e(date('Y')); ?> NitiKotha. All rights reserved.</p>
                </div>
                <div class="footer-menu-bottom">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Cookie Policy</a>
                    <a href="#">Sitemap</a>
                </div>
                <div class="developer-credit">
                    <p>Developed by <a href="#" target="_blank">Arif</a></p>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/body/footer_modern.blade.php ENDPATH**/ ?>