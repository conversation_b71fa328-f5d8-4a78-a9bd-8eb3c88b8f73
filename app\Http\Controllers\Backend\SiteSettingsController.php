<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;


class SiteSettingsController extends Controller
{
    public function index()
    {
        $settings = SiteSetting::getAllGrouped();
        
        // Initialize default settings if they don't exist
        $this->initializeDefaultSettings();
        
        // Refresh settings after initialization
        $settings = SiteSetting::getAllGrouped();
        
        return view('backend.site_settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        foreach ($request->settings as $key => $value) {
            $setting = SiteSetting::where('key', $key)->first();
            
            if ($setting) {
                // Handle file uploads
                if ($setting->type === 'image' && $request->hasFile("settings.{$key}")) {
                    $value = $this->handleImageUpload($request->file("settings.{$key}"), $key);
                } elseif ($setting->type === 'file' && $request->hasFile("settings.{$key}")) {
                    $value = $this->handleFileUpload($request->file("settings.{$key}"), $key);
                }
                
                $setting->update(['value' => $value]);
            }
        }

        // Clear cache
        SiteSetting::clearCache();

        return redirect()->back()->with('success', 'Site settings updated successfully!');
    }

    private function handleImageUpload($file, $key)
    {
        $filename = $key . '_' . time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('uploads/site_settings', $filename, 'public');

        return $path;
    }

    private function handleFileUpload($file, $key)
    {
        $filename = $key . '_' . time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('uploads/site_settings', $filename, 'public');
        
        return $path;
    }

    private function initializeDefaultSettings()
    {
        $defaultSettings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'NitiKotha',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'sort_order' => 1
            ],
            [
                'key' => 'site_title',
                'value' => 'NitiKotha - Latest News & Updates',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Title',
                'description' => 'The title that appears in browser tabs',
                'sort_order' => 2
            ],
            [
                'key' => 'site_description',
                'value' => 'Your trusted source for latest news and updates',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'Site Description',
                'description' => 'A brief description of your website',
                'sort_order' => 3
            ],
            [
                'key' => 'site_keywords',
                'value' => 'news, updates, latest, breaking news',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Keywords',
                'description' => 'SEO keywords separated by commas',
                'sort_order' => 4
            ],
            
            // Appearance Settings
            [
                'key' => 'site_logo',
                'value' => '',
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'Site Logo',
                'description' => 'Upload your site logo (recommended: 200x80px)',
                'sort_order' => 1
            ],
            [
                'key' => 'site_favicon',
                'value' => '',
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'Site Favicon',
                'description' => 'Upload your site favicon (32x32px)',
                'sort_order' => 2
            ],
            
            // Contact Settings
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'label' => 'Contact Email',
                'description' => 'Primary contact email address',
                'sort_order' => 1
            ],
            [
                'key' => 'contact_phone',
                'value' => '01736990123',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Contact Phone',
                'description' => 'Primary contact phone number',
                'sort_order' => 2
            ],
            [
                'key' => 'contact_address',
                'value' => 'Road -15, House -18, sector -12, Uttara Dhaka',
                'type' => 'textarea',
                'group' => 'contact',
                'label' => 'Contact Address',
                'description' => 'Physical address of your organization',
                'sort_order' => 3
            ],
            
            // Social Media Settings
            [
                'key' => 'social_facebook',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Your Facebook page URL',
                'sort_order' => 1
            ],
            [
                'key' => 'social_twitter',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Your Twitter profile URL',
                'sort_order' => 2
            ],
            [
                'key' => 'social_youtube',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'label' => 'YouTube URL',
                'description' => 'Your YouTube channel URL',
                'sort_order' => 3
            ],
            [
                'key' => 'social_instagram',
                'value' => '',
                'type' => 'url',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Your Instagram profile URL',
                'sort_order' => 4
            ]
        ];

        foreach ($defaultSettings as $setting) {
            SiteSetting::firstOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
