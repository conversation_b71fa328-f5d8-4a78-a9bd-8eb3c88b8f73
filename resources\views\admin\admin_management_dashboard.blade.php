<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Management Dashboard | NitiKotha</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
    <meta content="Coderthemes" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    
    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('backend/assets/images/favicon.ico') }}">

    <!-- Local Bootstrap CSS (Required for admin layout) -->
    <link href="{{ asset('backend/assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Local App CSS (Contains admin dashboard styles) -->
    <link href="{{ asset('backend/assets/css/app.min.css') }}" rel="stylesheet" type="text/css" id="app-style"/>

    <!-- Local Icons CSS (Material Design Icons) -->
    <link href="{{ asset('backend/assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- jQuery UI CSS (without integrity hash) -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">

    <!-- Toastr CSS (without integrity hash) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css">

    <!-- Font Awesome (without integrity hash) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Enhanced Post Management Styles -->
    <style>
        /* Post Management Enhancements */
        .post-row {
            transition: all 0.2s ease;
        }

        .post-row:hover {
            background-color: #f8f9fa !important;
        }

        .post-row.table-warning:hover {
            background-color: #fff3cd !important;
        }

        .btn {
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }

        .badge {
            font-size: 0.75rem;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
        }

        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
        }

        /* Loading animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .mdi-loading {
            animation: spin 1s linear infinite;
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .d-flex.gap-1 {
                flex-direction: column;
                gap: 0.25rem !important;
            }

            .btn-sm {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }

        /* Ensure proper spacing and alignment */
        .btn-group .btn {
            margin-right: 0;
        }

        .status-badges .badge {
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
        }

        /* Fix for order input */
        .order-input {
            width: 60px !important;
            min-width: 60px;
        }

        /* Improve table responsiveness */
        .table-responsive {
            border-radius: 8px;
        }

        /* Better button spacing */
        .d-flex.gap-1 > * {
            margin-right: 0.25rem;
        }

        .d-flex.gap-1 > *:last-child {
            margin-right: 0;
        }
    </style>

    <style>
        /* Custom styles for management pages */
        .ui-sortable-helper {
            background-color: #f8f9fa !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
            border: 2px dashed #007bff !important;
        }
        
        .ui-state-highlight {
            height: 60px !important;
            background-color: #e3f2fd !important;
            border: 2px dashed #2196f3 !important;
        }
        
        .drag-handle {
            cursor: move !important;
            opacity: 0.6;
            transition: opacity 0.2s;
        }
        
        .drag-handle:hover {
            opacity: 1;
        }
        
        .sortable-table tbody tr:hover .drag-handle {
            opacity: 1;
        }
        
        .post-row.table-warning {
            background-color: #fff3cd !important;
        }
        
        .category-row.table-secondary {
            opacity: 0.7;
        }
        
        .order-badge {
            min-width: 30px;
            display: inline-block;
            text-align: center;
        }

        /* Sidebar dropdown fixes */
        .nav-second-level {
            padding-left: 20px;
        }

        .nav-second-level li a {
            padding: 8px 15px;
            display: block;
            color: #8391a2;
            text-decoration: none;
        }

        .nav-second-level li a:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.1);
        }

        /* Fix content spacing */
        .content-page {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        .content {
            padding-top: 0 !important;
        }

        /* Sidebar collapse fixes */
        .sidebar-menu .collapse {
            transition: all 0.3s ease;
        }

        .sidebar-menu .collapse.show {
            display: block !important;
        }

        .sidebar-menu .collapse:not(.show) {
            display: none !important;
        }

        .sidebar-menu .menu-arrow {
            transition: transform 0.3s ease;
        }

        .sidebar-menu [aria-expanded="true"] .menu-arrow {
            transform: rotate(90deg);
        }

        /* Sidebar scrolling */
        .left-side-menu {
            height: 100vh;
            overflow: hidden;
        }

        .left-side-menu .h-100 {
            height: 100vh !important;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: #6c757d transparent;
        }

        /* Custom scrollbar for webkit browsers */
        .left-side-menu .h-100::-webkit-scrollbar {
            width: 6px;
        }

        .left-side-menu .h-100::-webkit-scrollbar-track {
            background: transparent;
        }

        .left-side-menu .h-100::-webkit-scrollbar-thumb {
            background-color: #6c757d;
            border-radius: 3px;
            opacity: 0.5;
        }

        .left-side-menu .h-100::-webkit-scrollbar-thumb:hover {
            background-color: #495057;
            opacity: 0.8;
        }

        /* Sidebar menu spacing - REDUCED VERTICAL SPACING */
        #sidebar-menu {
            padding-bottom: 30px;
        }

        .sidebar-menu li {
            margin-bottom: 0px; /* Reduced from 2px to 0px */
        }

        .sidebar-menu .menu-title {
            padding: 10px 20px 5px; /* Reduced padding */
            font-size: 11px;
            font-weight: 600;
            color: #8391a2;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 8px; /* Reduced from 10px */
        }

        .sidebar-menu .menu-title:first-child {
            border-top: none;
            margin-top: 0;
        }

        /* Main menu items - REDUCED PADDING */
        .sidebar-menu li > a {
            padding: 8px 20px; /* Reduced from default padding */
            display: flex;
            align-items: center;
            color: #8391a2;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 0;
        }

        /* Submenu items - REDUCED PADDING */
        .sidebar-menu .nav-second-level li > a {
            padding: 6px 20px 6px 45px; /* Reduced padding for submenu items */
            font-size: 13px;
            color: #8391a2;
            transition: all 0.2s ease;
        }

        /* Icon styling */
        .sidebar-menu i {
            width: 20px;
            text-align: center;
            margin-right: 10px;
            font-size: 16px;
            color: #8391a2;
        }

        /* IMPROVED HOVER EFFECTS */
        .sidebar-menu li a:hover {
            background-color: rgba(255,255,255,0.15); /* Slightly more visible */
            color: #fff !important;
            transform: translateX(2px); /* Subtle slide effect */
        }

        .sidebar-menu li a:hover i {
            color: #fff !important;
        }

        /* Submenu hover effects */
        .sidebar-menu .nav-second-level li a:hover {
            background-color: rgba(255,255,255,0.12);
            color: #fff !important;
            padding-left: 48px; /* Slight indent on hover */
        }

        /* Active menu item */
        .sidebar-menu li.active > a {
            background-color: rgba(255,255,255,0.2);
            color: #fff !important;
        }

        .sidebar-menu li.active > a i {
            color: #fff !important;
        }

        /* ARROW ICON IMPROVEMENTS */
        .sidebar-menu .menu-arrow {
            margin-left: auto;
            font-size: 12px;
            transition: transform 0.3s ease;
            color: #8391a2;
        }

        .sidebar-menu [aria-expanded="true"] .menu-arrow {
            transform: rotate(90deg);
            color: #fff;
        }

        /* Submenu container */
        .sidebar-menu .nav-second-level {
            padding-left: 0; /* Remove extra padding */
            background-color: rgba(0,0,0,0.1);
        }

        /* Reduce spacing between submenu items */
        .sidebar-menu .nav-second-level li {
            margin-bottom: 0;
        }

        /* Post Management Enhancements */
        .sortable-table tbody tr {
            transition: all 0.3s ease;
        }

        .sortable-table tbody tr.dragging {
            opacity: 0.8;
            transform: scale(1.02);
        }

        .ui-sortable-helper {
            background-color: #f8f9fa !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            border: 1px solid #dee2e6 !important;
            z-index: 1000 !important;
        }

        .ui-state-highlight {
            height: 60px;
            background-color: #e3f2fd;
            border: 2px dashed #2196f3;
            border-radius: 4px;
        }

        .drag-handle {
            cursor: move;
            color: #6c757d;
            transition: color 0.2s ease;
        }

        .drag-handle:hover {
            color: #495057;
        }

        /* Search results styling */
        #searchResults {
            margin-top: 5px;
            font-size: 0.875rem;
        }

        /* Action button loading state */
        .btn .mdi-loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Search icon positioning */
        .search-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
    </style>
</head>

<body class="loading" data-layout-color="light" data-leftbar-theme="dark" data-layout-mode="fluid" data-rightbar-onstart="true">
    <!-- Begin page -->
    <div class="wrapper">
        
        <!-- ========== Left Sidebar Start ========== -->
        @include('admin.body.sidebar')
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <div class="content">
                <!-- Topbar Start -->
                @include('admin.body.header')
                <!-- end Topbar -->

                <!-- Start Content-->
                @yield('admin')
                <!-- container -->

            </div>
            <!-- content -->

            <!-- Footer Start -->
            @include('admin.body.footer')
            <!-- end Footer -->

        </div>

        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <!-- jQuery (latest stable version - load first) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap Bundle JS (without integrity hash) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery UI (for enhanced interactions) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <!-- Toastr JS (notifications) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>

    <!-- Local App JS (load after jQuery) -->
    <script>
        // Check if Waves is defined, if not, define a dummy to prevent errors
        if (typeof Waves === 'undefined') {
            window.Waves = {
                init: function() {},
                attach: function() {},
                ripple: function() {}
            };
        }
    </script>
    <script src="{{ asset('backend/assets/js/app.min.js') }}"></script>

    <!-- Initialize Libraries and Global Settings -->
    <script>
        // Ensure jQuery is loaded before proceeding
        function initializeAdminDashboard() {
            if (typeof $ === 'undefined') {
                console.error('jQuery is not loaded!');
                setTimeout(initializeAdminDashboard, 100);
                return;
            }

            $(document).ready(function() {
                console.log('=== Admin Dashboard Libraries Check ===');
                console.log('jQuery version:', $.fn.jquery);
                console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
                console.log('jQuery UI available:', typeof $.ui !== 'undefined');
                console.log('Toastr available:', typeof toastr !== 'undefined');
                console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content') ? 'Found' : 'Missing');

                // Configure Toastr
                if (typeof toastr !== 'undefined') {
                    toastr.options = {
                        "closeButton": true,
                        "debug": false,
                        "newestOnTop": false,
                        "progressBar": true,
                        "positionClass": "toast-top-right",
                        "preventDuplicates": false,
                        "onclick": null,
                        "showDuration": "300",
                        "hideDuration": "1000",
                        "timeOut": "5000",
                        "extendedTimeOut": "1000",
                        "showEasing": "swing",
                        "hideEasing": "linear",
                        "showMethod": "fadeIn",
                        "hideMethod": "fadeOut"
                    };
                    console.log('Toastr configured successfully');
                }

                // Global AJAX setup
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                console.log('Global AJAX setup complete');
                console.log('=== Libraries Check Complete ===');
            });
        }

        // Initialize when page loads
        initializeAdminDashboard();

        // Session messages
        @if(Session::has('message'))
        var type = "{{ Session::get('alert-type','info') }}";
        switch(type){
            case 'info':
                toastr.info("{{ Session::get('message') }}");
                break;
            case 'success':
                toastr.success("{{ Session::get('message') }}");
                break;
            case 'warning':
                toastr.warning("{{ Session::get('message') }}");
                break;
            case 'error':
                toastr.error("{{ Session::get('message') }}");
                break; 
        }
        @endif
    </script>

    <!-- Bootstrap Bundle JS (includes Popper) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- App JS -->
    <script>
        $(document).ready(function() {
            console.log('Management Dashboard Ready');
            console.log('jQuery version:', $.fn.jquery);
            console.log('jQuery UI available:', typeof $.ui !== 'undefined');
            console.log('Bootstrap available:', typeof bootstrap !== 'undefined');

            // Initialize sidebar toggle
            $('[data-toggle="sidebar"]').click(function() {
                $('body').toggleClass('sidebar-enable');
            });

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Initialize popovers
            $('[data-bs-toggle="popover"]').popover();

            // Initialize sidebar collapse manually
            $('[data-bs-toggle="collapse"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this).attr('href');
                var $target = $(target);
                var $this = $(this);

                console.log('Collapse clicked:', target);

                if ($target.length) {
                    // Close other open collapses in sidebar
                    $('.sidebar-menu .collapse.show').not($target).removeClass('show');
                    $('.sidebar-menu [aria-expanded="true"]').not($this).attr('aria-expanded', 'false');

                    // Toggle current collapse
                    if ($target.hasClass('show')) {
                        $target.removeClass('show');
                        $this.attr('aria-expanded', 'false');
                    } else {
                        $target.addClass('show');
                        $this.attr('aria-expanded', 'true');
                    }
                }
            });

            // Also handle Bootstrap's native collapse events
            $('.collapse').on('show.bs.collapse', function () {
                var $trigger = $('[href="#' + this.id + '"]');
                $trigger.attr('aria-expanded', 'true');
            });

            $('.collapse').on('hide.bs.collapse', function () {
                var $trigger = $('[href="#' + this.id + '"]');
                $trigger.attr('aria-expanded', 'false');
            });

            // Initialize header dropdowns
            $('.dropdown-toggle').dropdown();

            // Debug logging
            console.log('Sidebar menu items found:', $('.sidebar-menu [data-bs-toggle="collapse"]').length);
            console.log('Header dropdowns found:', $('.dropdown-toggle').length);
        });
    </script>

</body>
</html>
