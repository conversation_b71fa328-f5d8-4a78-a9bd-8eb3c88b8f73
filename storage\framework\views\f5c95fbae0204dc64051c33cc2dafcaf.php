
<?php if($ad->isCurrentlyActive()): ?>
<div class="fb-advertisement fb-advertisement-<?php echo e($position); ?>" data-ad-id="<?php echo e($ad->id); ?>">
    <div class="fb-ad-header">
        <span class="fb-ad-label">
            <i class="fas fa-bullhorn"></i>
            Advertisement
        </span>
        <span class="fb-ad-type-badge">
            <?php echo e(ucfirst($ad->ad_type)); ?>

        </span>
    </div>

    <?php if($ad->ad_type === 'banner'): ?>
        
        <div class="fb-banner-ad">
            <?php if($ad->link_url): ?>
                <a href="<?php echo e($ad->link_url); ?>" target="_blank" onclick="trackRegularAdClick(<?php echo e($ad->id); ?>)">
            <?php endif; ?>
            
            <?php if($ad->image): ?>
                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-banner-image">
            <?php else: ?>
                <div class="fb-banner-text">
                    <h4><?php echo e($ad->title); ?></h4>
                    <?php if($ad->description): ?>
                        <p><?php echo e($ad->description); ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if($ad->link_url): ?>
                </a>
            <?php endif; ?>
        </div>

    <?php elseif($ad->ad_type === 'sidebar'): ?>
        
        <div class="fb-sidebar-ad">
            <div class="fb-sidebar-content">
                <h5><?php echo e($ad->title); ?></h5>
                <?php if($ad->description): ?>
                    <p><?php echo e(Str::limit($ad->description, 100)); ?></p>
                <?php endif; ?>
                
                <?php if($ad->image): ?>
                    <div class="fb-sidebar-image">
                        <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>">
                    </div>
                <?php endif; ?>
                
                <?php if($ad->link_url): ?>
                    <a href="<?php echo e($ad->link_url); ?>" target="_blank" class="fb-sidebar-cta" onclick="trackRegularAdClick(<?php echo e($ad->id); ?>)">
                        Learn More
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif($ad->ad_type === 'inline'): ?>
        
        <div class="fb-inline-ad">
            <div class="fb-inline-header">
                <h5><?php echo e($ad->title); ?></h5>
            </div>
            
            <?php if($ad->image): ?>
                <div class="fb-inline-image">
                    <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>">
                </div>
            <?php endif; ?>
            
            <div class="fb-inline-content">
                <?php if($ad->description): ?>
                    <p><?php echo e($ad->description); ?></p>
                <?php endif; ?>
                
                <?php if($ad->link_url): ?>
                    <a href="<?php echo e($ad->link_url); ?>" target="_blank" class="fb-inline-cta" onclick="trackRegularAdClick(<?php echo e($ad->id); ?>)">
                        Click Here
                        <i class="fas fa-arrow-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif($ad->ad_type === 'popup'): ?>
        
        <div class="fb-popup-ad" id="popup-ad-<?php echo e($ad->id); ?>" style="display: none;">
            <div class="fb-popup-overlay">
                <div class="fb-popup-content">
                    <button class="fb-popup-close" onclick="closePopupAd(<?php echo e($ad->id); ?>)">
                        <i class="fas fa-times"></i>
                    </button>
                    
                    <div class="fb-popup-body">
                        <h4><?php echo e($ad->title); ?></h4>
                        
                        <?php if($ad->image): ?>
                            <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-popup-image">
                        <?php endif; ?>
                        
                        <?php if($ad->description): ?>
                            <p><?php echo e($ad->description); ?></p>
                        <?php endif; ?>
                        
                        <?php if($ad->link_url): ?>
                            <a href="<?php echo e($ad->link_url); ?>" target="_blank" class="fb-popup-cta" onclick="trackRegularAdClick(<?php echo e($ad->id); ?>)">
                                Get Started
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    
    <?php if(config('app.debug')): ?>
        <div class="fb-ad-debug">
            <small class="text-muted">
                Views: <?php echo e($ad->view_count ?? 0); ?> | Clicks: <?php echo e($ad->click_count ?? 0); ?> | Order: <?php echo e($ad->display_order); ?>

            </small>
        </div>
    <?php endif; ?>

    
    <div class="fb-ad-actions">
        <button class="fb-ad-action" onclick="hideRegularAd(<?php echo e($ad->id); ?>)">
            <i class="fas fa-times"></i>
            Hide Ad
        </button>
        <button class="fb-ad-action" onclick="reportRegularAd(<?php echo e($ad->id); ?>)">
            <i class="fas fa-flag"></i>
            Report
        </button>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track ad view when it comes into viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                trackRegularAdView(<?php echo e($ad->id); ?>);
                observer.unobserve(entry.target);
            }
        });
    });
    
    const adElement = document.querySelector('[data-ad-id="<?php echo e($ad->id); ?>"]');
    if (adElement) {
        observer.observe(adElement);
    }

    // Show popup ads after delay (if popup type)
    <?php if($ad->ad_type === 'popup'): ?>
        setTimeout(() => {
            document.getElementById('popup-ad-<?php echo e($ad->id); ?>').style.display = 'block';
        }, 5000); // Show after 5 seconds
    <?php endif; ?>
});
</script>
<?php endif; ?>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/partials/advertisement.blade.php ENDPATH**/ ?>