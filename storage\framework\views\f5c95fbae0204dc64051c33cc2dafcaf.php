
<?php if($ad && $ad->is_active): ?>
<div class="fb-advertisement-container" data-ad-id="<?php echo e($ad->id); ?>" data-ad-type="regular" data-position="<?php echo e($position ?? 'default'); ?>">
    <div class="fb-advertisement fb-ad-<?php echo e($ad->ad_type); ?> fb-ad-position-<?php echo e($position ?? 'default'); ?>">
        <!-- Advertisement Header -->
        <div class="fb-ad-header">
            <div class="fb-ad-label">
                <i class="fas fa-bullhorn"></i>
                <span>Sponsored</span>
            </div>
            <div class="fb-ad-actions">
                <button class="fb-ad-action" data-action="hide" data-ad-id="<?php echo e($ad->id); ?>" title="Hide this ad">
                    <i class="fas fa-times"></i>
                </button>
                <button class="fb-ad-action" data-action="report" data-ad-id="<?php echo e($ad->id); ?>" title="Report ad">
                    <i class="fas fa-flag"></i>
                </button>
            </div>
        </div>

        <!-- Advertisement Content -->
        <div class="fb-ad-content">
            <?php if($ad->ad_type === 'banner'): ?>
                <!-- Banner Advertisement -->
                <div class="fb-ad-banner">
                    <?php if($ad->image): ?>
                        <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-ad-image">
                    <?php endif; ?>
                    <div class="fb-ad-text">
                        <h4 class="fb-ad-title"><?php echo e($ad->title); ?></h4>
                        <?php if($ad->description): ?>
                            <p class="fb-ad-description"><?php echo e(Str::limit($ad->description, 100)); ?></p>
                        <?php endif; ?>
                        <?php if($ad->link_url): ?>
                            <a href="<?php echo e($ad->link_url); ?>" class="fb-ad-link" target="_blank" rel="noopener" data-ad-id="<?php echo e($ad->id); ?>">
                                Learn More
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

            <?php elseif($ad->ad_type === 'sidebar'): ?>
                <!-- Sidebar Advertisement -->
                <div class="fb-ad-sidebar">
                    <?php if($ad->image): ?>
                        <div class="fb-ad-image-container">
                            <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-ad-image">
                        </div>
                    <?php endif; ?>
                    <div class="fb-ad-content-text">
                        <h5 class="fb-ad-title"><?php echo e($ad->title); ?></h5>
                        <?php if($ad->description): ?>
                            <p class="fb-ad-description"><?php echo e(Str::limit($ad->description, 80)); ?></p>
                        <?php endif; ?>
                        <?php if($ad->link_url): ?>
                            <a href="<?php echo e($ad->link_url); ?>" class="fb-ad-cta" target="_blank" rel="noopener" data-ad-id="<?php echo e($ad->id); ?>">
                                View Details
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

            <?php elseif($ad->ad_type === 'inline'): ?>
                <!-- Inline Advertisement -->
                <div class="fb-ad-inline">
                    <div class="fb-ad-inline-content">
                        <?php if($ad->image): ?>
                            <div class="fb-ad-image-wrapper">
                                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-ad-image">
                            </div>
                        <?php endif; ?>
                        <div class="fb-ad-text-wrapper">
                            <h4 class="fb-ad-title"><?php echo e($ad->title); ?></h4>
                            <?php if($ad->description): ?>
                                <p class="fb-ad-description"><?php echo e(Str::limit($ad->description, 120)); ?></p>
                            <?php endif; ?>
                            <?php if($ad->link_url): ?>
                                <a href="<?php echo e($ad->link_url); ?>" class="fb-ad-button" target="_blank" rel="noopener" data-ad-id="<?php echo e($ad->id); ?>">
                                    <i class="fas fa-arrow-right"></i>
                                    Learn More
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

            <?php elseif($ad->ad_type === 'popup'): ?>
                <!-- Popup Advertisement (Modal) -->
                <div class="fb-ad-popup-trigger" data-ad-id="<?php echo e($ad->id); ?>">
                    <div class="fb-ad-popup-preview">
                        <?php if($ad->image): ?>
                            <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" class="fb-ad-preview-image">
                        <?php endif; ?>
                        <div class="fb-ad-preview-text">
                            <h5 class="fb-ad-title"><?php echo e($ad->title); ?></h5>
                            <p class="fb-ad-description"><?php echo e(Str::limit($ad->description, 60)); ?></p>
                            <button class="fb-ad-popup-btn">View Offer</button>
                        </div>
                    </div>
                </div>

            <?php endif; ?>
        </div>

        
    </div>
</div>

<!-- Popup Modal for Popup Ads -->
<?php if($ad->ad_type === 'popup'): ?>
<div class="fb-ad-modal" id="ad-modal-<?php echo e($ad->id); ?>" style="display: none;">
    <div class="fb-ad-modal-overlay" data-close-modal="<?php echo e($ad->id); ?>"></div>
    <div class="fb-ad-modal-content">
        <button class="fb-ad-modal-close" data-close-modal="<?php echo e($ad->id); ?>">
            <i class="fas fa-times"></i>
        </button>
        <?php if($ad->image): ?>
            <div class="fb-ad-modal-image">
                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>">
            </div>
        <?php endif; ?>
        <div class="fb-ad-modal-text">
            <h3 class="fb-ad-modal-title"><?php echo e($ad->title); ?></h3>
            <?php if($ad->description): ?>
                <p class="fb-ad-modal-description"><?php echo e($ad->description); ?></p>
            <?php endif; ?>
            <?php if($ad->link_url): ?>
                <a href="<?php echo e($ad->link_url); ?>" class="fb-ad-modal-cta" target="_blank" rel="noopener" data-ad-id="<?php echo e($ad->id); ?>">
                    <i class="fas fa-external-link-alt"></i>
                    Visit Now
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Advertisement interaction tracking
(function() {
    var adId = <?php echo e($ad->id); ?>;
    var adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="regular"]');

    console.log('=== Regular Ad Tracking Initialization ===');
    console.log('Ad ID:', adId);
    console.log('Container found:', adContainer.length > 0);

    // Ensure jQuery is available
    function initializeAdTracking() {
        if (typeof $ === 'undefined') {
            console.warn('jQuery not available for ad tracking, retrying...');
            setTimeout(initializeAdTracking, 500);
            return;
        }

        console.log('Initializing tracking for regular ad:', adId);

        // Re-find container after jQuery is available
        adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="regular"]');
        console.log('Container found after jQuery load:', adContainer.length > 0);

        // Track advertisement view using Intersection Observer for accurate tracking
        if (typeof IntersectionObserver !== 'undefined') {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        console.log('Ad came into view, tracking view for ad:', adId);
                        trackAdView(adId);
                        observer.unobserve(entry.target); // Track view only once
                    }
                });
            }, { threshold: 0.5 });

            if (adContainer.length > 0) {
                observer.observe(adContainer[0]);
                console.log('Intersection observer set up for ad:', adId);
            } else {
                console.warn('Container not found for intersection observer, ad:', adId);
                // Fallback: track view immediately
                setTimeout(function() { trackAdView(adId); }, 1000);
            }
        } else {
            // Fallback for older browsers
            console.log('IntersectionObserver not available, tracking view immediately for ad:', adId);
            trackAdView(adId);
        }

        setupClickTracking();
    }

    function setupClickTracking() {
        // Track advertisement clicks
        $(document).on('click', '[data-ad-id="' + adId + '"][data-ad-type="regular"] a[href]', function(e) {
            console.log('Regular ad click detected for ad:', adId);
            trackAdClick(adId);
        });

        // Handle ad actions (hide, report)
        $(document).on('click', '[data-ad-id="' + adId + '"][data-ad-type="regular"] .fb-ad-action', function(e) {
            e.preventDefault();
            var action = $(this).data('action');

            console.log('Ad action:', action, 'for ad:', adId);

            if (action === 'hide') {
                hideAdvertisement(adId);
            } else if (action === 'report') {
                reportAdvertisement(adId);
            }
        });

        // Handle popup ads
        <?php if($ad->ad_type === 'popup'): ?>
        $(document).on('click', '.fb-ad-popup-trigger[data-ad-id="' + adId + '"]', function() {
            console.log('Popup ad triggered for ad:', adId);
            $('#ad-modal-' + adId).fadeIn(300);
            trackAdClick(adId);
        });

        $(document).on('click', '[data-close-modal="' + adId + '"]', function() {
            $('#ad-modal-' + adId).fadeOut(300);
        });
        <?php endif; ?>

        console.log('Click tracking set up for ad:', adId);
    }

    // Initialize when DOM is ready or jQuery is available
    if (typeof $ !== 'undefined') {
        $(document).ready(initializeAdTracking);
    } else {
        // Wait for jQuery to load
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdTracking();
        });
    }

function trackAdView(adId) {
    console.log('=== Tracking View for Regular Ad ===');
    console.log('Ad ID:', adId);
    console.log('CSRF Token:', '<?php echo e(csrf_token()); ?>');

    if (typeof $ === 'undefined') {
        console.error('jQuery not available for view tracking');
        return;
    }

    $.ajax({
        url: '/api/regular-ads/track-view',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        beforeSend: function() {
            console.log('Sending view tracking request for ad:', adId);
        },
        success: function(response) {
            console.log('✅ View tracked successfully for ad:', adId);
            console.log('Response:', response);
        },
        error: function(xhr, status, error) {
            console.error('❌ Failed to track view for ad:', adId);
            console.error('Status:', status);
            console.error('Error:', error);
            console.error('Response:', xhr.responseText);
        }
    });
}

function trackAdClick(adId) {
    console.log('=== Tracking Click for Regular Ad ===');
    console.log('Ad ID:', adId);
    console.log('CSRF Token:', '<?php echo e(csrf_token()); ?>');

    if (typeof $ === 'undefined') {
        console.error('jQuery not available for click tracking');
        return;
    }

    $.ajax({
        url: '/api/regular-ads/track-click',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        beforeSend: function() {
            console.log('Sending click tracking request for ad:', adId);
        },
        success: function(response) {
            console.log('✅ Click tracked successfully for ad:', adId);
            console.log('Response:', response);
        },
        error: function(xhr, status, error) {
            console.error('❌ Failed to track click for ad:', adId);
            console.error('Status:', status);
            console.error('Error:', error);
            console.error('Response:', xhr.responseText);
        }
    });
}

function hideAdvertisement(adId) {
    $('[data-ad-id="' + adId + '"]').fadeOut(300);

    $.ajax({
        url: '/api/regular-ads/track-hide',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            console.log('Hide tracked successfully for ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track hide for ad:', adId, error);
        }
    });
}

function reportAdvertisement(adId) {
    if (confirm('Report this advertisement as inappropriate?')) {
        $.ajax({
            url: '/api/regular-ads/report',
            method: 'POST',
            data: {
                ad_id: adId,
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                console.log('Report tracked successfully for ad:', adId);
                alert('Advertisement reported successfully.');
                $('[data-ad-id="' + adId + '"]').fadeOut(300);
            },
            error: function(xhr, status, error) {
                console.error('Failed to track report for ad:', adId, error);
                alert('Failed to report advertisement. Please try again.');
            }
        });
    }
})(); // End of IIFE
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/partials/advertisement.blade.php ENDPATH**/ ?>