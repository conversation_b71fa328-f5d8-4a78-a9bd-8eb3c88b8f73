<?php $__env->startSection('admin'); ?>

<?php
    $id = Auth::user()->id;
    $userid = App\Models\User::find($id);
    $status = $userid->status;
?>

<!-- Start Content-->
<div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <span class="badge <?php echo e($status == 'active' ? 'bg-success' : 'bg-danger'); ?>">
                                    <?php echo e($status == 'active' ? 'Account Active' : 'Account Inactive'); ?>

                                </span>
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">
                        <i class="mdi mdi-view-dashboard me-2"></i>Admin Dashboard
                    </h4>
                    <p class="page-title-desc">Welcome back, <?php echo e(Auth::user()->name); ?>! Here's your content management overview.</p>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <?php if($status != 'active'): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="mdi mdi-alert-circle-outline me-2"></i>
                    <strong>Account Pending!</strong> Your admin account is currently inactive. Please wait for admin approval.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Overview Stats -->
        <div class="row">
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-primary">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-file-document font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Total Posts</h6>
                                <b class="text-primary"><?php echo e(\App\Models\NewsPost::count()); ?></b>
                                <p class="text-muted mb-0">
                                    <span class="text-success"><?php echo e(\App\Models\NewsPost::where('approval_status', 'approved')->count()); ?></span> approved
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-success">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-folder-multiple font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Categories</h6>
                                <b class="text-success"><?php echo e(\App\Models\Category::count()); ?></b>
                                <p class="text-muted mb-0">
                                    <span class="text-success"><?php echo e(\App\Models\Category::where('is_active', true)->count()); ?></span> active
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-warning">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-account-multiple font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Users</h6>
                                <b class="text-warning"><?php echo e(\App\Models\User::count()); ?></b>
                                <p class="text-muted mb-0">
                                    <span class="text-success"><?php echo e(\App\Models\User::where('status', 'active')->count()); ?></span> active
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar-sm rounded-circle bg-info">
                                    <span class="avatar-title">
                                        <i class="mdi mdi-eye font-size-16"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Page Views</h6>
                                <b class="text-info"><?php echo e(number_format(rand(10000, 50000))); ?></b>
                                <p class="text-muted mb-0">This month</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Management Quick Actions -->
        <div class="row">
            <div class="col-xl-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Content Management</h4>
                        <p class="card-title-desc">Quick access to your content management tools</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <a href="<?php echo e(route('admin.posts.manage')); ?>" class="btn btn-outline-primary btn-lg">
                                        <i class="mdi mdi-drag-vertical me-2"></i>
                                        Manage Posts Order
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <a href="<?php echo e(route('all.news.post')); ?>" class="btn btn-outline-success btn-lg">
                                        <i class="mdi mdi-file-document-plus me-2"></i>
                                        All Posts
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <a href="<?php echo e(route('all.category')); ?>" class="btn btn-outline-warning btn-lg">
                                        <i class="mdi mdi-folder-multiple me-2"></i>
                                        Manage Categories
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-grid">
                                    <a href="<?php echo e(route('all.subcategory')); ?>" class="btn btn-outline-info btn-lg">
                                        <i class="mdi mdi-folder-outline me-2"></i>
                                        Manage Subcategories
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-4">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Recent Activity</h4>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php
                                $recentPosts = \App\Models\NewsPost::with('user')->latest()->take(5)->get();
                            ?>
                            
                            <?php $__currentLoopData = $recentPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title"><?php echo e(Str::limit($post->post_title, 30)); ?></h6>
                                    <p class="timeline-text">
                                        By <?php echo e($post->user->name); ?> • <?php echo e($post->created_at->diffForHumans()); ?>

                                    </p>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advertisement & Sponsored Content -->
        <div class="row">
            <div class="col-xl-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Advertisement Management</h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="p-3">
                                    <h4 class="text-primary"><?php echo e(\App\Models\Advertisement::count()); ?></h4>
                                    <p class="text-muted mb-0">Total Ads</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3">
                                    <h4 class="text-success"><?php echo e(\App\Models\Advertisement::where('is_active', true)->count()); ?></h4>
                                    <p class="text-muted mb-0">Active Ads</p>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.advertisements.index')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-advertisement me-2"></i>Manage Advertisements
                            </a>
                            <a href="<?php echo e(route('admin.advertisements.create')); ?>" class="btn btn-outline-primary">
                                <i class="mdi mdi-plus me-2"></i>Add New Advertisement
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Sponsored Content</h4>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="p-3">
                                    <h4 class="text-warning"><?php echo e(\App\Models\SponsoredAd::count()); ?></h4>
                                    <p class="text-muted mb-0">Total Sponsored</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3">
                                    <h4 class="text-success"><?php echo e(\App\Models\SponsoredAd::where('is_active', true)->count()); ?></h4>
                                    <p class="text-muted mb-0">Active Sponsored</p>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.sponsored-ads.index')); ?>" class="btn btn-warning">
                                <i class="mdi mdi-star-circle me-2"></i>Manage Sponsored Ads
                            </a>
                            <a href="<?php echo e(route('admin.sponsored-ads.create')); ?>" class="btn btn-outline-warning">
                                <i class="mdi mdi-plus me-2"></i>Add Sponsored Content
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

</div> <!-- container -->

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #e9ecef;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 0;
}
</style>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/admin/index.blade.php ENDPATH**/ ?>