<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.advertisements.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to List
                            </a>
                            <a href="<?php echo e(route('admin.advertisements.edit', $advertisement->id)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                            <form method="POST" action="<?php echo e(route('admin.advertisements.toggle-status', $advertisement->id)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn <?php echo e($advertisement->is_active ? 'btn-secondary' : 'btn-success'); ?>">
                                    <i class="mdi <?php echo e($advertisement->is_active ? 'mdi-pause' : 'mdi-play'); ?>"></i>
                                    <?php echo e($advertisement->is_active ? 'Deactivate' : 'Activate'); ?>

                                </button>
                            </form>
                        </ol>
                    </div>
                    <h4 class="page-title">Advertisement Details</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <div class="row">
            <!-- Advertisement Info -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><?php echo e($advertisement->title); ?></h5>
                        <div class="d-flex gap-2 mt-2">
                            <span class="badge bg-<?php echo e($advertisement->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($advertisement->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                            <span class="badge bg-primary"><?php echo e(ucfirst($advertisement->ad_type)); ?></span>
                            <span class="badge bg-info"><?php echo e(ucfirst($advertisement->position)); ?></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if($advertisement->image): ?>
                            <div class="mb-4">
                                <img src="<?php echo e(asset($advertisement->image)); ?>" alt="<?php echo e($advertisement->title); ?>" 
                                     class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                        <?php endif; ?>

                        <?php if($advertisement->description): ?>
                            <div class="mb-3">
                                <h6>Description:</h6>
                                <p class="text-muted"><?php echo e($advertisement->description); ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if($advertisement->link_url): ?>
                            <div class="mb-3">
                                <h6>Link URL:</h6>
                                <a href="<?php echo e($advertisement->link_url); ?>" target="_blank" class="text-primary">
                                    <?php echo e($advertisement->link_url); ?> <i class="mdi mdi-open-in-new"></i>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Advertisement Type:</h6>
                                <p class="text-muted"><?php echo e(ucfirst($advertisement->ad_type)); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Position:</h6>
                                <p class="text-muted"><?php echo e(ucfirst($advertisement->position)); ?></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Display Order:</h6>
                                <p class="text-muted"><?php echo e($advertisement->display_order); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Status:</h6>
                                <span class="badge bg-<?php echo e($advertisement->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($advertisement->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                        </div>

                        <?php if($advertisement->start_date || $advertisement->end_date): ?>
                            <div class="row">
                                <?php if($advertisement->start_date): ?>
                                <div class="col-md-6">
                                    <h6>Start Date:</h6>
                                    <p class="text-muted"><?php echo e($advertisement->start_date->format('F d, Y')); ?></p>
                                </div>
                                <?php endif; ?>
                                <?php if($advertisement->end_date): ?>
                                <div class="col-md-6">
                                    <h6>End Date:</h6>
                                    <p class="text-muted"><?php echo e($advertisement->end_date->format('F d, Y')); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if($advertisement->target_pages && count($advertisement->target_pages) > 0): ?>
                            <div class="mb-3">
                                <h6>Target Pages:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $advertisement->target_pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark"><?php echo e(ucfirst(str_replace('_', ' ', $page))); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($advertisement->target_categories && count($advertisement->target_categories) > 0): ?>
                            <div class="mb-3">
                                <h6>Target Categories:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $advertisement->target_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $category = \App\Models\Category::find($categoryId);
                                        ?>
                                        <?php if($category): ?>
                                            <span class="badge bg-light text-dark"><?php echo e($category->category_name); ?></span>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics & Info -->
            <div class="col-md-4">
                <!-- Performance Stats -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Performance Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e(number_format($advertisement->view_count)); ?></h4>
                                <small class="text-muted">Total Views</small>
                            </div>
                            <div class="text-primary">
                                <i class="mdi mdi-eye" style="font-size: 2rem;"></i>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e(number_format($advertisement->click_count)); ?></h4>
                                <small class="text-muted">Total Clicks</small>
                            </div>
                            <div class="text-success">
                                <i class="mdi mdi-cursor-pointer" style="font-size: 2rem;"></i>
                            </div>
                        </div>

                        <?php if($advertisement->view_count > 0): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e(round(($advertisement->click_count / $advertisement->view_count) * 100, 2)); ?>%</h4>
                                <small class="text-muted">Click-Through Rate</small>
                            </div>
                            <div class="text-warning">
                                <i class="mdi mdi-chart-line" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="progress mb-2">
                            <?php
                                $ctr = $advertisement->view_count > 0 ? ($advertisement->click_count / $advertisement->view_count) * 100 : 0;
                                $progressColor = $ctr > 5 ? 'success' : ($ctr > 2 ? 'warning' : 'danger');
                            ?>
                            <div class="progress-bar bg-<?php echo e($progressColor); ?>" role="progressbar" style="width: <?php echo e(min($ctr * 10, 100)); ?>%"></div>
                        </div>
                        <small class="text-muted">Performance indicator</small>
                    </div>
                </div>

                <!-- Advertisement Info -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Advertisement Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-calendar me-2 text-muted"></i>
                            <span>Created <?php echo e($advertisement->created_at->format('F d, Y')); ?></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-clock me-2 text-muted"></i>
                            <span>Last updated <?php echo e($advertisement->updated_at->diffForHumans()); ?></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="mdi mdi-identifier me-2 text-muted"></i>
                            <span>ID: <?php echo e($advertisement->id); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.advertisements.edit', $advertisement->id)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit Advertisement
                            </a>
                            
                            <form method="POST" action="<?php echo e(route('admin.advertisements.toggle-status', $advertisement->id)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn <?php echo e($advertisement->is_active ? 'btn-secondary' : 'btn-success'); ?> w-100">
                                    <i class="mdi <?php echo e($advertisement->is_active ? 'mdi-pause' : 'mdi-play'); ?>"></i>
                                    <?php echo e($advertisement->is_active ? 'Deactivate' : 'Activate'); ?>

                                </button>
                            </form>

                            <form method="POST" action="<?php echo e(route('admin.advertisements.destroy', $advertisement->id)); ?>" 
                                  onsubmit="return confirm('Are you sure you want to delete this advertisement?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="mdi mdi-delete"></i> Delete Advertisement
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/advertisements/show.blade.php ENDPATH**/ ?>