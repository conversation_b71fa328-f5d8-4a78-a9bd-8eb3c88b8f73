
<?php $__env->startSection('admin'); ?>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">
                        
                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            
                                            <li class="breadcrumb-item active">Add Category</li>
                                        </ol>
                                    </div>
                                    <h4 class="page-title">Add Category</h4>
                                </div>
                            </div>
                        </div>     
                        <!-- end page title --> 
  
                        <!-- Form row -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                         
    <form id="myForm" method="post" action="<?php echo e(route('category.store')); ?>">
    	<?php echo csrf_field(); ?> 
    	
        <div class="row">
            <div class="form-group col-md-6 mb-3">
                <label for="inputEmail4" class="form-label">Category Name </label>
                <input type="text" name="category_name" class="form-control" id="inputEmail4" placeholder="Add Category">
            </div>

            <div class="form-group col-md-6 mb-3">
                <label for="categoryIcon" class="form-label">Category Icon</label>
                <div class="input-group">
                    <input type="text" name="icon" class="form-control" id="categoryIcon" placeholder="Select an icon" readonly>
                    <button class="btn btn-outline-secondary" type="button" id="iconPickerBtn">
                        <i class="mdi mdi-emoticon-outline"></i> Choose Icon
                    </button>
                </div>
                <small class="text-muted">Leave empty to use automatic icon detection</small>
            </div>
        </div>

        <!-- Icon Picker Modal -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card" id="iconPickerCard" style="display: none;">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Choose Category Icon</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <h6>Popular Category Icons:</h6>
                                <div class="icon-grid">
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="💻" title="Technology">💻</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🏛️" title="Politics">🏛️</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="⚽" title="Sports">⚽</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🎬" title="Entertainment">🎬</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="💼" title="Business">💼</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🏥" title="Health">🏥</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🔬" title="Science">🔬</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🌍" title="World">🌍</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🇧🇩" title="National">🇧🇩</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="💭" title="Opinion">💭</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🤖" title="AI">🤖</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🗳️" title="Election">🗳️</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="📊" title="Economics">📊</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🎓" title="Education">🎓</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="✈️" title="Travel">✈️</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🍽️" title="Food">🍽️</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="👗" title="Fashion">👗</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🚗" title="Automotive">🚗</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🏠" title="Real Estate">🏠</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🎵" title="Music">🎵</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="📱" title="Mobile">📱</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="🌱" title="Environment">🌱</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="⚖️" title="Legal">⚖️</button>
                                    <button type="button" class="btn btn-outline-primary icon-btn" data-icon="📰" title="News">📰</button>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" id="clearIconBtn">Clear Icon</button>
                            <button type="button" class="btn btn-primary" id="closeIconPickerBtn">Done</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
 
                                          

   <button type="submit" class="btn btn-primary waves-effect waves-light">Save Changes</button>

                                        </form>

                                    </div> <!-- end card-body -->
                                </div> <!-- end card-->
                            </div> <!-- end col -->
                        </div>
                        <!-- end row -->

 
                        
                    </div> <!-- container -->

                </div> <!-- content -->

<style>
.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.icon-btn {
    width: 60px;
    height: 60px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.2s;
}

.icon-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.icon-btn.selected {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

#iconPickerCard {
    border: 2px solid var(--bs-primary);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
</style>

<script type="text/javascript">
    $(document).ready(function (){
        $('#myForm').validate({
            rules: {
                category_name: {
                    required : true,
                }, 
            },
            messages :{
                category_name: {
                    required : 'Please Enter Category Name',
                },
            },
            errorElement : 'span', 
            errorPlacement: function (error,element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight : function(element, errorClass, validClass){
                $(element).addClass('is-invalid');
            },
            unhighlight : function(element, errorClass, validClass){
                $(element).removeClass('is-invalid');
            },
        });
    });

    // Icon Picker Functionality
    $(document).ready(function() {
        // Show/Hide Icon Picker
        $('#iconPickerBtn').click(function() {
            $('#iconPickerCard').slideToggle();
        });

        $('#closeIconPickerBtn').click(function() {
            $('#iconPickerCard').slideUp();
        });

        // Icon Selection
        $('.icon-btn').click(function() {
            const selectedIcon = $(this).data('icon');

            // Remove previous selection
            $('.icon-btn').removeClass('selected');

            // Add selection to clicked icon
            $(this).addClass('selected');

            // Update input field
            $('#categoryIcon').val(selectedIcon);
        });

        // Clear Icon
        $('#clearIconBtn').click(function() {
            $('#categoryIcon').val('');
            $('.icon-btn').removeClass('selected');
        });

        // Auto-suggest icon based on category name
        $('#inputEmail4').on('input', function() {
            const categoryName = $(this).val().toLowerCase();
            let suggestedIcon = '';

            // Auto-suggest logic
            if (categoryName.includes('tech') || categoryName.includes('computer')) {
                suggestedIcon = '💻';
            } else if (categoryName.includes('politic') || categoryName.includes('government')) {
                suggestedIcon = '🏛️';
            } else if (categoryName.includes('sport') || categoryName.includes('game')) {
                suggestedIcon = '⚽';
            } else if (categoryName.includes('entertainment') || categoryName.includes('movie') || categoryName.includes('film')) {
                suggestedIcon = '🎬';
            } else if (categoryName.includes('business') || categoryName.includes('finance')) {
                suggestedIcon = '💼';
            } else if (categoryName.includes('health') || categoryName.includes('medical')) {
                suggestedIcon = '🏥';
            } else if (categoryName.includes('science') || categoryName.includes('research')) {
                suggestedIcon = '🔬';
            } else if (categoryName.includes('world') || categoryName.includes('international')) {
                suggestedIcon = '🌍';
            } else if (categoryName.includes('national') || categoryName.includes('country')) {
                suggestedIcon = '🇧🇩';
            } else if (categoryName.includes('opinion') || categoryName.includes('editorial')) {
                suggestedIcon = '💭';
            } else if (categoryName.includes('ai') || categoryName.includes('artificial')) {
                suggestedIcon = '🤖';
            } else if (categoryName.includes('election') || categoryName.includes('vote')) {
                suggestedIcon = '🗳️';
            } else if (categoryName.includes('economic') || categoryName.includes('economy')) {
                suggestedIcon = '📊';
            } else if (categoryName.includes('education') || categoryName.includes('school')) {
                suggestedIcon = '🎓';
            } else if (categoryName.includes('travel') || categoryName.includes('tourism')) {
                suggestedIcon = '✈️';
            }

            // If we have a suggestion and no icon is currently selected
            if (suggestedIcon && !$('#categoryIcon').val()) {
                $('#categoryIcon').val(suggestedIcon);
                $('.icon-btn').removeClass('selected');
                $(`.icon-btn[data-icon="${suggestedIcon}"]`).addClass('selected');
            }
        });
    });

</script>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/category/category_add.blade.php ENDPATH**/ ?>