@extends('admin.admin_management_dashboard')
@section('admin')
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<style>
.modern-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.modern-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border: none;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.input-group-text {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.permission-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.group-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem;
    background: #e7f3ff;
    color: #0066cc;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.group-badge:hover {
    background: #0066cc;
    color: white;
}

.new-group-input {
    display: none;
    margin-top: 0.5rem;
}
</style>

<div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">
                        
                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box">
                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                            <li class="breadcrumb-item"><a href="{{ route('all.permission') }}">Permissions</a></li>
                                            <li class="breadcrumb-item active">Add Permission</li>
                                        </ol>
                                    </div>
                                    <h4 class="page-title">
                                        <i class="mdi mdi-key-plus me-2"></i>
                                        Add New Permission
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- end page title -->

                        <!-- Form row -->
                        <div class="row justify-content-center">
                            <div class="col-lg-8">
                                <div class="modern-card">
                                    <div class="modern-card-header">
                                        <h5 class="mb-0">
                                            <i class="mdi mdi-shield-plus me-2"></i>
                                            Create New Permission
                                        </h5>
                                        <p class="mb-0 opacity-75">Define a new permission for the system</p>
                                    </div>
                                    <div class="card-body p-4">

    <form id="myForm" method="post" action="{{ route('permission.store') }}">
    	@csrf

        <div class="row">
            <div class="col-12 mb-4">
                <label for="permission-name" class="form-label">
                    <i class="mdi mdi-key-variant me-1"></i>
                    Permission Name
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="mdi mdi-shield-outline"></i>
                    </span>
                    <input type="text" name="name" class="form-control" id="permission-name"
                           placeholder="e.g., user.create, post.edit, admin.view" required>
                </div>
                <small class="text-muted">Use dot notation for better organization (e.g., module.action)</small>
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-4">
                <label for="group-select" class="form-label">
                    <i class="mdi mdi-folder-key me-1"></i>
                    Permission Group
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="mdi mdi-folder-outline"></i>
                    </span>
                    <select name="group_name" class="form-select" id="group-select" required>
                        <option value="">Select or create a group...</option>
                        @foreach($existingGroups as $group)
                            <option value="{{ $group }}">{{ $group }}</option>
                        @endforeach
                        <option value="__new__">+ Create New Group</option>
                    </select>
                </div>

                <div class="new-group-input">
                    <input type="text" class="form-control mt-2" id="new-group-name"
                           placeholder="Enter new group name...">
                    <small class="text-muted">This will create a new permission group</small>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-4">
                <label class="form-label">
                    <i class="mdi mdi-tag-multiple me-1"></i>
                    Existing Groups
                </label>
                <div class="existing-groups">
                    @foreach($existingGroups as $group)
                        <span class="group-badge" data-group="{{ $group }}">{{ $group }}</span>
                    @endforeach
                </div>
                <small class="text-muted">Click on a group to select it quickly</small>
            </div>
        </div>

        <div class="permission-preview">
            <h6><i class="mdi mdi-eye me-1"></i>Permission Preview</h6>
            <div class="d-flex align-items-center">
                <strong>Full Permission:</strong>
                <span class="ms-2 text-primary" id="permission-preview">-</span>
            </div>
        </div>

        <div class="text-center mt-4">
            <button type="submit" class="btn btn-modern btn-lg">
                <i class="mdi mdi-content-save me-2"></i>
                Create Permission
            </button>
            <a href="{{ route('all.permission') }}" class="btn btn-outline-secondary btn-lg ms-3">
                <i class="mdi mdi-arrow-left me-2"></i>
                Cancel
            </a>
        </div>

                                        </form>

                                    </div> <!-- end card-body -->
                                </div> <!-- end card-->
                            </div> <!-- end col -->
                        </div>
                        <!-- end row -->

 
                        
                    </div> <!-- container -->

                </div> <!-- content -->

<script>
$(document).ready(function() {
    // Handle new group creation
    $('#group-select').change(function() {
        if ($(this).val() === '__new__') {
            $('.new-group-input').show();
            $('#new-group-name').focus();
        } else {
            $('.new-group-input').hide();
            updatePreview();
        }
    });

    // Handle new group name input
    $('#new-group-name').on('input', function() {
        const newGroupName = $(this).val();
        if (newGroupName) {
            // Update the select option
            $('#group-select option[value="__new__"]').text('+ ' + newGroupName);
            $('#group-select').val('__new__');
            updatePreview();
        }
    });

    // Handle group badge clicks
    $('.group-badge').click(function() {
        const groupName = $(this).data('group');
        $('#group-select').val(groupName);
        $('.new-group-input').hide();
        updatePreview();
    });

    // Update permission preview
    function updatePreview() {
        const permissionName = $('#permission-name').val();
        const groupName = $('#group-select').val() === '__new__' ?
                         $('#new-group-name').val() :
                         $('#group-select').val();

        if (permissionName && groupName && groupName !== '__new__') {
            $('#permission-preview').text(groupName + '.' + permissionName);
        } else if (permissionName) {
            $('#permission-preview').text(permissionName);
        } else {
            $('#permission-preview').text('-');
        }
    }

    // Update preview on input
    $('#permission-name').on('input', updatePreview);

    // Form validation
    $('#myForm').submit(function(e) {
        const permissionName = $('#permission-name').val().trim();
        const groupSelect = $('#group-select').val();

        if (!permissionName) {
            e.preventDefault();
            alert('Please enter a permission name');
            $('#permission-name').focus();
            return false;
        }

        if (!groupSelect) {
            e.preventDefault();
            alert('Please select or create a group');
            $('#group-select').focus();
            return false;
        }

        // If creating new group, update the form data
        if (groupSelect === '__new__') {
            const newGroupName = $('#new-group-name').val().trim();
            if (!newGroupName) {
                e.preventDefault();
                alert('Please enter a new group name');
                $('#new-group-name').focus();
                return false;
            }
            // Update the select value to the new group name
            $('#group-select').append(`<option value="${newGroupName}" selected>${newGroupName}</option>`);
            $('#group-select').val(newGroupName);
        }

        return true;
    });

    // Initialize preview
    updatePreview();
});
</script>

@endsection