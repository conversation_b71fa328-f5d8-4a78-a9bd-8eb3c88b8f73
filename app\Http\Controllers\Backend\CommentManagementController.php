<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\NewsComment;
use App\Models\NewsPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommentManagementController extends Controller
{
    /**
     * Display a listing of comments
     */
    public function index(Request $request)
    {
        $query = NewsComment::with(['newsPost', 'user'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            if ($request->status === 'approved') {
                $query->where('is_approved', true);
            } elseif ($request->status === 'pending') {
                $query->where('is_approved', false);
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhere('guest_name', 'like', "%{$search}%")
                  ->orWhere('guest_email', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('newsPost', function($postQuery) use ($search) {
                      $postQuery->where('news_title', 'like', "%{$search}%");
                  });
            });
        }

        $comments = $query->paginate(20);

        // Get counts for status filter
        $totalComments = NewsComment::count();
        $pendingComments = NewsComment::where('is_approved', false)->count();
        $approvedComments = NewsComment::where('is_approved', true)->count();

        return view('backend.comment_management.index', compact(
            'comments', 
            'totalComments', 
            'pendingComments', 
            'approvedComments'
        ));
    }

    /**
     * Approve a comment
     */
    public function approve($id)
    {
        $comment = NewsComment::findOrFail($id);
        $comment->update([
            'is_approved' => true,
            'approved_by' => Auth::id(),
            'approved_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Comment approved successfully'
        ]);
    }

    /**
     * Reject/Unapprove a comment
     */
    public function reject($id)
    {
        $comment = NewsComment::findOrFail($id);
        $comment->update([
            'is_approved' => false,
            'approved_by' => null,
            'approved_at' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Comment rejected successfully'
        ]);
    }

    /**
     * Delete a comment
     */
    public function destroy($id)
    {
        $comment = NewsComment::findOrFail($id);
        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Comment deleted successfully'
        ]);
    }

    /**
     * Bulk actions for comments
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'comment_ids' => 'required|array',
            'comment_ids.*' => 'exists:news_comments,id'
        ]);

        $commentIds = $request->comment_ids;
        $action = $request->action;

        switch ($action) {
            case 'approve':
                NewsComment::whereIn('id', $commentIds)->update([
                    'is_approved' => true,
                    'approved_by' => Auth::id(),
                    'approved_at' => now()
                ]);
                $message = 'Comments approved successfully';
                break;

            case 'reject':
                NewsComment::whereIn('id', $commentIds)->update([
                    'is_approved' => false,
                    'approved_by' => null,
                    'approved_at' => null
                ]);
                $message = 'Comments rejected successfully';
                break;

            case 'delete':
                NewsComment::whereIn('id', $commentIds)->delete();
                $message = 'Comments deleted successfully';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Show comment details
     */
    public function show($id)
    {
        $comment = NewsComment::with(['newsPost', 'user'])->findOrFail($id);
        
        return view('backend.comment_management.show', compact('comment'));
    }
}
