<?php $__env->startSection('admin'); ?>
<div class="page-content">
    <nav class="page-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Approved Posts</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="card-title">Approved Posts</h6>
                        <div>
                            <a href="<?php echo e(route('admin.posts.pending')); ?>" class="btn btn-warning btn-sm">Pending Posts</a>
                            <a href="<?php echo e(route('admin.posts.rejected')); ?>" class="btn btn-danger btn-sm">Rejected Posts</a>
                        </div>
                    </div>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Author</th>
                                    <th>Category</th>
                                    <th>Approved By</th>
                                    <th>Approved Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $approvedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php if($post->image): ?>
                                            <img src="<?php echo e(asset($post->image)); ?>" alt="Post Image" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php else: ?>
                                            <img src="<?php echo e(asset('upload/no_image.jpg')); ?>" alt="No Image" style="width: 50px; height: 50px; object-fit: cover;">
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo e(Str::limit($post->news_title, 50)); ?></div>
                                        <small class="text-muted"><?php echo e(Str::limit($post->news_details, 80)); ?></small>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo e($post->user->name ?? 'Unknown'); ?></div>
                                        <small class="text-muted"><?php echo e($post->user->email ?? 'N/A'); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo e($post->category->category_name ?? 'Uncategorized'); ?></span>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo e($post->approvedBy->name ?? 'System'); ?></div>
                                        <small class="text-muted"><?php echo e($post->approvedBy->email ?? 'N/A'); ?></small>
                                    </td>
                                    <td>
                                        <?php if($post->approved_at): ?>
                                            <?php
                                                $approvedDate = is_string($post->approved_at) ? \Carbon\Carbon::parse($post->approved_at) : $post->approved_at;
                                            ?>
                                            <div class="fw-bold"><?php echo e($approvedDate->format('M d, Y')); ?></div>
                                            <small class="text-muted"><?php echo e($approvedDate->format('h:i A')); ?></small>
                                        <?php else: ?>
                                            <div class="fw-bold">N/A</div>
                                            <small class="text-muted"></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="<?php echo e(route('admin.posts.show', $post->id)); ?>">
                                                    <i class="mdi mdi-eye"></i> View Details
                                                </a></li>
                                                <li><a class="dropdown-item" href="<?php echo e(url('news/details/'.$post->id.'/'.$post->news_title_slug)); ?>" target="_blank">
                                                    <i class="mdi mdi-open-in-new"></i> View on Frontend
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form action="<?php echo e(route('admin.posts.reject', $post->id)); ?>" method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="dropdown-item text-danger" 
                                                                onclick="return confirm('Are you sure you want to reject this approved post?')">
                                                            <i class="mdi mdi-close-circle"></i> Reject Post
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="mdi mdi-check-circle-outline" style="font-size: 48px; color: #28a745;"></i>
                                            <h5 class="mt-2">No Approved Posts</h5>
                                            <p class="text-muted">There are no approved posts at the moment.</p>
                                            <a href="<?php echo e(route('admin.posts.pending')); ?>" class="btn btn-primary btn-sm">
                                                <i class="mdi mdi-clock-outline"></i> View Pending Posts
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($approvedPosts->hasPages()): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($approvedPosts->links('pagination::bootstrap-5')); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-top: none;
}

.badge {
    font-size: 0.75rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
}

.dropdown-item i {
    margin-right: 0.5rem;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/approval/approved.blade.php ENDPATH**/ ?>