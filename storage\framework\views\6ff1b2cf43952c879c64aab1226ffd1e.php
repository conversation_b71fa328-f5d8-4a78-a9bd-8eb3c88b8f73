<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Niti<PERSON>otha</title>

    <!-- Preload Critical Resources -->
    <link rel="preload" href="<?php echo e(asset('frontend/assets/css/modern-login.css')); ?>" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">

    <!-- Critical CSS Inline -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --text-dark: #1f2937;
            --text-light: #6b7280;
        }

        /* Critical above-the-fold styles */
        body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif; }
        .login-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        .login-container { width: 100%; max-width: 450px; }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem 2.5rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
    </style>

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/modern-login.css')); ?>" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" media="print" onload="this.media='all'">

    <!-- Enhanced form functionality -->
    <script src="<?php echo e(asset('frontend/assets/js/auth-forms.js')); ?>" defer></script>

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('frontend/assets/images/favicon.gif')); ?>" type="image/x-icon">
</head>
<body>
    <div class="login-page">
        <div class="login-container">
            <div class="login-card">
                <!-- Login Header -->
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h1 class="login-title">Welcome Back</h1>
                    <p class="login-subtitle">Sign in to your NitiKotha account</p>
                </div>
                
                <!-- Alert Messages -->
                <?php if(session('status')): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo e(session('status')); ?>

                    </div>
                <?php elseif(session('error')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>
                
                <?php if($errors->any()): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <ul style="margin: 0; padding-left: 1rem;">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <!-- Login Form -->
                <form method="POST" action="<?php echo e(route('login')); ?>" class="login-form" id="loginForm">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="form-input-wrapper">
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                class="form-input" 
                                placeholder="Enter your email address"
                                value="<?php echo e(old('email')); ?>"
                                required 
                                autofocus
                            >
                            <i class="fas fa-envelope form-icon"></i>
                        </div>
                    </div>
                    
                    <!-- Password Field -->
                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="form-input-wrapper">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                class="form-input"
                                placeholder="Enter your password"
                                required
                            >
                            <i class="fas fa-lock form-icon clickable" id="passwordToggleIcon"></i>
                        </div>
                    </div>
                    
                    <!-- Form Options -->
                    <div class="form-options">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                            <label for="remember">Remember me</label>
                        </div>
                        <a href="<?php echo e(route('password.request')); ?>" class="forgot-password">
                            Forgot password?
                        </a>
                    </div>
                    
                    <!-- Login Button -->
                    <button type="submit" class="login-button" id="loginButton">
                        <span class="button-text">Sign In</span>
                    </button>
                </form>
                
                <!-- Social Login -->
                <div class="social-login">
                    <div class="divider">
                        <span>or continue with</span>
                    </div>
                    <div class="social-buttons">
                        <a href="#" class="social-button google">
                            <i class="fab fa-google"></i>
                            Google
                        </a>
                        <a href="#" class="social-button facebook">
                            <i class="fab fa-facebook-f"></i>
                            Facebook
                        </a>
                    </div>
                </div>
                
                <!-- Login Footer -->
                <div class="login-footer">
                    <p class="signup-link">
                        Don't have an account? 
                        <a href="<?php echo e(route('register')); ?>">Create one here</a>
                    </p>
                    <p class="signup-link">
                        <a href="<?php echo e(url('/')); ?>">
                            <i class="fas fa-arrow-left"></i> Back to Homepage
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const buttonText = loginButton.querySelector('.button-text');
            
            // Form submission with loading state
            loginForm.addEventListener('submit', function(e) {
                // Add loading state
                loginButton.classList.add('loading');
                loginButton.disabled = true;
                buttonText.textContent = 'Signing In...';
                
                // If there are validation errors, the form will reload
                // and the loading state will be reset automatically
            });
            
            // Input focus effects
            const formInputs = document.querySelectorAll('.form-input');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
                
                // Check if input has value on page load
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });
            
            // Password visibility toggle (optional enhancement)
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordToggleIcon');

            // Add toggle functionality
            passwordIcon.addEventListener('click', function() {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    this.classList.remove('fa-lock');
                    this.classList.add('fa-lock-open');
                } else {
                    passwordInput.type = 'password';
                    this.classList.remove('fa-lock-open');
                    this.classList.add('fa-lock');
                }
            });

            passwordIcon.style.cursor = 'pointer';
            passwordIcon.style.pointerEvents = 'auto';
            passwordIcon.title = 'Toggle password visibility';
            
            // Form validation enhancement
            const emailInput = document.getElementById('email');
            emailInput.addEventListener('blur', function() {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (this.value && !emailRegex.test(this.value)) {
                    this.style.borderColor = '#ef4444';
                    this.style.boxShadow = '0 0 0 3px rgba(239, 68, 68, 0.1)';
                } else {
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                }
            });
            
            // Social login handlers (implement as needed)
            const socialButtons = document.querySelectorAll('.social-button');
            socialButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const provider = this.classList.contains('google') ? 'Google' : 'Facebook';
                    alert(`${provider} login will be implemented here`);
                });
            });
            
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Enter key to submit form
                if (e.key === 'Enter' && !e.shiftKey) {
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === 'INPUT') {
                        loginForm.submit();
                    }
                }
            });
            
            // Auto-focus email field if empty
            if (!emailInput.value) {
                emailInput.focus();
            }
        });
    </script>
</body>
</html>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/auth/login_modern.blade.php ENDPATH**/ ?>