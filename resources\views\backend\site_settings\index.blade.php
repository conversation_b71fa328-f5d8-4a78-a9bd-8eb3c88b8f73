@extends('admin.admin_management_dashboard')
@section('admin')

<div class="container-fluid">
    
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Site Settings</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    <i class="mdi mdi-cog text-primary me-2"></i>Site Settings
                </h4>
                <p class="page-title-desc">Manage your website's general settings, appearance, and configuration.</p>
            </div>
        </div>
    </div>     
    <!-- end page title --> 

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="mdi mdi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <form action="{{ route('admin.site-settings.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="row">
            @foreach($settings as $group => $groupSettings)
            <div class="col-xl-6 col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">
                            @switch($group)
                                @case('general')
                                    <i class="mdi mdi-cog text-primary me-2"></i>General Settings
                                    @break
                                @case('appearance')
                                    <i class="mdi mdi-palette text-info me-2"></i>Appearance Settings
                                    @break
                                @case('contact')
                                    <i class="mdi mdi-phone text-success me-2"></i>Contact Information
                                    @break
                                @case('social')
                                    <i class="mdi mdi-share-variant text-warning me-2"></i>Social Media
                                    @break
                                @default
                                    <i class="mdi mdi-settings text-secondary me-2"></i>{{ ucfirst($group) }} Settings
                            @endswitch
                        </h4>
                        <p class="card-title-desc">
                            @switch($group)
                                @case('general')
                                    Configure basic site information and metadata
                                    @break
                                @case('appearance')
                                    Manage logos, favicons, and visual elements
                                    @break
                                @case('contact')
                                    Update contact details and address information
                                    @break
                                @case('social')
                                    Add social media links and profiles
                                    @break
                                @default
                                    Manage {{ $group }} related settings
                            @endswitch
                        </p>
                    </div>
                    <div class="card-body">
                        @foreach($groupSettings as $setting)
                        <div class="mb-3">
                            <label for="{{ $setting->key }}" class="form-label">
                                {{ $setting->label }}
                                @if($setting->description)
                                    <i class="mdi mdi-information-outline text-muted ms-1" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ $setting->description }}"></i>
                                @endif
                            </label>
                            
                            @switch($setting->type)
                                @case('textarea')
                                    <textarea name="settings[{{ $setting->key }}]" 
                                              id="{{ $setting->key }}" 
                                              class="form-control" 
                                              rows="3"
                                              placeholder="{{ $setting->description }}">{{ $setting->value }}</textarea>
                                    @break
                                    
                                @case('image')
                                    <div class="input-group">
                                        <input type="file" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="{{ $setting->key }}" 
                                               class="form-control" 
                                               accept="image/*">
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearImage('{{ $setting->key }}')">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    @if($setting->value)
                                        <div class="mt-2">
                                            <img src="{{ asset($setting->value) }}"
                                                 alt="{{ $setting->label }}"
                                                 class="img-thumbnail"
                                                 style="max-height: 100px;">
                                            <small class="text-muted d-block">Current: {{ basename($setting->value) }}</small>
                                        </div>
                                    @endif
                                    @break
                                    
                                @case('file')
                                    <div class="input-group">
                                        <input type="file" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="{{ $setting->key }}" 
                                               class="form-control">
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearFile('{{ $setting->key }}')">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    @if($setting->value)
                                        <div class="mt-2">
                                            <a href="{{ asset('storage/' . $setting->value) }}" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="mdi mdi-download me-1"></i>{{ basename($setting->value) }}
                                            </a>
                                        </div>
                                    @endif
                                    @break
                                    
                                @case('boolean')
                                    <div class="form-check form-switch">
                                        <input type="hidden" name="settings[{{ $setting->key }}]" value="0">
                                        <input type="checkbox" 
                                               name="settings[{{ $setting->key }}]" 
                                               id="{{ $setting->key }}" 
                                               class="form-check-input" 
                                               value="1" 
                                               {{ $setting->value ? 'checked' : '' }}>
                                        <label class="form-check-label" for="{{ $setting->key }}">
                                            {{ $setting->description ?: 'Enable this option' }}
                                        </label>
                                    </div>
                                    @break
                                    
                                @case('email')
                                    <input type="email" 
                                           name="settings[{{ $setting->key }}]" 
                                           id="{{ $setting->key }}" 
                                           class="form-control" 
                                           value="{{ $setting->value }}" 
                                           placeholder="{{ $setting->description }}">
                                    @break
                                    
                                @case('url')
                                    <input type="url" 
                                           name="settings[{{ $setting->key }}]" 
                                           id="{{ $setting->key }}" 
                                           class="form-control" 
                                           value="{{ $setting->value }}" 
                                           placeholder="{{ $setting->description }}">
                                    @break
                                    
                                @default
                                    <input type="text" 
                                           name="settings[{{ $setting->key }}]" 
                                           id="{{ $setting->key }}" 
                                           class="form-control" 
                                           value="{{ $setting->value }}" 
                                           placeholder="{{ $setting->description }}">
                            @endswitch
                            
                            @if($setting->description && $setting->type !== 'boolean')
                                <small class="form-text text-muted">{{ $setting->description }}</small>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="mdi mdi-content-save me-2"></i>Save Settings
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary btn-lg ms-2">
                            <i class="mdi mdi-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
</div>

<script>
// Check if Waves is defined, if not, define a dummy to prevent errors
if (typeof Waves === 'undefined') {
    window.Waves = {
        init: function() {},
        attach: function() {},
        ripple: function() {}
    };
}

// Check if feather is defined, if not, define a dummy to prevent errors
if (typeof feather === 'undefined') {
    window.feather = {
        replace: function() {},
        icons: {}
    };
}

function clearImage(key) {
    document.getElementById(key).value = '';
}

function clearFile(key) {
    document.getElementById(key).value = '';
}

// Ensure jQuery is loaded before initializing
function initializeSiteSettings() {
    if (typeof $ === 'undefined') {
        console.log('Waiting for jQuery to load...');
        setTimeout(initializeSiteSettings, 100);
        return;
    }

    $(document).ready(function() {
        console.log('=== Site Settings System Starting ===');
        console.log('jQuery loaded:', typeof $ !== 'undefined');
        console.log('jQuery version:', $.fn.jquery);
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Validate required fields
        $('input[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            toastr.error('Please fill in all required fields.');
        }
    });
    
    // Real-time validation
    $('input, textarea').on('blur', function() {
        if ($(this).attr('required') && !$(this).val()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

        console.log('=== Site Settings System Initialized Successfully ===');
    });
}

// Initialize when page loads
initializeSiteSettings();
</script>

@endsection
