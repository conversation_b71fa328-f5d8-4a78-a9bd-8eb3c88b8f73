@charset "UTF-8";
@import url("https://fonts.googleapis.com/css?family=Nunito:400,600,700,900");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-light.eot");
  src: local("Cerebri-sans Light"), url("../fonts/cerebrisans-light.woff") format("woff");
  font-weight: 300;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-regular.eot");
  src: local("Cerebri-sans Regular"), url("../fonts/cerebrisans-regular.woff") format("woff");
  font-weight: 400;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-medium.eot");
  src: local("Cerebri-sans Medium"), url("../fonts/cerebrisans-medium.woff") format("woff");
  font-weight: 500;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-semibold.eot");
  src: local("Cerebri-sans Semibold"), url("../fonts/cerebrisans-semibold.woff") format("woff");
  font-weight: 600;
}
@font-face {
  font-family: "Cerebri Sans,sans-serif";
  src: url("../fonts/cerebrisans-bold.eot");
  src: local("Cerebri-sans Bold"), url("../fonts/cerebrisans-bold.woff") format("woff");
  font-weight: 700;
}
:root {
  --ct-gray-100-rgb: 247, 248, 249;
  --ct-gray-200-rgb: 236, 239, 241;
  --ct-gray-300-rgb: 222, 226, 230;
  --ct-gray-400-rgb: 206, 212, 218;
  --ct-gray-500-rgb: 173, 181, 189;
  --ct-gray-600-rgb: 152, 166, 173;
  --ct-gray-700-rgb: 108, 117, 125;
  --ct-gray-800-rgb: 52, 58, 64;
  --ct-gray-900-rgb: 50, 58, 70;
  --ct-box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12);
  --ct-box-shadow-sm: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  --ct-box-shadow-lg: 0 2px 5px 0 rgba(0, 0, 0, 0.16),0 2px 10px 0 rgba(0, 0, 0, 0.12);
  --ct-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --ct-component-active-color: #fff;
  --ct-component-active-bg: #6658dd;
  --ct-text-muted: #98a6ad;
  --ct-blockquote-footer-color: #98a6ad;
  --ct-hr-color: #e5e8eb;
  --ct-input-btn-focus-color: rgba(102, 88, 221, 0.25);
  --ct-form-text-color: var(--ct-text-muted);
  --ct-input-bg: #fff;
  --ct-input-disabled-bg: #fff;
  --ct-input-color: #6c757d;
  --ct-input-border-color: #ced4da;
  --ct-input-box-shadow: var(--ct-box-shadow-inset);
  --ct-input-focus-bg: var(--ct-input-bg);
  --ct-input-focus-border-color: #b9bfc4;
  --ct-input-focus-color: var(--ct-input-color);
  --ct-input-placeholder-color: #adb5bd;
  --ct-input-plaintext-color: #6c757d;
  --ct-form-check-input-bg: #fff;
  --ct-form-check-input-border: 1px solid #adb5bd;
  --ct-form-check-input-checked-color: var(--ct-component-active-color);
  --ct-form-check-input-checked-bg-color: var(--ct-component-active-bg);
  --ct-form-check-input-checked-border-color: var(--ct-form-check-input-checked-bg-color);
  --ct-form-check-input-indeterminate-color: var(--ct-component-active-color);
  --ct-form-check-input-indeterminate-bg-color: var(--ct-component-active-bg);
  --ct-form-check-input-indeterminate-border-color: var(--ct-form-check-input-indeterminate-bg-color);
  --ct-form-switch-color: #dee2e6;
  --ct-input-group-addon-color: var(--ct-input-color);
  --ct-input-group-addon-bg: #eceff1;
  --ct-input-group-addon-border-color: #ced4da;
  --ct-form-select-color: var(--ct-input-color);
  --ct-form-select-bg: var(--ct-input-bg);
  --ct-form-select-disabled-color: #98a6ad;
  --ct-form-select-disabled-bg: #eceff1;
  --ct-form-select-disabled-border-color: ;
  --ct-form-select-indicator-color: #343a40;
  --ct-form-range-track-bg: #dee2e6;
  --ct-form-range-track-box-shadow: var(--ct-box-shadow-inset);
  --ct-form-range-thumb-box-shadow: 0 .1rem .25rem rgba(0, 0, 0, 0.1);
  --ct-form-range-thumb-active-bg: #d1cdf5;
  --ct-form-range-thumb-disabled-bg: #adb5bd;
  --ct-form-file-button-color: var(--ct-input-color);
  --ct-form-file-button-bg: var(--ct-input-group-addon-bg);
  --ct-form-file-button-hover-bg: #e0e3e5;
  --ct-thumbnail-bg: #f5f6f8;
  --ct-thumbnail-border-color: #dee2e6;
  --ct-thumbnail-box-shadow: var(--ct-box-shadow-sm);
  --ct-figure-caption-color: #98a6ad;
  --ct-breadcrumb-divider-color: #ced4da;
  --ct-breadcrumb-active-color: #adb5bd;
  --ct-carousel-control-color: #fff;
  --ct-carousel-indicator-active-bg: #fff;
  --ct-carousel-caption-color: #fff;
  --ct-carousel-dark-indicator-active-bg: #000;
  --ct-carousel-dark-caption-color: #000;
  --ct-btn-close-color: #000;
  --ct-kbd-color: #fff;
  --ct-kbd-bg: #323a46;
  --ct-pre-color: #323a46;
  --ct-bg-leftbar: #fff;
  --ct-bg-leftbar-dark: #38414a;
  --ct-bg-leftbar-brand: #4a81d4;
  --ct-bg-leftbar-gradient: #683ba9;
  --ct-twocolumn-sidebar-bg: #fff;
  --ct-twocolumn-sidebar-iconview-bg: #3e4852;
  --ct-menu-item-color-dark: #9097a7;
  --ct-menu-item-hover-color-dark: #c8cddc;
  --ct-menu-item-active-color-dark: #fff;
  --ct-menu-item: #6e768e;
  --ct-menu-item-hover: #00acc1;
  --ct-menu-item-active: #00acc1;
  --ct-menu-sub-item-active: #00acc1;
  --ct-menuitem-active-bg: rgba(0, 172, 193, 0.07);
  --ct-hori-menu-item-color: #6e7488;
  --ct-rightbar-bg: #fff;
  --ct-bg-topbar-light: #fff;
  --ct-bg-topbar-dark: #566676;
  --ct-app-search-box-bg: rgba(255, 255, 255, 0.07);
  --ct-box-shadow-condensed: 3px 5px 10px 0 rgba(154, 161, 171, 0.2);
  --ct-footer-bg: #eeeff3;
  --ct-auth-bg: #fff;
  --ct-auth-bg-alt: #6658dd;
  --ct-apex-grid-color: #f9f9fd;
  --ct-chat-primary-user-bg: #fef5e4;
  --ct-chat-secondary-user-bg: #f1f3fa;
  --ct-heading-color: #343a40;
  --ct-card-overlay-color: #3e4852;
  --ct-card-overlay-bg: rgba(255, 255, 255, 0.8);
  --ct-input-light-bg: #f3f7f9;
  --ct-lighten-300: #edeff1;
  --ct-components-shadow-sm: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  --ct-components-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16), 0 2px 6px 0 rgba(0, 0, 0, 0.12);
  --ct-components-shadow-lg: 0 0 45px 0 rgba(0, 0, 0, 0.12);
}
:root .table, :root .table-bordered, :root .table-striped, :root .jsgrid-table {
  --ct-table-color: #6c757d;
  --ct-table-bg: transparent;
  --ct-table-accent-bg: transparent;
  --ct-table-striped-color: var(--ct-table-color);
  --ct-table-striped-bg: #f7f8f9;
  --ct-table-active-color: var(--ct-table-color);
  --ct-table-active-bg: #f7f8f9;
  --ct-table-hover-color: var(--ct-table-color);
  --ct-table-hover-bg: #f7f8f9;
  --ct-table-border-color: #dee2e6;
  --ct-table-group-separator-color: #dee2e6;
  --ct-table-caption-color: var(--ct-text-muted);
}
:root .btn {
  --ct-btn-box-shadow: 0px 2px 6px 0px;
  --ct-btn-active-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --ct-btn-link-color: #6658dd;
  --ct-btn-link-hover-color: #473e9b;
  --ct-btn-link-disabled-color: #98a6ad;
}
:root .nav {
  --ct-nav-link-disabled-color: #98a6ad;
  --ct-nav-tabs-border-color: #dee2e6;
  --ct-nav-tabs-link-hover-border-color: #eceff1 #eceff1 var(--ct-nav-tabs-border-color);
  --ct-nav-tabs-link-active-color: #6c757d;
  --ct-nav-tabs-link-active-bg: #fff;
  --ct-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 var(--ct-nav-tabs-link-active-bg);
}
:root .navbar {
  --ct-navbar-light-color: rgba(0, 0, 0, 0.55);
  --ct-navbar-light-hover-color: rgba(0, 0, 0, 0.7);
  --ct-navbar-light-active-color: rgba(0, 0, 0, 0.9);
  --ct-navbar-light-disabled-color: rgba(0, 0, 0, 0.3);
  --ct-navbar-light-toggler-border-color: rgba(0, 0, 0, 0.1);
}
:root .navbar-dark {
  --ct-navbar-dark-color: rgba(255, 255, 255, 0.55);
  --ct-navbar-dark-hover-color: rgba(255, 255, 255, 0.75);
  --ct-navbar-dark-active-color: #fff;
  --ct-navbar-dark-disabled-color: rgba(255, 255, 255, 0.25);
  --ct-navbar-dark-toggler-border-color: rgba(255, 255, 255, 0.1);
}
:root .dropdown-menu, :root .select2-dropdown, :root .selectize-dropdown, :root .autocomplete-suggestions, :root .daterangepicker, :root .ql-toolbar, :root .dd-list, :root .popover-content, :root .clockpicker-popover, :root .flatpickr-calendar, :root .message-list, :root .mail-list, :root .right-bar {
  --ct-dropdown-color: #6c757d;
  --ct-dropdown-bg: #fff;
  --ct-dropdown-border-color: #e5e8ea;
  --ct-dropdown-divider-bg: #eceff1;
  --ct-dropdown-box-shadow: 0 .5rem 1rem rgba(0, 0, 0, 0.175);
  --ct-dropdown-link-color: #6c757d;
  --ct-dropdown-link-hover-color: ;
  --ct-dropdown-link-hover-bg: #f7f8f9;
  --ct-dropdown-link-active-color: #323a46;
  --ct-dropdown-link-active-bg: #f7f8f9;
  --ct-dropdown-link-disabled-color: #98a6ad;
}
:root .dropdown-menu-dark, :root .select2-dropdown, :root .selectize-dropdown, :root .autocomplete-suggestions, :root .daterangepicker, :root .ql-toolbar, :root .dd-list, :root .popover-content, :root .clockpicker-popover, :root .flatpickr-calendar, :root .message-list, :root .mail-list, :root .right-bar {
  --ct-dropdown-dark-color: #dee2e6;
  --ct-dropdown-dark-bg: #343a40;
  --ct-dropdown-dark-border-color: var(--ct-dropdown-border-color);
  --ct-dropdown-dark-divider-bg: var(--ct-dropdown-divider-bg);
  --ct-dropdown-dark-link-color: var(--ct-dropdown-dark-color);
  --ct-dropdown-dark-link-hover-color: #fff;
  --ct-dropdown-dark-link-hover-bg: rgba(255, 255, 255, 0.15);
  --ct-dropdown-dark-link-active-color: var(--ct-dropdown-link-active-color);
  --ct-dropdown-dark-link-active-bg: var(--ct-dropdown-link-active-bg);
  --ct-dropdown-dark-link-disabled-color: #adb5bd;
  --ct-dropdown-dark-header-color: #adb5bd;
}
:root .pagination {
  --ct-pagination-color: #323a46;
  --ct-pagination-bg: #fff;
  --ct-pagination-border-color: #dee2e6;
  --ct-pagination-focus-color: #473e9b;
  --ct-pagination-focus-bg: #eceff1;
  --ct-pagination-focus-box-shadow: 0 0 0 0.15rem var(--ct-input-btn-focus-color);
  --ct-pagination-hover-color: #323a46;
  --ct-pagination-hover-bg: #eceff1;
  --ct-pagination-hover-border-color: #dee2e6;
  --ct-pagination-disabled-color: #98a6ad;
  --ct-pagination-disabled-bg: #fff;
  --ct-pagination-disabled-border-color: #dee2e6;
}
:root .card, :root .card-header, :root .timeline, :root .gal-box, :root #preloader {
  --ct-card-border-color: #eceff1;
  --ct-card-box-shadow: var(--ct-box-shadow-sm);
  --ct-card-cap-bg: #edeff1;
  --ct-card-bg: #fff;
}
:root .accordion {
  --ct-accordion-color: #6c757d;
  --ct-accordion-border-color: rgba(0, 0, 0, 0.125);
  --ct-accordion-button-active-bg: #f0eefc;
  --ct-accordion-button-active-color: #5c4fc7;
  --ct-accordion-button-focus-border-color: var(--ct-input-focus-border-color);
  --ct-accordion-button-focus-box-shadow: 0 0 0 0.15rem var(--ct-input-btn-focus-color);
}
:root .tooltip {
  --ct-tooltip-color: #fff;
  --ct-tooltip-bg: #000;
}
:root .popover {
  --ct-popover-bg: #fff;
  --ct-popover-border-color: #dee2e6;
  --ct-popover-box-shadow: 0 .25rem .5rem rgba(0, 0, 0, 0.2);
  --ct-popover-header-bg: #f0f0f0;
  --ct-popover-header-color: ;
  --ct-popover-body-color: #6c757d;
  --ct-popover-arrow-color: #fff;
  --ct-popover-arrow-outer-color: #dee2e6;
}
:root .toast {
  --ct-toast-background-color: rgba(255, 255, 255, 0.85);
  --ct-toast-border-color: rgba(0, 0, 0, 0.1);
  --ct-toast-box-shadow: var(--ct-box-shadow);
  --ct-toast-header-color: #98a6ad;
  --ct-toast-header-background-color: rgba(255, 255, 255, 0.85);
  --ct-toast-header-border-color: rgba(0, 0, 0, 0.05);
}
:root .badge {
  --ct-badge-color: #fff;
}
:root .modal, :root .modal-backdrop, :root .offcanvas, :root .offcanvas-backdrop {
  --ct-modal-content-bg: #fff;
  --ct-modal-content-box-shadow-xs: 0 .25rem .5rem rgba(0, 0, 0, 0.5);
  --ct-modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba(0, 0, 0, 0.5);
  --ct-modal-backdrop-bg: #323a46;
  --ct-modal-header-border-color: #dee2e6;
  --ct-modal-footer-border-color: #dee2e6;
}
:root .offcanvas, :root .offcanvas-xxl, :root .offcanvas-xl, :root .offcanvas-lg, :root .offcanvas-md, :root .offcanvas-sm {
  --ct-offcanvas-bg: #eceff1;
  --ct-offcanvas-border-color: transparent;
}
:root .progress, :root .irs {
  --ct-progress-bg: #eef0f2;
  --ct-progress-box-shadow: var(--ct-box-shadow-inset);
  --ct-progress-bar-color: #fff;
  --ct-progress-bar-bg: #6658dd;
}
:root .list-group {
  --ct-list-group-color: ;
  --ct-list-group-bg: #fff;
  --ct-list-group-border-color: rgba(0, 0, 0, 0.125);
  --ct-list-group-hover-bg: #f7f8f9;
  --ct-list-group-disabled-color: #98a6ad;
  --ct-list-group-disabled-bg: #fff;
  --ct-list-group-action-color: #6c757d;
  --ct-list-group-action-active-color: #6c757d;
  --ct-list-group-action-active-bg: #eceff1;
}

body[data-theme=dark] {
  --ct-gray-100: #323a46;
  --ct-gray-200: #36404a;
  --ct-gray-300: #424e5a;
  --ct-gray-400: #5d7186;
  --ct-gray-500: #8c98a5;
  --ct-gray-600: #cedeef;
  --ct-gray-700: #dee2e6;
  --ct-gray-800: #f7f7f7;
  --ct-gray-900: #f3f7f9;
  --ct-gray-100-rgb: 50, 58, 70;
  --ct-gray-200-rgb: 54, 64, 74;
  --ct-gray-300-rgb: 66, 78, 90;
  --ct-gray-400-rgb: 93, 113, 134;
  --ct-gray-500-rgb: 140, 152, 165;
  --ct-gray-600-rgb: 206, 222, 239;
  --ct-gray-700-rgb: 222, 226, 230;
  --ct-gray-800-rgb: 247, 247, 247;
  --ct-gray-900-rgb: 243, 247, 249;
  --ct-body-bg: #303841;
  --ct-body-color: #94a0ad;
  --ct-body-bg-rgb: 48, 56, 65;
  --ct-body-color-rgb: 148, 160, 173;
  --ct-light: #424e5a;
  --ct-light-rgb: 66, 78, 90;
  --ct-dark: #f3f7f9;
  --ct-dark-rgb: 243, 247, 249;
  --ct-link-color: #6658dd;
  --ct-link-hover-color: #6658dd;
  --ct-border-color: #424e5a;
  --ct-box-shadow: 0px 0px 35px 0px rgba(66, 72, 80, 0.15);
  --ct-box-shadow-sm: 0 0.75rem 6rem rgba(56, 65, 74, 0.03);
  --ct-box-shadow-lg: 0 0 45px 0 rgba(0, 0, 0, 0.12);
  --ct-box-shadow-inset: inset 0 -0.1rem 0 rgba(0, 0, 0, 0.075);
  --ct-component-active-color: #fff;
  --ct-component-active-bg: #6658dd;
  --ct-text-muted: #8c98a5;
  --ct-blockquote-footer-color: #cedeef;
  --ct-hr-color: #424e5a;
  --ct-mark-bg: #fcf8e3;
  --ct-input-btn-focus-color: rgba(102, 88, 221, 0.25);
  --ct-form-text-color: var(--ct-text-muted);
  --ct-input-bg: #3b4651;
  --ct-input-disabled-bg: #3b4651;
  --ct-input-color: var(--ct-body-color);
  --ct-input-border-color: #424e5a;
  --ct-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  --ct-input-focus-bg: var(--ct-input-bg);
  --ct-input-focus-border-color: #475461;
  --ct-input-focus-color: var(--ct-body-color);
  --ct-input-placeholder-color: #586877;
  --ct-input-plaintext-color: var(--ct-body-color);
  --ct-form-check-input-bg: #3b4651;
  --ct-form-check-input-border: 1px solid #5d7186;
  --ct-form-check-input-checked-color: var(--ct-component-active-color);
  --ct-form-check-input-checked-bg-color: var(--ct-component-active-bg);
  --ct-form-check-input-checked-border-color: var(--ct-form-check-input-checked-bg-color);
  --ct-form-check-input-indeterminate-color: var(--ct-component-active-color);
  --ct-form-check-input-indeterminate-bg-color: var(--ct-component-active-bg);
  --ct-form-check-input-indeterminate-border-color: var(--ct-form-check-input-indeterminate-bg-color);
  --ct-form-switch-color: #5d7186;
  --ct-input-group-addon-color: var(--ct-body-color);
  --ct-input-group-addon-bg: #36404a;
  --ct-input-group-addon-border-color: #424e5a;
  --ct-form-select-color: var(--ct-body-color);
  --ct-form-select-bg: var(--ct-input-bg);
  --ct-form-select-disabled-color: #cedeef;
  --ct-form-select-disabled-bg: #36404a;
  --ct-form-select-disabled-border-color: ;
  --ct-form-select-indicator-color: #f7f7f7;
  --ct-form-range-track-bg: #424e5a;
  --ct-form-range-track-box-shadow: inset 0 0.25rem 0.25rem rgba(0, 0, 0, 0.1);
  --ct-form-range-thumb-box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);
  --ct-form-range-thumb-active-bg: #d1cdf5;
  --ct-form-range-thumb-disabled-bg: #8c98a5;
  --ct-form-file-button-color: var(--ct-body-color);
  --ct-form-file-button-bg: var(--ct-input-group-addon-bg);
  --ct-form-file-button-hover-bg: #333d46;
  --ct-thumbnail-bg: #424e5a;
  --ct-thumbnail-border-color: #424e5a;
  --ct-thumbnail-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);
  --ct-figure-caption-color: #cedeef;
  --ct-breadcrumb-divider-color: #5d7186;
  --ct-breadcrumb-active-color: #8c98a5;
  --ct-carousel-control-color: #fff;
  --ct-carousel-indicator-active-bg: #fff;
  --ct-carousel-caption-color: #fff;
  --ct-carousel-dark-indicator-active-bg: #000;
  --ct-carousel-dark-caption-color: #000;
  --ct-btn-close-color: #f7f7f7;
  --ct-code-color: #f672a7;
  --ct-kbd-color: #fff;
  --ct-kbd-bg: #424e5a;
  --ct-pre-color: #f3f7f9;
  --ct-bg-leftbar: #fff;
  --ct-bg-leftbar-dark: #37424c;
  --ct-bg-leftbar-brand: #4a81d4;
  --ct-bg-leftbar-gradient: #683ba9;
  --ct-twocolumn-sidebar-bg: #fff;
  --ct-twocolumn-sidebar-iconview-bg: #4a81d4;
  --ct-menu-item-color-dark: #9097a7;
  --ct-menu-item-hover-color-dark: #c8cddc;
  --ct-menu-item-active-color-dark: #fff;
  --ct-menu-item: #6e768e;
  --ct-menu-item-hover: #4a81d4;
  --ct-menu-item-active: #4a81d4;
  --ct-menu-sub-item-active: #4a81d4;
  --ct-menuitem-active-bg: rgba(74, 129, 212, 0.07);
  --ct-hori-menu-item-color: #919eab;
  --ct-rightbar-bg: #3b4651;
  --ct-bg-topbar-light: #3c4752;
  --ct-bg-topbar-dark: #3c4752;
  --ct-app-search-box-bg: #323a46;
  --ct-box-shadow-condensed: 3px 5px 10px 0 rgb(50, 58, 66);
  --ct-footer-bg: #2b323a;
  --ct-auth-bg: #36404a;
  --ct-auth-bg-alt: #3e4852;
  --ct-apex-grid-color: #3e4852;
  --ct-chat-primary-user-bg: #404c58;
  --ct-chat-secondary-user-bg: #404c58;
  --ct-heading-color: #acbfd2;
  --ct-card-overlay-color: #fff;
  --ct-card-overlay-bg: rgba(255, 255, 255, 0.1);
  --ct-input-light-bg: #3b4550;
  --ct-lighten-300: #4d5b69;
}
body[data-theme=dark] .table, body[data-theme=dark] .table-bordered, body[data-theme=dark] .table-striped, body[data-theme=dark] .jsgrid-table {
  --ct-table-color: var(--ct-body-color);
  --ct-table-bg: transparent;
  --ct-table-accent-bg: transparent;
  --ct-table-striped-color: var(--ct-body-color);
  --ct-table-striped-bg: #3b4651;
  --ct-table-active-color: var(--ct-body-color);
  --ct-table-active-bg: #3b4651;
  --ct-table-hover-color: var(--ct-body-color);
  --ct-table-hover-bg: #3b4651;
  --ct-table-border-color: #424e5a;
  --ct-table-group-separator-color: #424e5a;
  --ct-table-caption-color: var(--ct-text-muted);
}
body[data-theme=dark] .btn {
  --ct-btn-box-shadow: 0 0 0;
  --ct-btn-active-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --ct-btn-link-color: #6658dd;
  --ct-btn-link-hover-color: #473e9b;
  --ct-btn-link-disabled-color: #cedeef;
}
body[data-theme=dark] .nav {
  --ct-nav-link-disabled-color: #cedeef;
  --ct-nav-tabs-border-color: #424e5a;
  --ct-nav-tabs-link-hover-border-color: #36404a #36404a var(--ct-nav-tabs-border-color);
  --ct-nav-tabs-link-active-color: #dee2e6;
  --ct-nav-tabs-link-active-bg: #424e5a;
  --ct-nav-tabs-link-active-border-color: #424e5a #424e5a var(--ct-nav-tabs-link-active-bg);
}
body[data-theme=dark] .navbar {
  --ct-navbar-light-color: rgba(247, 247, 247, 0.5);
  --ct-navbar-light-hover-color: rgba(247, 247, 247, 0.7);
  --ct-navbar-light-active-color: rgba(247, 247, 247, 0.9);
  --ct-navbar-light-disabled-color: rgba(247, 247, 247, 0.3);
  --ct-navbar-light-toggler-border-color: rgba(247, 247, 247, 0.1);
}
body[data-theme=dark] .navbar-dark {
  --ct-navbar-dark-color: rgba(255, 255, 255, 0.55);
  --ct-navbar-dark-hover-color: rgba(255, 255, 255, 0.75);
  --ct-navbar-dark-active-color: #fff;
  --ct-navbar-dark-disabled-color: rgba(255, 255, 255, 0.25);
  --ct-navbar-dark-toggler-border-color: rgba(255, 255, 255, 0.1);
}
body[data-theme=dark] .dropdown-menu, body[data-theme=dark] .select2-dropdown, body[data-theme=dark] .selectize-dropdown, body[data-theme=dark] .autocomplete-suggestions, body[data-theme=dark] .daterangepicker, body[data-theme=dark] .ql-toolbar, body[data-theme=dark] .dd-list, body[data-theme=dark] .popover-content, body[data-theme=dark] .clockpicker-popover, body[data-theme=dark] .flatpickr-calendar, body[data-theme=dark] .message-list, body[data-theme=dark] .mail-list, body[data-theme=dark] .right-bar {
  --ct-dropdown-color: var(--ct-body-color);
  --ct-dropdown-bg: #3b4651;
  --ct-dropdown-border-color: #414d59;
  --ct-dropdown-divider-bg: #475461;
  --ct-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  --ct-dropdown-link-color: var(--ct-body-color);
  --ct-dropdown-link-hover-color: ;
  --ct-dropdown-link-hover-bg: #424e5a;
  --ct-dropdown-link-active-color: #f3f7f9;
  --ct-dropdown-link-active-bg: #424e5a;
  --ct-dropdown-link-disabled-color: #5d7186;
}
body[data-theme=dark] .dropdown-menu-dark, body[data-theme=dark] .select2-dropdown, body[data-theme=dark] .selectize-dropdown, body[data-theme=dark] .autocomplete-suggestions, body[data-theme=dark] .daterangepicker, body[data-theme=dark] .ql-toolbar, body[data-theme=dark] .dd-list, body[data-theme=dark] .popover-content, body[data-theme=dark] .clockpicker-popover, body[data-theme=dark] .flatpickr-calendar, body[data-theme=dark] .message-list, body[data-theme=dark] .mail-list, body[data-theme=dark] .right-bar {
  --ct-dropdown-dark-color: #424e5a;
  --ct-dropdown-dark-bg: #f7f7f7;
  --ct-dropdown-dark-border-color: var(--ct-dropdown-border-color);
  --ct-dropdown-dark-divider-bg: var(--ct-dropdown-divider-bg);
  --ct-dropdown-dark-link-color: var(--ct-dropdown-dark-color);
  --ct-dropdown-dark-link-hover-color: #fff;
  --ct-dropdown-dark-link-hover-bg: rgba(255, 255, 255, 0.15);
  --ct-dropdown-dark-link-active-color: var(--ct-dropdown-link-active-color);
  --ct-dropdown-dark-link-active-bg: var(--ct-dropdown-link-active-bg);
  --ct-dropdown-dark-link-disabled-color: #8c98a5;
  --ct-dropdown-dark-header-color: #8c98a5;
}
body[data-theme=dark] .pagination {
  --ct-pagination-color: #f3f7f9;
  --ct-pagination-bg: #3b4651;
  --ct-pagination-border-color: #424e5a;
  --ct-pagination-focus-color: #f3f7f9;
  --ct-pagination-focus-bg: #424e5a;
  --ct-pagination-focus-box-shadow: 0 0 0 0.15rem var(--ct-input-btn-focus-color);
  --ct-pagination-hover-color: #f3f7f9;
  --ct-pagination-hover-bg: #424e5a;
  --ct-pagination-hover-border-color: #475461;
  --ct-pagination-disabled-color: #cedeef;
  --ct-pagination-disabled-bg: #424e5a;
  --ct-pagination-disabled-border-color: #424e5a;
}
body[data-theme=dark] .card, body[data-theme=dark] .card-header, body[data-theme=dark] .timeline, body[data-theme=dark] .gal-box, body[data-theme=dark] #preloader {
  --ct-card-border-color: #36404a;
  --ct-card-box-shadow: var(--ct-box-shadow-sm);
  --ct-card-cap-bg: #424e5a;
  --ct-card-bg: #36404a;
}
body[data-theme=dark] .accordion {
  --ct-accordion-color: var(--ct-body-color);
  --ct-accordion-border-color: rgba(0, 0, 0, 0.125);
  --ct-accordion-button-active-bg: #f0eefc;
  --ct-accordion-button-active-color: #5c4fc7;
  --ct-accordion-button-focus-border-color: var(--ct-input-focus-border-color);
  --ct-accordion-button-focus-box-shadow: 0 0 0 0.15rem var(--ct-input-btn-focus-color);
}
body[data-theme=dark] .tooltip {
  --ct-tooltip-color: #fff;
  --ct-tooltip-bg: #000;
}
body[data-theme=dark] .popover {
  --ct-popover-bg: #3c4853;
  --ct-popover-border-color: #424e5a;
  --ct-popover-box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
  --ct-popover-header-bg: #323a46;
  --ct-popover-header-color: ;
  --ct-popover-body-color: #cedeef;
  --ct-popover-arrow-color: #3c4853;
  --ct-popover-arrow-outer-color: #4c5a67;
}
body[data-theme=dark] .toast {
  --ct-toast-background-color: rgba(54, 64, 74, 0.85);
  --ct-toast-border-color: rgba(0, 0, 0, 0.1);
  --ct-toast-box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  --ct-toast-header-color: #cedeef;
  --ct-toast-header-background-color: rgba(50, 58, 70, 0.85);
  --ct-toast-header-border-color: rgba(0, 0, 0, 0.05);
}
body[data-theme=dark] .badge {
  --ct-badge-color: #fff;
}
body[data-theme=dark] .modal, body[data-theme=dark] .modal-backdrop, body[data-theme=dark] .offcanvas, body[data-theme=dark] .offcanvas-backdrop {
  --ct-modal-content-bg: #36404a;
  --ct-modal-content-box-shadow-xs: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.5);
  --ct-modal-content-box-shadow-sm-up: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
  --ct-modal-backdrop-bg: #f3f7f9;
  --ct-modal-header-border-color: #414d59;
  --ct-modal-footer-border-color: #414d59;
}
body[data-theme=dark] .offcanvas, body[data-theme=dark] .offcanvas-xxl, body[data-theme=dark] .offcanvas-xl, body[data-theme=dark] .offcanvas-lg, body[data-theme=dark] .offcanvas-md, body[data-theme=dark] .offcanvas-sm {
  --ct-offcanvas-bg: #3a444e;
  --ct-offcanvas-border-color: transparent;
}
body[data-theme=dark] .progress, body[data-theme=dark] .irs {
  --ct-progress-bg: #424e5a;
  --ct-progress-box-shadow: var(--ct-box-shadow-inset);
  --ct-progress-bar-color: #fff;
  --ct-progress-bar-bg: #6658dd;
}
body[data-theme=dark] .list-group {
  --ct-list-group-color: ;
  --ct-list-group-bg: #36404a;
  --ct-list-group-border-color: #424e5a;
  --ct-list-group-hover-bg: #3b4651;
  --ct-list-group-disabled-color: #cedeef;
  --ct-list-group-disabled-bg: var(--ct-list-group-bg);
  --ct-list-group-action-color: #8c98a5;
  --ct-list-group-action-active-color: var(--ct-body-color);
  --ct-list-group-action-active-bg: #36404a;
}

.custom-accordion .accordion-arrow {
  font-size: 1.2rem;
  position: absolute;
  right: 0;
}
.custom-accordion a.collapsed i.accordion-arrow:before {
  content: "\f0142";
}

.alert-primary {
  --ct-alert-color: #473e9b;
  --ct-alert-bg: rgba(102, 88, 221, 0.18);
  --ct-alert-border-color: rgba(102, 88, 221, 0.18);
}
.alert-primary .alert-link {
  --ct-alert-color: #473e9b;
}

.alert-secondary {
  --ct-alert-color: #4c5258;
  --ct-alert-bg: rgba(108, 117, 125, 0.18);
  --ct-alert-border-color: rgba(108, 117, 125, 0.18);
}
.alert-secondary .alert-link {
  --ct-alert-color: #4c5258;
}

.alert-success {
  --ct-alert-color: #12846d;
  --ct-alert-bg: rgba(26, 188, 156, 0.18);
  --ct-alert-border-color: rgba(26, 188, 156, 0.18);
}
.alert-success .alert-link {
  --ct-alert-color: #12846d;
}

.alert-info {
  --ct-alert-color: #378b9e;
  --ct-alert-bg: rgba(79, 198, 225, 0.18);
  --ct-alert-border-color: rgba(79, 198, 225, 0.18);
}
.alert-info .alert-link {
  --ct-alert-color: #378b9e;
}

.alert-warning {
  --ct-alert-color: #ad8135;
  --ct-alert-bg: rgba(247, 184, 75, 0.18);
  --ct-alert-border-color: rgba(247, 184, 75, 0.18);
}
.alert-warning .alert-link {
  --ct-alert-color: #ad8135;
}

.alert-danger {
  --ct-alert-color: #a93c4c;
  --ct-alert-bg: rgba(241, 85, 108, 0.18);
  --ct-alert-border-color: rgba(241, 85, 108, 0.18);
}
.alert-danger .alert-link {
  --ct-alert-color: #a93c4c;
}

.alert-light {
  --ct-alert-color: #a5a7a9;
  --ct-alert-bg: rgba(236, 239, 241, 0.18);
  --ct-alert-border-color: rgba(236, 239, 241, 0.18);
}
.alert-light .alert-link {
  --ct-alert-color: #a5a7a9;
}

.alert-dark {
  --ct-alert-color: #232931;
  --ct-alert-bg: rgba(50, 58, 70, 0.18);
  --ct-alert-border-color: rgba(50, 58, 70, 0.18);
}
.alert-dark .alert-link {
  --ct-alert-color: #232931;
}

.alert-pink {
  --ct-alert-color: #ac5075;
  --ct-alert-bg: rgba(246, 114, 167, 0.18);
  --ct-alert-border-color: rgba(246, 114, 167, 0.18);
}
.alert-pink .alert-link {
  --ct-alert-color: #ac5075;
}

.alert-blue {
  --ct-alert-color: #345a94;
  --ct-alert-bg: rgba(74, 129, 212, 0.18);
  --ct-alert-border-color: rgba(74, 129, 212, 0.18);
}
.alert-blue .alert-link {
  --ct-alert-color: #345a94;
}

.badge {
  -webkit-box-shadow: var(--ct-components-shadow);
          box-shadow: var(--ct-components-shadow);
}

.badge-soft-primary {
  color: #6658dd;
  background-color: rgba(102, 88, 221, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-primary {
  color: #6658dd;
  border: 1px solid #6658dd;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-secondary {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-secondary {
  color: #6c757d;
  border: 1px solid #6c757d;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-success {
  color: #1abc9c;
  background-color: rgba(26, 188, 156, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-success {
  color: #1abc9c;
  border: 1px solid #1abc9c;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-info {
  color: #4fc6e1;
  background-color: rgba(79, 198, 225, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-info {
  color: #4fc6e1;
  border: 1px solid #4fc6e1;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-warning {
  color: #f7b84b;
  background-color: rgba(247, 184, 75, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-warning {
  color: #f7b84b;
  border: 1px solid #f7b84b;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-danger {
  color: #f1556c;
  background-color: rgba(241, 85, 108, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-danger {
  color: #f1556c;
  border: 1px solid #f1556c;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-light {
  color: #eceff1;
  background-color: rgba(236, 239, 241, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-light {
  color: #eceff1;
  border: 1px solid #eceff1;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-dark {
  color: #323a46;
  background-color: rgba(50, 58, 70, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-dark {
  color: #323a46;
  border: 1px solid #323a46;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-pink {
  color: #f672a7;
  background-color: rgba(246, 114, 167, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-pink {
  color: #f672a7;
  border: 1px solid #f672a7;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-soft-blue {
  color: #4a81d4;
  background-color: rgba(74, 129, 212, 0.18);
  -webkit-box-shadow: none;
          box-shadow: none;
}

.badge-outline-blue {
  color: #4a81d4;
  border: 1px solid #4a81d4;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.breadcrumb-item > a {
  color: var(--ct-gray-700);
}
.breadcrumb-item + .breadcrumb-item::before {
  font-family: "Material Design Icons";
}

button:focus {
  outline: none;
}

.btn .mdi:before {
  margin-top: -1px;
}
.btn i {
  display: inline-block;
}

.btn-white,
.btn-outline-light {
  --ct-btn-color: var(--ct-gray-900);
  --ct-btn-hover-bg: var(--ct-gray-100);
  --ct-btn-border-color: var(--ct-gray-300);
  --ct-btn-hover-border-color: var(--ct-gray-100);
}

.btn-label {
  margin: -0.55rem 0.9rem -0.55rem -0.9rem;
  padding: 0.6rem 0.9rem;
  background-color: rgba(var(--ct-gray-900-rgb), 0.1);
}

.btn-label-right {
  margin: -0.45rem -0.9rem -0.45rem 0.9rem;
  padding: 0.45rem 0.9rem;
  background-color: rgba(var(--ct-gray-900-rgb), 0.1);
  display: inline-block;
}

.btn-xs {
  padding: 0.2rem 0.6rem;
  font-size: 0.75rem;
  border-radius: 0.15rem;
}

.btn-primary {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-primary-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-primary-rgb), 0.5);
}

.btn-secondary {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-secondary-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-secondary-rgb), 0.5);
}

.btn-success {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-success-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-success-rgb), 0.5);
}

.btn-info {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-info-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-info-rgb), 0.5);
}

.btn-warning {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-warning-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-warning-rgb), 0.5);
}

.btn-danger {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-danger-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-danger-rgb), 0.5);
}

.btn-light {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-light-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-light-rgb), 0.5);
}

.btn-dark {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-dark-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-dark-rgb), 0.5);
}

.btn-pink {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-pink-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-pink-rgb), 0.5);
}

.btn-blue {
  -webkit-box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-blue-rgb), 0.5);
          box-shadow: var(--ct-btn-box-shadow) rgba(var(--ct-blue-rgb), 0.5);
}

.btn-soft-primary {
  --ct-btn-color: var(--ct-primary);
  --ct-btn-bg: rgba(var(--ct-primary-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-primary-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-primary);
  --ct-btn-hover-border-color: var(--ct-primary);
  --ct-btn-focus-shadow-rgb: var(--ct-primary-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-primary);
  --ct-btn-active-border-color: var(--ct-primary);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-primary);
}

.btn-soft-secondary {
  --ct-btn-color: var(--ct-secondary);
  --ct-btn-bg: rgba(var(--ct-secondary-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-secondary-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-secondary);
  --ct-btn-hover-border-color: var(--ct-secondary);
  --ct-btn-focus-shadow-rgb: var(--ct-secondary-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-secondary);
  --ct-btn-active-border-color: var(--ct-secondary);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-secondary);
}

.btn-soft-success {
  --ct-btn-color: var(--ct-success);
  --ct-btn-bg: rgba(var(--ct-success-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-success-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-success);
  --ct-btn-hover-border-color: var(--ct-success);
  --ct-btn-focus-shadow-rgb: var(--ct-success-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-success);
  --ct-btn-active-border-color: var(--ct-success);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-success);
}

.btn-soft-info {
  --ct-btn-color: var(--ct-info);
  --ct-btn-bg: rgba(var(--ct-info-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-info-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-info);
  --ct-btn-hover-border-color: var(--ct-info);
  --ct-btn-focus-shadow-rgb: var(--ct-info-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-info);
  --ct-btn-active-border-color: var(--ct-info);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-info);
}

.btn-soft-warning {
  --ct-btn-color: var(--ct-warning);
  --ct-btn-bg: rgba(var(--ct-warning-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-warning-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-warning);
  --ct-btn-hover-border-color: var(--ct-warning);
  --ct-btn-focus-shadow-rgb: var(--ct-warning-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-warning);
  --ct-btn-active-border-color: var(--ct-warning);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-warning);
}

.btn-soft-danger {
  --ct-btn-color: var(--ct-danger);
  --ct-btn-bg: rgba(var(--ct-danger-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-danger-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-danger);
  --ct-btn-hover-border-color: var(--ct-danger);
  --ct-btn-focus-shadow-rgb: var(--ct-danger-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-danger);
  --ct-btn-active-border-color: var(--ct-danger);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-danger);
}

.btn-soft-light {
  --ct-btn-color: var(--ct-light);
  --ct-btn-bg: rgba(var(--ct-light-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-light-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-light);
  --ct-btn-hover-border-color: var(--ct-light);
  --ct-btn-focus-shadow-rgb: var(--ct-light-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-light);
  --ct-btn-active-border-color: var(--ct-light);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-light);
}

.btn-soft-dark {
  --ct-btn-color: var(--ct-dark);
  --ct-btn-bg: rgba(var(--ct-dark-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-dark-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-dark);
  --ct-btn-hover-border-color: var(--ct-dark);
  --ct-btn-focus-shadow-rgb: var(--ct-dark-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-dark);
  --ct-btn-active-border-color: var(--ct-dark);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-dark);
}

.btn-soft-pink {
  --ct-btn-color: var(--ct-pink);
  --ct-btn-bg: rgba(var(--ct-pink-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-pink-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-pink);
  --ct-btn-hover-border-color: var(--ct-pink);
  --ct-btn-focus-shadow-rgb: var(--ct-pink-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-pink);
  --ct-btn-active-border-color: var(--ct-pink);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-pink);
}

.btn-soft-blue {
  --ct-btn-color: var(--ct-blue);
  --ct-btn-bg: rgba(var(--ct-blue-rgb), 0.18);
  --ct-btn-border-color: rgba(var(--ct-blue-rgb), 0.12);
  --ct-btn-hover-color: #fff;
  --ct-btn-hover-bg: var(--ct-blue);
  --ct-btn-hover-border-color: var(--ct-blue);
  --ct-btn-focus-shadow-rgb: var(--ct-blue-rgb);
  --ct-btn-active-color: #fff;
  --ct-btn-active-bg: var(--ct-blue);
  --ct-btn-active-border-color: var(--ct-blue);
  --ct-btn-active-shadow: var(--ct-btn-active-box-shadow);
  --ct-btn-disabled-color: var(--ct-blue);
}

body[data-theme=dark] .btn-light {
  --ct-btn-color: var(--ct-gray-900);
  --ct-btn-bg: var(--ct-gray-300);
  --ct-btn-border-color: var(--ct-gray-300);
  --ct-btn-hover-color: var(--ct-gray-900);
  --ct-btn-hover-bg: var(--ct-gray-300);
  --ct-btn-hover-border-color: var(--ct-gray-300);
  --ct-btn-active-color: var(--ct-gray-900);
  --ct-btn-active-bg: var(--ct-gray-300);
  --ct-btn-active-border-color: var(--ct-gray-300);
}
body[data-theme=dark] .btn-outline-dark {
  --ct-btn-color: var(--ct-gray-900);
}
body[data-theme=dark] .btn-outline-light,
body[data-theme=dark] .btn-outline-dark {
  --ct-btn-hover-color: var(--ct-gray-900);
  --ct-btn-color: var(--ct-gray-900);
}
body[data-theme=dark] .btn-soft-dark {
  --ct-btn-hover-bg: var(--ct-light);
  --ct-btn-hover-border-color: var(--ct-light);
}

.card {
  -webkit-box-shadow: var(--ct-card-box-shadow);
          box-shadow: var(--ct-card-box-shadow);
  margin-bottom: 24px;
}

.card-drop {
  font-size: 20px;
  line-height: 0;
  color: inherit;
}

.card-widgets {
  float: right;
  height: 16px;
}
.card-widgets > a {
  color: inherit;
  font-size: 18px;
  display: inline-block;
  line-height: 1;
}
.card-widgets > a.collapsed i:before {
  content: "\f0415";
}

.card-title,
.card-header {
  margin-top: 0;
}

.card-disabled {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 0.25rem;
  background: var(--ct-card-overlay-bg);
  cursor: progress;
}
.card-disabled .card-portlets-loader {
  background-color: var(--ct-card-overlay-color);
  -webkit-animation: rotatebox 1.2s infinite ease-in-out;
          animation: rotatebox 1.2s infinite ease-in-out;
  height: 30px;
  width: 30px;
  position: absolute;
  left: 50%;
  top: 50%;
  border-radius: 3px;
  margin-left: -12px;
  margin-top: -12px;
}

@-webkit-keyframes rotatebox {
  0% {
    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
            transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
            transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
            transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}

@keyframes rotatebox {
  0% {
    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
            transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
            transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
            transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}
.header-title {
  font-size: 1rem;
  margin: 0 0 7px 0;
}

.sub-header {
  font-size: 0.875rem;
  margin-bottom: 24px;
  color: var(--ct-text-muted);
}

.dropdown-menu {
  padding: 0.3rem;
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  /*rtl:remove*/
  -webkit-animation-name: DropDownSlide;
          animation-name: DropDownSlide;
  /*rtl:remove*/
  -webkit-animation-duration: 0.3s;
          animation-duration: 0.3s;
  /*rtl:remove*/
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  top: 100%;
}
.dropdown-menu.show {
  /*rtl:remove*/
  top: 100% !important;
}
.dropdown-menu i {
  display: inline-block;
}

.dropdown-menu-end {
  right: 0 !important;
  left: auto !important;
}

.dropdown-menu[data-popper-placement^=right],
.dropdown-menu[data-popper-placement^=top],
.dropdown-menu[data-popper-placement^=left] {
  top: auto !important;
  -webkit-animation: none !important;
          animation: none !important;
}

@-webkit-keyframes DropDownSlide {
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  0% {
    -webkit-transform: translateY(15px);
            transform: translateY(15px);
  }
}

@keyframes DropDownSlide {
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  0% {
    -webkit-transform: translateY(15px);
            transform: translateY(15px);
  }
}
@media (min-width: 600px) {
  .dropdown-lg {
    width: 320px;
  }
}
.dropdown-mega {
  position: static !important;
}

.dropdown-megamenu {
  padding: 20px;
  left: 20px !important;
  right: 20px !important;
  background-image: url("../images/megamenu-bg.png");
  background-position: right bottom;
  background-repeat: no-repeat;
}

html[dir=rtl] .dropdown-megamenu {
  left: 60px !important;
  right: -20px !important;
}

.megamenu-list li {
  padding: 5px 20px 5px 25px;
  position: relative;
}
.megamenu-list li a {
  color: var(--ct-dropdown-link-color);
}
.megamenu-list li a:hover {
  color: #6658dd;
}
.megamenu-list li:before {
  content: "\f0142";
  position: absolute;
  left: 0;
  font-family: "Material Design Icons";
}

html[dir=ltr] .megamenu-list li:before {
  content: "\f0141";
}

.dropdown-icon-item {
  display: block;
  border-radius: 3px;
  line-height: 34px;
  text-align: center;
  padding: 15px 0 9px;
  display: block;
  border: 1px solid transparent;
  color: var(--ct-dropdown-link-color);
}
.dropdown-icon-item img {
  height: 24px;
}
.dropdown-icon-item span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dropdown-icon-item:hover {
  background-color: var(--ct-dropdown-link-hover-bg);
}

@media (min-width: 992px) {
  .dropdown-mega-menu-xl {
    width: 40rem;
  }
  .dropdown-mega-menu-lg {
    width: 26rem;
  }
}
label {
  font-weight: 500;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
label.form-check-label {
  cursor: pointer;
}

.form-control,
.form-select {
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
}

.form-control-light {
  background-color: var(--ct-input-light-bg) !important;
  border-color: var(--ct-input-light-bg) !important;
}

input.form-control[type=color],
input.form-control[type=range] {
  min-height: 39px;
}

.custom-select.is-invalid:focus,
.form-control.is-invalid:focus,
.custom-select:invalid:focus,
.form-control:invalid:focus,
.custom-select.is-valid:focus,
.form-control.is-valid:focus,
.custom-select:valid:focus,
.form-control:valid:focus {
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.comment-area-box .form-control {
  border-color: var(--ct-gray-300);
  border-radius: 0.2rem 0.2rem 0 0;
}
.comment-area-box .comment-area-btn {
  background-color: var(--ct-gray-100);
  padding: 10px;
  border: 1px solid var(--ct-gray-300);
  border-top: none;
  border-radius: 0 0 0.2rem 0.2rem;
}

.search-bar .form-control {
  padding-left: 40px;
  padding-right: 20px;
  border-radius: 30px;
}
.search-bar span {
  position: absolute;
  z-index: 10;
  font-size: 16px;
  line-height: calc(1.5em + 0.9rem + 2px);
  left: 13px;
  top: -2px;
  color: var(--ct-text-muted);
}

.password-eye:before {
  font-family: feather !important;
  content: "\e86a";
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  vertical-align: middle;
  line-height: 1.2;
  font-size: 16px;
}

.show-password .password-eye:before {
  content: "\e86e";
}

.modal-title {
  margin-top: 0;
}

.modal-full-width {
  width: 95%;
  max-width: none;
}

.modal-top {
  margin: 0 auto;
}

.modal-right {
  position: absolute;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  height: 100%;
  margin: 0;
  background-color: var(--ct-modal-content-bg);
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-transform: translate(25%, 0) !important;
          transform: translate(25%, 0) !important;
}
.modal-right button.btn-close {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1;
}

.modal.show .modal-right,
.modal.show .modal-left {
  -webkit-transform: translate(0, 0) !important;
          transform: translate(0, 0) !important;
}

.modal-bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-flow: column nowrap;
          flex-flow: column nowrap;
  -ms-flex-pack: end;
  -webkit-box-pack: end;
          justify-content: flex-end;
  height: 100%;
  margin: 0 auto;
  -ms-flex-line-pack: center;
      align-content: center;
}

.modal-colored-header {
  color: #fff;
  border-radius: 0;
}
.modal-colored-header .btn-close {
  color: #fff !important;
}

.nav-tabs > li > a,
.nav-pills > li > a {
  color: var(--ct-gray-700);
  font-weight: 500;
}

.nav-pills > a {
  color: var(--ct-gray-700);
  font-weight: 500;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: auto;
}

.navtab-bg .nav-link {
  background-color: var(--ct-card-cap-bg);
  margin: 0 5px;
}

.nav-bordered {
  border-bottom: 2px solid rgba(var(--ct-gray-600-rgb), 0.2) !important;
}
.nav-bordered .nav-item {
  margin-bottom: -1px;
}
.nav-bordered li a {
  border: 0 !important;
  padding: 10px 20px;
}
.nav-bordered a.active {
  border-bottom: 2px solid #6658dd !important;
}

.tab-content {
  padding: 20px 0 0 0;
}

.pagination-rounded .page-link {
  border-radius: 30px !important;
  margin: 0 6px;
  border: none;
}
.pagination-rounded .page-item:last-child .page-link {
  margin-right: 0px;
}

.pagination a {
  line-height: 1.25 !important;
}

.popover {
  position: absolute;
}
.popover .popover-header {
  margin-top: 0;
}

.progress-sm {
  height: 5px;
}

.progress-md {
  height: 8px;
}

.progress-lg {
  height: 12px;
}

.progress-xl {
  height: 15px;
}

/* Progressbar Vertical */
.progress-vertical {
  min-height: 250px;
  height: 250px;
  width: 10px;
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}
.progress-vertical .progress-bar {
  width: 100%;
}
.progress-vertical.progress-xl {
  width: 15px;
}
.progress-vertical.progress-lg {
  width: 12px;
}
.progress-vertical.progress-md {
  width: 8px;
}
.progress-vertical.progress-sm {
  width: 5px;
}

.progress-vertical-bottom {
  min-height: 250px;
  height: 250px;
  position: relative;
  width: 10px;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}
.progress-vertical-bottom .progress-bar {
  width: 100%;
  bottom: 0;
  position: absolute;
}
.progress-vertical-bottom.progress-xl {
  width: 15px;
}
.progress-vertical-bottom.progress-lg {
  width: 12px;
}
.progress-vertical-bottom.progress-md {
  width: 8px;
}
.progress-vertical-bottom.progress-sm {
  width: 5px;
}

body {
  padding-right: 0px !important;
  padding-left: 0px !important;
}

th {
  font-weight: 500;
}

.table-centered td,
.table-centered th {
  vertical-align: middle !important;
}

.table-nowrap th,
.table-nowrap td {
  white-space: nowrap;
}

.table > :not(:first-child) {
  border-top: 2px solid var(--ct-table-group-separator-color);
}
.table .table-user img {
  height: 30px;
  width: 30px;
}
.table.table-bordered tbody {
  border-top: 1px solid;
  border-top-color: inherit;
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.action-icon {
  color: var(--ct-gray-600);
  font-size: 1.2rem;
  display: inline-block;
  padding: 0 3px;
}
.action-icon:hover {
  color: var(--ct-gray-700);
}

body[data-theme=dark] .table-light {
  --ct-table-bg: #3e4954;
  color: #fff;
  --ct-table-border-color: var(--ct-table-group-separator-color);
}
body[data-theme=dark] .table-dark {
  --ct-table-bg: var(--ct-light);
}
body[data-theme=dark] .table-dark tbody,
body[data-theme=dark] .table-dark tr {
  border-color: var(--ct-gray-300);
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 10px 0;
  font-family: "Roboto", sans-serif;
}

.font-10 {
  font-size: 10px !important;
}

.font-11 {
  font-size: 11px !important;
}

.font-12 {
  font-size: 12px !important;
}

.font-13 {
  font-size: 13px !important;
}

.font-14 {
  font-size: 14px !important;
}

.font-15 {
  font-size: 15px !important;
}

.font-16 {
  font-size: 16px !important;
}

.font-17 {
  font-size: 17px !important;
}

.font-18 {
  font-size: 18px !important;
}

.font-19 {
  font-size: 19px !important;
}

.font-20 {
  font-size: 20px !important;
}

.font-21 {
  font-size: 21px !important;
}

.font-22 {
  font-size: 22px !important;
}

.font-23 {
  font-size: 23px !important;
}

.font-24 {
  font-size: 24px !important;
}

.font-36 {
  font-size: 36px !important;
}

.font-48 {
  font-size: 48px !important;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.bg-soft-primary {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-primary-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-secondary {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-secondary-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-success {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-success-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-info {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-info-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-warning {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-warning-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-danger {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-danger-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-light {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-light-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-dark {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-dark-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-pink {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-pink-rgb), var(--ct-bg-opacity)) !important;
}

.bg-soft-blue {
  --ct-bg-opacity: 0.25;
  background-color: rgba(var(--ct-blue-rgb), var(--ct-bg-opacity)) !important;
}

.bg-ghost {
  --ct-bg-opacity: 0.4;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  overflow-x: hidden;
}

/*rtl:options:
{
    "aliases": {
        "left-side-menu": "left-side-menu"
    }
}*/
#wrapper {
  height: 100%;
  overflow: hidden;
  width: 100%;
}

.content-page {
  margin-left: 240px;
  overflow: hidden;
  padding: 0 15px 65px 15px;
  min-height: 80vh;
  margin-top: 70px;
}

.left-side-menu {
  width: 240px;
  background: var(--ct-bg-leftbar);
  bottom: 0;
  padding: 20px 0;
  position: fixed;
  -webkit-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
  top: 70px;
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
}

#sidebar-menu > ul {
  list-style: none;
  padding: 0;
}
#sidebar-menu > ul > li > a {
  color: var(--ct-menu-item);
  display: block;
  padding: 12px 20px;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-family: "Roboto", sans-serif;
  font-size: 0.95rem;
}
#sidebar-menu > ul > li > a:hover, #sidebar-menu > ul > li > a:focus, #sidebar-menu > ul > li > a:active {
  color: var(--ct-menu-item-hover);
  text-decoration: none;
}
#sidebar-menu > ul > li > a > span {
  vertical-align: middle;
}
#sidebar-menu > ul > li > a i {
  display: inline-block;
  line-height: 1.0625rem;
  margin: 0 10px 0 3px;
  text-align: center;
  vertical-align: middle;
  width: 16px;
  font-size: 18px;
}
#sidebar-menu > ul > li > a svg {
  width: 16px;
  height: 16px;
  margin-left: 3px;
  margin-right: 10px;
}
#sidebar-menu > ul > li > a .drop-arrow {
  float: right;
}
#sidebar-menu > ul > li > a .drop-arrow i {
  margin-right: 0;
}
#sidebar-menu > ul > li > a.mm-active {
  color: var(--ct-menu-item-active);
}
#sidebar-menu > ul > li ul {
  padding-left: 34px;
  list-style: none;
}
#sidebar-menu > ul > li ul ul {
  padding-left: 20px;
}
#sidebar-menu .badge {
  margin-top: 4px;
}
#sidebar-menu .menu-title {
  padding: 10px 20px;
  letter-spacing: 0.05em;
  pointer-events: none;
  cursor: default;
  font-size: 0.6875rem;
  text-transform: uppercase;
  color: var(--ct-menu-item);
  font-weight: 500;
}
#sidebar-menu .menuitem-active > a {
  color: var(--ct-menu-item-active);
}
#sidebar-menu .menuitem-active .active {
  color: var(--ct-menu-item-active);
}

.nav-second-level li a {
  padding: 8px 20px;
  color: var(--ct-menu-item);
  display: block;
  position: relative;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  font-size: 0.875rem;
}
.nav-second-level li a:focus, .nav-second-level li a:hover {
  color: var(--ct-menu-item-hover);
}
.nav-second-level li.active > a {
  color: var(--ct-menu-item-active);
}

.menu-arrow {
  -webkit-transition: -webkit-transform 0.15s;
  transition: -webkit-transform 0.15s;
  transition: transform 0.15s;
  transition: transform 0.15s, -webkit-transform 0.15s;
  position: absolute;
  right: 20px;
  display: inline-block;
  font-family: "Material Design Icons";
  text-rendering: auto;
  line-height: 1.5rem;
  font-size: 1.1rem;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.menu-arrow:before {
  content: "\f0142";
}

[dir=rtl] .menu-arrow:before {
  content: "\f0141";
}

li > a[aria-expanded=true] > span.menu-arrow {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
li.menuitem-active > a:not(.collapsed) > span.menu-arrow {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

body[data-leftbar-size=condensed] .logo-box {
  width: 70px !important;
}
body[data-leftbar-size=condensed] .logo span.logo-lg {
  display: none;
}
body[data-leftbar-size=condensed] .logo span.logo-sm {
  display: block;
}
body[data-leftbar-size=condensed] .left-side-menu {
  position: absolute;
  padding-top: 0;
  width: 70px !important;
  z-index: 5;
}
body[data-leftbar-size=condensed] .left-side-menu .simplebar-mask,
body[data-leftbar-size=condensed] .left-side-menu .simplebar-content-wrapper {
  overflow: visible !important;
}
body[data-leftbar-size=condensed] .left-side-menu .simplebar-scrollbar {
  display: none !important;
}
body[data-leftbar-size=condensed] .left-side-menu .simplebar-offset {
  bottom: 0 !important;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu .menu-title,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu .menu-arrow,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu .label,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu .badge {
  display: none !important;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu .nav.collapse {
  height: inherit !important;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li {
  position: relative;
  white-space: nowrap;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a {
  padding: 15px 20px;
  min-height: 54px;
  -webkit-transition: none;
  transition: none;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a:hover, body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a:active, body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a:focus {
  color: var(--ct-menu-item-hover);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a i {
  font-size: 1.3rem;
  margin-right: 20px;
  margin-left: 5px;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a svg {
  width: 18px;
  height: 18px;
  margin-left: 6px;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li > a span {
  display: none;
  padding-left: 25px;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > a {
  position: relative;
  width: calc(190px + 70px);
  color: var(--ct-menu-item-active);
  background-color: var(--ct-gray-100);
  -webkit-box-shadow: inset var(--ct-box-shadow-condensed);
          box-shadow: inset var(--ct-box-shadow-condensed);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > a span {
  display: inline;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover a.open :after,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover a.active :after {
  display: none;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapse,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapsing {
  display: block !important;
  height: auto !important;
  -webkit-transition: none !important;
  transition: none !important;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapse > ul,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapsing > ul {
  display: block !important;
  left: 70px;
  position: absolute;
  width: 190px;
  -webkit-box-shadow: var(--ct-box-shadow-condensed);
          box-shadow: var(--ct-box-shadow-condensed);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapse > ul ul,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapsing > ul ul {
  -webkit-box-shadow: var(--ct-box-shadow-condensed);
          box-shadow: var(--ct-box-shadow-condensed);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapse > ul a,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapsing > ul a {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 8px 20px;
  position: relative;
  width: 190px;
  z-index: 6;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapse > ul a:hover,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul > li:hover > .collapsing > ul a:hover {
  color: var(--ct-menu-item-hover);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul {
  padding: 5px 0;
  z-index: 9999;
  display: none;
  background-color: var(--ct-bg-leftbar);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li:hover > .collapse,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li:hover > .collapsing {
  display: block !important;
  height: auto !important;
  -webkit-transition: none !important;
  transition: none !important;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li:hover > .collapse > ul,
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li:hover > .collapsing > ul {
  display: block;
  left: 190px;
  margin-top: -36px;
  position: absolute;
  width: 190px;
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li > a span.pull-right {
  position: absolute;
  right: 20px;
  top: 12px;
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}
body[data-leftbar-size=condensed] .left-side-menu #sidebar-menu > ul ul li.active a {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-size=condensed] .content-page {
  margin-left: 70px !important;
}
@media (min-width: 992px) {
  body[data-leftbar-size=condensed] .footer {
    left: 70px !important;
  }
}
body[data-leftbar-size=condensed] .user-box {
  display: none !important;
}

@media (min-width: 768px) {
  body[data-leftbar-size=condensed]:not([data-layout=compact]) {
    min-height: 1750px;
  }
}
@media (max-width: 767.98px) {
  .pro-user-name {
    display: none;
  }
}
@media (max-width: 991.98px) {
  body {
    overflow-x: hidden;
    padding-bottom: 80px;
  }
  .left-side-menu {
    display: none;
    z-index: 10 !important;
  }
  .sidebar-enable .left-side-menu {
    display: block;
  }
  .content-page,
body[data-leftbar-size=condensed] .content-page {
    margin-left: 0 !important;
    padding: 0 10px;
  }
  .footer {
    left: 0 !important;
  }
}
/* =============
  Small Menu
============= */
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .logo-box {
  width: 160px !important;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu {
  width: 160px !important;
  text-align: center;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu #sidebar-menu > ul > li > a > i {
  display: block;
  font-size: 18px;
  line-height: 24px;
  width: 100%;
  margin: 0;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu #sidebar-menu > ul > li > a svg {
  display: block;
  margin: 0 auto 5px auto;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu #sidebar-menu > ul ul {
  padding-left: 0;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu #sidebar-menu > ul ul a {
  padding: 10px 20px;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu .menu-arrow,
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu .badge {
  display: none !important;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu + .content-page {
  margin-left: 160px;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu + .content-page .footer {
  left: 160px;
}
body[data-leftbar-size=compact]:not([data-leftbar-size=condensed]) .left-side-menu .menu-title {
  background-color: var(--ct-gray-100);
}

body[data-leftbar-color=dark]:not([data-layout-mode=detached]) .logo-box {
  background-color: var(--ct-bg-leftbar-dark);
}
body[data-leftbar-color=dark]:not([data-layout-mode=detached]) .logo-box .logo-dark {
  display: none;
}
body[data-leftbar-color=dark]:not([data-layout-mode=detached]) .logo-box .logo-light {
  display: block;
}

body[data-leftbar-color=dark] .left-side-menu {
  background-color: var(--ct-bg-leftbar-dark);
  -webkit-box-shadow: none;
          box-shadow: none;
}
body[data-leftbar-color=dark] .left-side-menu #sidebar-menu > ul > li > a {
  color: var(--ct-menu-item-color-dark);
}
body[data-leftbar-color=dark] .left-side-menu #sidebar-menu > ul > li > a:hover, body[data-leftbar-color=dark] .left-side-menu #sidebar-menu > ul > li > a:focus, body[data-leftbar-color=dark] .left-side-menu #sidebar-menu > ul > li > a:active {
  color: var(--ct-menu-item-hover-color-dark);
}
body[data-leftbar-color=dark] .left-side-menu #sidebar-menu .menu-title {
  color: var(--ct-gray-500);
}
body[data-leftbar-color=dark] .left-side-menu #sidebar-menu .menuitem-active > a {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-color=dark] .left-side-menu #sidebar-menu .menuitem-active .active {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-color=dark] .left-side-menu .nav-second-level li a,
body[data-leftbar-color=dark] .left-side-menu .nav-thrid-level li a {
  color: var(--ct-menu-item-color-dark);
}
body[data-leftbar-color=dark] .left-side-menu .nav-second-level li a:focus, body[data-leftbar-color=dark] .left-side-menu .nav-second-level li a:hover,
body[data-leftbar-color=dark] .left-side-menu .nav-thrid-level li a:focus,
body[data-leftbar-color=dark] .left-side-menu .nav-thrid-level li a:hover {
  background-color: transparent;
  color: var(--ct-menu-item-hover-color-dark);
}
body[data-leftbar-color=dark] .left-side-menu .nav-second-level li.active > a,
body[data-leftbar-color=dark] .left-side-menu .nav-thrid-level li.active > a {
  color: var(--ct-menu-item-active-color-dark);
}
body[data-leftbar-color=dark] .left-side-menu .user-box .dropdown > a {
  color: #fff !important;
}
body[data-leftbar-color=dark][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {
  background-color: var(--ct-bg-leftbar-dark);
  -webkit-box-shadow: none;
          box-shadow: none;
}
body[data-leftbar-color=dark][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu .mm-active .active {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-color=dark][data-leftbar-size=compact] #wrapper .left-side-menu .menu-title {
  background-color: rgba(255, 255, 255, 0.03);
}

body[data-leftbar-color=brand] .logo-box,
body[data-leftbar-color=gradient] .logo-box {
  background-color: var(--ct-bg-leftbar-brand);
}
body[data-leftbar-color=brand] .menuitem-active > a,
body[data-leftbar-color=gradient] .menuitem-active > a {
  color: #fff !important;
}
body[data-leftbar-color=brand] .left-side-menu,
body[data-leftbar-color=gradient] .left-side-menu {
  background-color: var(--ct-bg-leftbar-brand);
  -webkit-box-shadow: none;
          box-shadow: none;
}
body[data-leftbar-color=brand] .left-side-menu #sidebar-menu > ul > li > a,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu > ul > li > a {
  color: rgba(255, 255, 255, 0.7);
}
body[data-leftbar-color=brand] .left-side-menu #sidebar-menu > ul > li > a:hover, body[data-leftbar-color=brand] .left-side-menu #sidebar-menu > ul > li > a:focus, body[data-leftbar-color=brand] .left-side-menu #sidebar-menu > ul > li > a:active,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu > ul > li > a:hover,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu > ul > li > a:focus,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu > ul > li > a:active {
  color: rgba(255, 255, 255, 0.9);
}
body[data-leftbar-color=brand] .left-side-menu #sidebar-menu > ul > li > a.mm-active,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu > ul > li > a.mm-active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.07);
}
body[data-leftbar-color=brand] .left-side-menu #sidebar-menu .menu-title,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu .menu-title {
  color: rgba(255, 255, 255, 0.6);
}
body[data-leftbar-color=brand] .left-side-menu #sidebar-menu .mm-active .active,
body[data-leftbar-color=gradient] .left-side-menu #sidebar-menu .mm-active .active {
  color: #fff;
}
body[data-leftbar-color=brand] .left-side-menu .nav-second-level li a,
body[data-leftbar-color=brand] .left-side-menu .nav-thrid-level li a,
body[data-leftbar-color=gradient] .left-side-menu .nav-second-level li a,
body[data-leftbar-color=gradient] .left-side-menu .nav-thrid-level li a {
  color: rgba(255, 255, 255, 0.7);
}
body[data-leftbar-color=brand] .left-side-menu .nav-second-level li a:focus, body[data-leftbar-color=brand] .left-side-menu .nav-second-level li a:hover,
body[data-leftbar-color=brand] .left-side-menu .nav-thrid-level li a:focus,
body[data-leftbar-color=brand] .left-side-menu .nav-thrid-level li a:hover,
body[data-leftbar-color=gradient] .left-side-menu .nav-second-level li a:focus,
body[data-leftbar-color=gradient] .left-side-menu .nav-second-level li a:hover,
body[data-leftbar-color=gradient] .left-side-menu .nav-thrid-level li a:focus,
body[data-leftbar-color=gradient] .left-side-menu .nav-thrid-level li a:hover {
  background-color: transparent;
  color: #fff;
}
body[data-leftbar-color=brand] .left-side-menu .nav-second-level li.active > a,
body[data-leftbar-color=brand] .left-side-menu .nav-thrid-level li.active > a,
body[data-leftbar-color=gradient] .left-side-menu .nav-second-level li.active > a,
body[data-leftbar-color=gradient] .left-side-menu .nav-thrid-level li.active > a {
  color: var(--ct-menu-item-active-color-dark);
}
body[data-leftbar-color=brand] .left-side-menu .user-box .dropdown > a,
body[data-leftbar-color=gradient] .left-side-menu .user-box .dropdown > a {
  color: #fff !important;
}
body[data-leftbar-color=brand][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {
  background-color: var(--ct-bg-leftbar-brand);
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #fff;
}
body[data-leftbar-color=brand][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu .mm-active .active,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu .mm-active .active {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-color=brand][data-leftbar-size=condensed] #wrapper .left-side-menu .menuitem-active .menuitem-active a.active,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu .menuitem-active .menuitem-active a.active {
  color: var(--ct-menu-item-active) !important;
}
body[data-leftbar-color=brand][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-second-level li a,
body[data-leftbar-color=brand][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-thrid-level li a,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-second-level li a,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-thrid-level li a {
  color: var(--ct-menu-item);
}
body[data-leftbar-color=brand][data-leftbar-size=compact] #wrapper .left-side-menu .menu-title,
body[data-leftbar-color=gradient][data-leftbar-size=compact] #wrapper .left-side-menu .menu-title {
  background-color: rgba(255, 255, 255, 0.05);
}

body[data-leftbar-color=gradient] .logo-box,
body[data-leftbar-color=gradient] .left-side-menu {
  background: var(--ct-bg-leftbar-gradient);
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(64, 149, 216, 0.15)), to(transparent));
  background-image: linear-gradient(270deg, rgba(64, 149, 216, 0.15), transparent);
}
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu > ul > li:hover > a {
  background: var(--ct-bg-leftbar-gradient);
}
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu #sidebar-menu .mm-active .active {
  color: var(--ct-menu-item-active);
}
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-second-level li a,
body[data-leftbar-color=gradient][data-leftbar-size=condensed] #wrapper .left-side-menu .nav-thrid-level li a {
  color: var(--ct-menu-item);
}

.user-box {
  display: none;
}

.user-pro-dropdown {
  background-color: var(--ct-gray-100);
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 15px 5px;
  width: 90%;
  margin-left: 5% !important;
  margin-top: 10px !important;
}
.user-pro-dropdown .dropdown-item {
  border-radius: 3px;
}
.user-pro-dropdown .dropdown-item:hover {
  background-color: #6658dd;
  color: #fff;
}

@media (min-width: 992px) {
  body[data-layout-mode=detached] .navbar-custom .container-fluid {
    max-width: 95%;
  }
  body[data-layout-mode=detached] #wrapper {
    max-width: 95%;
    margin: 0 auto;
  }
  body[data-layout-mode=detached] .left-side-menu {
    margin-top: 30px;
    margin-bottom: 30px;
    border-radius: 5px;
  }
  body[data-layout-mode=detached] .content-page {
    padding-bottom: 30px;
  }
  body[data-layout-mode=detached] .logo-box {
    background-color: transparent;
    background-image: none;
  }
}

body[data-sidebar-user=true] .user-box {
  display: block;
}

body[data-sidebar-icon=twotones] #sidebar-menu > ul > li > a i {
  color: #4a81d4;
}
body[data-sidebar-icon=twotones] #sidebar-menu > ul > li > a svg {
  width: 18px;
  height: 18px;
  margin-left: 3px;
  margin-right: 10px;
  color: #4a81d4;
  fill: rgba(74, 129, 212, 0.2);
}
body[data-sidebar-icon=twotones][data-leftbar-color=brand] #sidebar-menu > ul > li > a i, body[data-sidebar-icon=twotones][data-leftbar-color=gradient] #sidebar-menu > ul > li > a i {
  color: var(--ct-gray-100);
}
body[data-sidebar-icon=twotones][data-leftbar-color=brand] #sidebar-menu > ul > li > a svg, body[data-sidebar-icon=twotones][data-leftbar-color=gradient] #sidebar-menu > ul > li > a svg {
  color: var(--ct-gray-100);
  fill: rgba(var(--ct-gray-100-rgb), 0.2);
}

body[data-leftbar-color=brand]:not([data-layout-mode=detached]) .logo-box .logo-dark,
body[data-leftbar-color=gradient]:not([data-layout-mode=detached]) .logo-box .logo-dark {
  display: none;
}
body[data-leftbar-color=brand]:not([data-layout-mode=detached]) .logo-box .logo-light,
body[data-leftbar-color=gradient]:not([data-layout-mode=detached]) .logo-box .logo-light {
  display: block;
}

.logo {
  display: block;
}
.logo span.logo-lg {
  display: block;
}
.logo span.logo-sm {
  display: none;
}
.logo .logo-lg-text-dark {
  color: var(--ct-gray-900);
  font-weight: 700;
  font-size: 22px;
  text-transform: uppercase;
}
.logo .logo-lg-text-light {
  color: #fff;
  font-weight: 700;
  font-size: 22px;
  text-transform: uppercase;
}

.logo-box {
  height: 70px;
  width: 240px;
  float: left;
  -webkit-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
}
.logo-box .logo {
  line-height: 70px;
}

.logo-light {
  display: block;
}

.logo-dark {
  display: none;
}

.navbar-custom {
  background-color: var(--ct-bg-topbar-dark);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  padding: 0 10px 0 0;
  position: fixed;
  left: 0;
  right: 0;
  height: 70px;
  z-index: 1001;
  /* Search */
}
.navbar-custom .topnav-menu > li {
  float: left;
}
.navbar-custom .topnav-menu .nav-link {
  padding: 0 15px;
  color: rgba(255, 255, 255, 0.6);
  min-width: 32px;
  display: block;
  line-height: 70px;
  text-align: center;
  max-height: 70px;
}
.navbar-custom .dropdown .nav-link.show {
  background-color: rgba(255, 255, 255, 0.05);
}
.navbar-custom .container-fluid {
  padding: 0;
}
.navbar-custom .app-search {
  height: 70px;
  display: table;
  max-width: 180px;
  margin-right: 20px;
}
.navbar-custom .app-search .app-search-box {
  display: table-cell;
  vertical-align: middle;
  position: relative;
}
.navbar-custom .app-search .app-search-box input::-webkit-input-placeholder {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.3);
}
.navbar-custom .app-search .app-search-box input::-moz-placeholder {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.3);
}
.navbar-custom .app-search .app-search-box input:-ms-input-placeholder {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.3);
}
.navbar-custom .app-search .app-search-box input::-ms-input-placeholder {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.3);
}
.navbar-custom .app-search .app-search-box input::placeholder {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.3);
}
.navbar-custom .app-search .form-control {
  border: none;
  height: 38px;
  padding-left: 20px;
  padding-right: 0;
  color: #fff;
  background-color: var(--ct-app-search-box-bg);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 30px 0 0 30px;
}
.navbar-custom .app-search .input-group-text {
  margin-left: 0 !important;
  z-index: 4;
}
.navbar-custom .app-search .btn {
  background-color: var(--ct-app-search-box-bg);
  border-color: transparent;
  color: rgba(255, 255, 255, 0.3);
  border-radius: 0 30px 30px 0;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}
.navbar-custom .button-menu-mobile {
  border: none;
  color: #fff;
  display: inline-block;
  height: 70px;
  line-height: 70px;
  width: 60px;
  background-color: transparent;
  font-size: 24px;
  cursor: pointer;
}
.navbar-custom .button-menu-mobile.disable-btn {
  display: none;
}

/* Notification */
.noti-scroll {
  max-height: 230px;
}

.notification-list {
  margin-left: 0;
}
.notification-list .noti-title {
  background-color: transparent;
  padding: 15px 20px;
}
.notification-list .noti-icon-badge {
  display: inline-block;
  position: absolute;
  top: 16px;
  right: 10px;
}
.notification-list .notify-item {
  padding: 12px 20px;
}
.notification-list .notify-item .notify-icon {
  float: left;
  height: 36px;
  width: 36px;
  font-size: 18px;
  line-height: 36px;
  text-align: center;
  margin-right: 10px;
  border-radius: 50%;
  color: #fff;
}
.notification-list .notify-item .notify-details {
  margin-bottom: 5px;
  overflow: hidden;
  margin-left: 45px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--ct-gray-800);
}
.notification-list .notify-item .notify-details b {
  font-weight: 500;
}
.notification-list .notify-item .notify-details small {
  display: block;
}
.notification-list .notify-item .notify-details span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
}
.notification-list .notify-item .user-msg {
  margin-left: 45px;
  white-space: normal;
  line-height: 16px;
}
.notification-list .profile-dropdown .notify-item {
  padding: 7px 20px;
}

.noti-icon {
  font-size: 21px;
  vertical-align: middle;
}

.profile-dropdown {
  min-width: 170px;
}
.profile-dropdown i {
  vertical-align: middle;
  margin-right: 5px;
}

.nav-user {
  padding: 0 12px !important;
}
.nav-user img {
  height: 32px;
  width: 32px;
}

.fullscreen-enable [data-toggle=fullscreen] .fe-maximize::before {
  content: "\e88d";
}

@media (max-width: 991.98px) {
  .logo-box {
    width: 70px !important;
    padding-right: 0 !important;
  }
  .logo-box .logo-lg {
    display: none !important;
  }
  .logo-box .logo-sm {
    display: block !important;
  }
}
@media (max-width: 600px) {
  .navbar-custom .dropdown {
    position: static;
  }
  .navbar-custom .dropdown .dropdown-menu {
    left: 10px !important;
    right: 10px !important;
  }
}
body[data-topbar-color=light] .navbar-custom {
  background-color: var(--ct-bg-topbar-light) !important;
  -webkit-box-shadow: var(--ct-box-shadow-sm);
          box-shadow: var(--ct-box-shadow-sm);
  /* Search */
}
body[data-topbar-color=light] .navbar-custom .topnav-menu .nav-link {
  color: var(--ct-gray-700);
}
body[data-topbar-color=light] .navbar-custom .dropdown .nav-link.show {
  background-color: rgba(var(--ct-gray-900), 0.03);
}
body[data-topbar-color=light] .navbar-custom .button-menu-mobile {
  color: var(--ct-gray-900);
}
body[data-topbar-color=light] .navbar-custom .app-search input::-webkit-input-placeholder {
  color: var(--ct-gray-500) !important;
}
body[data-topbar-color=light] .navbar-custom .app-search input::-moz-placeholder {
  color: var(--ct-gray-500) !important;
}
body[data-topbar-color=light] .navbar-custom .app-search input:-ms-input-placeholder {
  color: var(--ct-gray-500) !important;
}
body[data-topbar-color=light] .navbar-custom .app-search input::-ms-input-placeholder {
  color: var(--ct-gray-500) !important;
}
body[data-topbar-color=light] .navbar-custom .app-search input::placeholder {
  color: var(--ct-gray-500) !important;
}
body[data-topbar-color=light] .navbar-custom .app-search .form-control {
  color: var(--ct-gray-900);
  background-color: var(--ct-gray-100);
  border-color: var(--ct-gray-100);
}
body[data-topbar-color=light] .navbar-custom .app-search .btn {
  background-color: var(--ct-gray-100);
  color: var(--ct-gray-400);
}
body[data-topbar-color=light] .logo-dark {
  display: block;
}
body[data-topbar-color=light] .logo-light {
  display: none;
}

@media (max-width: 991.98px) {
  body[data-layout-mode=horizontal] .navbar-toggle {
    border: 0;
    position: relative;
    padding: 0;
    margin: 0;
    cursor: pointer;
  }
  body[data-layout-mode=horizontal] .navbar-toggle .lines {
    width: 25px;
    display: block;
    position: relative;
    height: 16px;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    margin-top: calc(54px / 2);
  }
  body[data-layout-mode=horizontal] .navbar-toggle span {
    height: 2px;
    width: 100%;
    background-color: var(--ct-gray-700);
    display: block;
    margin-bottom: 5px;
    -webkit-transition: -webkit-transform 0.5s ease;
    transition: -webkit-transform 0.5s ease;
    transition: transform 0.5s ease;
    transition: transform 0.5s ease, -webkit-transform 0.5s ease;
  }
  body[data-layout-mode=horizontal] .navbar-toggle.open span {
    position: absolute;
  }
  body[data-layout-mode=horizontal] .navbar-toggle.open span:first-child {
    top: 7px;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
  }
  body[data-layout-mode=horizontal] .navbar-toggle.open span:nth-child(2) {
    visibility: hidden;
  }
  body[data-layout-mode=horizontal] .navbar-toggle.open span:last-child {
    width: 100%;
    top: 7px;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
  }
}
body[data-layout-mode=horizontal] .button-menu-mobile {
  display: none;
}
body[data-layout-mode=horizontal] .logo-box {
  width: auto;
  padding-right: 50px;
  background-color: transparent;
}
@media (min-width: 992px) and (max-width: 1366px) {
  body[data-layout-mode=horizontal] .logo-box {
    padding-left: 20px;
  }
}

@media (max-width: 360px) {
  .navbar-custom .topnav-menu .nav-link {
    padding: 0 12px;
  }
  .navbar-custom .button-menu-mobile {
    width: 45px;
  }
}
body[data-layout-mode=two-column][data-topbar-color=dark] .navbar-custom .logo-box .logo-light {
  display: none;
}
body[data-layout-mode=two-column][data-topbar-color=dark] .navbar-custom .logo-box .logo-dark {
  display: block;
}
body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=dark] .navbar-custom .logo-box .logo-light {
  display: block;
}
body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=dark] .navbar-custom .logo-box .logo-dark {
  display: none;
}
body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=brand] .navbar-custom .logo-box .logo-light, body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=gradient] .navbar-custom .logo-box .logo-light {
  display: block;
}
body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=brand] .navbar-custom .logo-box .logo-dark, body[data-layout-mode=two-column][data-topbar-color=dark][data-leftbar-color=gradient] .navbar-custom .logo-box .logo-dark {
  display: none;
}

.page-title-box .page-title {
  font-size: 1.25rem;
  margin: 0;
  line-height: 75px;
  color: var(--ct-gray-900);
}
.page-title-box .page-title-right {
  float: right;
  margin-top: 22px;
}
.page-title-box .breadcrumb {
  padding-top: 5px;
}

@media (max-width: 767.98px) {
  .page-title-box .page-title {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 70px;
  }
  .page-title-box .breadcrumb {
    display: none;
  }
}
@media (max-width: 640px) {
  .page-title-box .page-title-right {
    display: none;
  }
}
@media (max-width: 419px) {
  .page-title-box .breadcrumb {
    display: none;
  }
}
.footer {
  bottom: 0;
  padding: 19px 15px 20px;
  position: absolute;
  right: 0;
  color: var(--ct-text-muted);
  left: 240px;
  background-color: var(--ct-footer-bg);
}
.footer .footer-links a {
  color: var(--ct-text-muted);
  margin-left: 1.5rem;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
.footer .footer-links a:hover {
  color: var(--ct-gray-900);
}
.footer .footer-links a:first-of-type {
  margin-left: 0;
}

.footer-alt {
  left: 0 !important;
  text-align: center;
  background-color: transparent;
}

@media (max-width: 767.98px) {
  .footer {
    left: 0 !important;
    text-align: center;
  }
}
body[data-layout-mode=horizontal] .footer {
  left: 0 !important;
}
body[data-layout-mode=horizontal][data-layout-width=boxed] .footer {
  max-width: 1300px !important;
}

@media (min-width: 992px) {
  body[data-layout-mode=detached] .footer {
    position: inherit;
    margin: 0 10px;
  }
}

.right-bar {
  background-color: var(--ct-rightbar-bg);
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 260px;
  z-index: 9999;
  float: right !important;
  right: -270px;
  top: 0;
  bottom: 0;
}
.right-bar .rightbar-title {
  background-color: #6658dd;
  padding: 27px 25px;
  color: #fff;
}
.right-bar .user-box {
  padding: 25px;
  text-align: center;
}
.right-bar .user-box .user-img {
  position: relative;
  height: 64px;
  width: 64px;
  margin: 0 auto 15px auto;
}
.right-bar .user-box .user-img .user-edit {
  position: absolute;
  right: -5px;
  bottom: 0px;
  height: 24px;
  width: 24px;
  background-color: #fff;
  line-height: 24px;
  border-radius: 50%;
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
}
.right-bar .user-box h5 {
  margin-bottom: 2px;
}
.right-bar .user-box h5 a {
  color: var(--ct-gray-900);
}
.right-bar .notification-item .noti-user-item {
  padding: 0.75rem 1rem;
}
.right-bar .notification-item .noti-user-item:hover {
  background-color: var(--ct-dropdown-link-hover-bg);
}
.right-bar .notification-item .user-status {
  position: absolute;
  right: 0px;
  bottom: -4px;
  font-size: 10px;
}
.right-bar .notification-item .user-status.online {
  color: #1abc9c;
}
.right-bar .notification-item .user-status.away {
  color: #f7b84b;
}
.right-bar .notification-item .user-status.busy {
  color: #f1556c;
}

.rightbar-overlay {
  background-color: rgba(var(--ct-gray-900-rgb), 0.2);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
}

.right-bar-enabled .right-bar {
  right: 0;
}
.right-bar-enabled .rightbar-overlay {
  display: block;
}

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto;
  }
  .right-bar .slimscroll-menu {
    height: auto !important;
  }
}
body[data-layout-width=boxed] #wrapper {
  max-width: 1300px;
  margin: 0 auto;
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
}
body[data-layout-width=boxed] .navbar-custom {
  max-width: 1300px;
  margin: 0 auto;
}
body[data-layout-width=boxed] .footer {
  margin: 0 auto;
  max-width: calc(1300px - 240px);
}
body[data-layout-width=boxed][data-leftbar-size=condensed] .footer {
  max-width: calc(1300px - 70px);
}
body[data-layout-width=boxed][data-leftbar-size=compact] .footer {
  max-width: calc(1300px - 160px);
}

@media (min-width: 768px) {
  body[data-layout-width=boxed][data-leftbar-size=condensed] .content-page {
    min-height: calc(1750px - 70px);
  }
}
@media (min-width: 1200px) {
  body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) {
    padding-bottom: 0;
  }
  body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) #wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) .navbar-custom,
body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) .topnav {
    position: absolute;
  }
  body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) .left-side-menu {
    position: relative;
    min-width: 240px;
    max-width: 240px;
    padding: 20px 0 calc(70px + 20px);
  }
  body[data-menu-position=scrollable]:not([data-leftbar-size=condensed]):not([data-leftbar-size=compact]):not([data-layout-mode=two-column]) .content-page {
    margin-left: 0;
    width: 100%;
    padding-bottom: 60px;
  }
}
@media (min-width: 1200px) {
  body[data-layout-mode=horizontal] .container-fluid {
    max-width: 90%;
  }
  body[data-layout-mode=horizontal] .navbar-custom {
    padding: 0 24px;
  }
}
body[data-layout-mode=horizontal] .content-page {
  margin-left: 0 !important;
}

.topnav {
  background: var(--ct-bg-topbar-light);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  margin-top: 70px;
  padding: 0 calc(24px / 2);
  position: fixed;
  left: 0;
  right: 0;
  z-index: 100;
}
.topnav .topnav-menu {
  margin: 0;
  padding: 0;
}
.topnav .navbar-nav .nav-link {
  font-size: 0.95rem;
  position: relative;
  line-height: 22px;
  padding: calc(33px / 2) 1.1rem;
  color: var(--ct-hori-menu-item-color);
  font-family: "Roboto", sans-serif;
}
.topnav .navbar-nav .nav-link i {
  font-size: 15px;
  display: inline-block;
}
.topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {
  color: var(--ct-menu-item-hover);
  background-color: transparent;
}
.topnav .navbar-nav .nav-item.dropdown.active > a.dropdown-toggle {
  color: var(--ct-menu-item-active);
}

@media (min-width: 992px) {
  .topnav {
    height: 55px;
  }
  .topnav .navbar-nav .nav-item:first-of-type .nav-link {
    padding-left: 0;
  }
  .topnav .dropdown-item {
    padding: 0.5rem 1.25rem;
    min-width: 180px;
    margin: 0 0.3rem;
    width: auto;
  }
  .topnav .dropdown-item.active {
    background-color: transparent;
    color: var(--ct-menu-item-active);
  }
  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {
    left: 0px;
    right: auto;
  }
  .topnav .dropdown .dropdown-menu {
    padding: 0.3rem 0;
    margin-top: 0;
    border-radius: 0 0 0.25rem 0.25rem;
  }
  .topnav .dropdown .dropdown-menu .arrow-down::after {
    right: 20px;
    -webkit-transform: rotate(-135deg) translateY(-50%);
            transform: rotate(-135deg) translateY(-50%);
    position: absolute;
  }
  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {
    position: absolute;
    top: 0 !important;
    left: 100%;
    display: none;
  }
  .topnav .dropdown:hover > .nav-link {
    color: var(--ct-menu-item-hover);
  }
  .topnav .dropdown:hover > .dropdown-menu {
    display: block;
  }
  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-item {
    color: var(--ct-menu-item-active);
  }
  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {
    display: block;
  }
  .navbar-toggle {
    display: none;
  }
  body[data-layout-mode=horizontal] .content-page {
    padding: 55px 15px 65px 15px;
  }
}
.arrow-down {
  display: inline-block;
}
.arrow-down:after {
  border-color: initial;
  border-style: solid;
  border-width: 0 0 1px 1px;
  content: "";
  height: 0.4em;
  display: inline-block;
  right: 5px;
  top: 50%;
  margin-left: 10px;
  -webkit-transform: rotate(-45deg) translateY(-50%);
          transform: rotate(-45deg) translateY(-50%);
  -webkit-transform-origin: top;
          transform-origin: top;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  width: 0.4em;
}

@media (max-width: 1199.98px) {
  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {
    right: 100%;
    left: auto;
  }
}
@media (max-width: 991.98px) {
  .topnav {
    max-height: 360px;
    overflow-y: auto;
    padding: 0;
  }
  .topnav .navbar-nav .nav-link {
    padding: 0.75rem 1.1rem;
  }
  .topnav .dropdown .dropdown-menu {
    background-color: transparent;
    border: none;
    -webkit-box-shadow: none;
            box-shadow: none;
    padding-left: 15px;
  }
  .topnav .dropdown .dropdown-item {
    position: relative;
    background-color: transparent;
  }
  .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {
    color: var(--ct-menu-item-active);
  }
  .topnav .arrow-down::after {
    right: 15px;
    position: absolute;
  }
}
@media (min-width: 992px) {
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav {
    background-color: var(--ct-bg-topbar-dark);
  }
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.7);
  }
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav .nav-link:focus, body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9);
  }
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav .nav-link.active {
    color: #fff;
  }
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav .nav-item:hover .nav-link {
    color: #fff;
  }
  body[data-layout-mode=horizontal][data-topbar-color=light] .topnav .navbar-nav > .dropdown.active > a {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}
body[data-layout-mode=horizontal][data-layout-width=boxed] .topnav {
  max-width: 1300px;
  margin: 70px auto 0;
}

body[data-layout-mode=two-column] .left-side-menu {
  width: calc(70px + 220px);
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
body[data-layout-mode=two-column] .sidebar-icon-menu {
  position: fixed;
  width: 70px;
  z-index: 500;
  top: 0;
  bottom: 0;
  padding-bottom: 20px;
  background-color: var(--ct-twocolumn-sidebar-iconview-bg);
}
body[data-layout-mode=two-column] .sidebar-icon-menu .logo {
  display: block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
}
body[data-layout-mode=two-column] .sidebar-icon-menu .nav {
  background-color: transparent;
  margin: 24px auto;
}
body[data-layout-mode=two-column] .sidebar-icon-menu .nav .nav-link {
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 40px;
  margin: 12px auto;
  padding: 0px;
  border-radius: 4px;
}
body[data-layout-mode=two-column] .sidebar-icon-menu .nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.12);
}
body[data-layout-mode=two-column] .sidebar-icon-menu .nav .nav-link svg {
  color: #fff;
  fill: rgba(255, 255, 255, 0.12);
  height: 22px;
  width: 22px;
}
body[data-layout-mode=two-column] .sidebar-main-menu {
  display: block;
  position: fixed;
  width: 220px;
  background-color: var(--ct-twocolumn-sidebar-bg);
  top: 70px;
  bottom: 0;
  left: 70px;
  padding: 30px 5px;
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  -webkit-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
}
body[data-layout-mode=two-column] .sidebar-main-menu .sidebar-menu-body {
  padding: 20px;
}
body[data-layout-mode=two-column] .sidebar-main-menu .menu-title {
  color: var(--ct-gray-500) !important;
  margin: 0;
  padding: 10px 20px;
  letter-spacing: 0.05em;
  font-size: 0.7rem;
  text-transform: uppercase;
  font-weight: 600;
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav > .nav-item > .nav-link {
  color: var(--ct-menu-item);
  font-size: 0.95rem;
  font-family: "Roboto", sans-serif;
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav > .nav-item .menu-arrow {
  right: 10px;
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav .nav-link {
  position: relative;
  color: var(--ct-menu-item);
  padding: 6px 15px;
  border-radius: 3px;
  margin: 3px 5px;
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav .nav-link:hover, body[data-layout-mode=two-column] .sidebar-main-menu .nav .nav-link:focus, body[data-layout-mode=two-column] .sidebar-main-menu .nav .nav-link.active {
  color: var(--ct-menu-item-active);
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav .menuitem-active > a.nav-link {
  color: var(--ct-menu-item-active);
  background-color: var(--ct-menuitem-active-bg);
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav .menuitem-active a.active {
  color: var(--ct-menu-item-active);
}
body[data-layout-mode=two-column] .sidebar-main-menu #sidebar-menu .menu-arrow {
  top: 7px;
}
body[data-layout-mode=two-column] .sidebar-main-menu .nav-second-level {
  padding-left: 15px;
  list-style: none;
}
@media (min-width: 992px) {
  body[data-layout-mode=two-column] .navbar-custom {
    left: 70px !important;
    padding-left: 0px;
  }
  body[data-layout-mode=two-column] .navbar-custom .logo-box {
    width: 220px;
  }
  body[data-layout-mode=two-column] .navbar-custom .logo-box .logo-sm {
    display: none;
  }
  body[data-layout-mode=two-column][data-leftbar-size=condensed] .logo-box {
    width: 0 !important;
  }
}
body[data-layout-mode=two-column][data-leftbar-color=light] .logo-box {
  background-color: var(--ct-twocolumn-sidebar-bg);
}
@media (min-width: 992px) {
  body[data-layout-mode=two-column] .content-page {
    margin-left: calc(70px + 220px);
  }
  body[data-layout-mode=two-column] .footer {
    left: calc(70px + 220px);
  }
  body[data-layout-mode=two-column][data-leftbar-size=condensed] .sidebar-main-menu {
    display: none;
  }
}
body[data-layout-mode=two-column] .twocolumn-menu-item {
  display: none;
}
body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu {
  background-color: var(--ct-bg-leftbar-dark);
}
body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav > .nav-item > .nav-link {
  color: var(--ct-menu-item-color-dark);
}
body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav .nav-link:hover, body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav .nav-link:focus, body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav .nav-link.active {
  color: var(--ct-menu-item-hover-color-dark);
}
body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav .menuitem-active > a.nav-link {
  color: var(--ct-menu-item-active);
  background-color: var(--ct-menuitem-active-bg);
}
body[data-layout-mode=two-column][data-leftbar-color=dark] .sidebar-main-menu .nav .menuitem-active a.active {
  color: var(--ct-menu-item-active);
}
body[data-layout-mode=two-column][data-leftbar-color=brand] .sidebar-main-menu .nav > .nav-item > .nav-link, body[data-layout-mode=two-column][data-leftbar-color=gradient] .sidebar-main-menu .nav > .nav-item > .nav-link {
  color: rgba(255, 255, 255, 0.7);
}
body[data-layout-mode=two-column][data-leftbar-color=brand] .sidebar-main-menu .nav .nav-link:hover, body[data-layout-mode=two-column][data-leftbar-color=brand] .sidebar-main-menu .nav .nav-link:focus, body[data-layout-mode=two-column][data-leftbar-color=brand] .sidebar-main-menu .nav .nav-link.active, body[data-layout-mode=two-column][data-leftbar-color=gradient] .sidebar-main-menu .nav .nav-link:hover, body[data-layout-mode=two-column][data-leftbar-color=gradient] .sidebar-main-menu .nav .nav-link:focus, body[data-layout-mode=two-column][data-leftbar-color=gradient] .sidebar-main-menu .nav .nav-link.active {
  color: rgba(255, 255, 255, 0.9);
}
body[data-layout-mode=two-column][data-leftbar-color=brand] .sidebar-main-menu {
  background-color: var(--ct-bg-leftbar-brand);
}
body[data-layout-mode=two-column][data-leftbar-color=gradient] .sidebar-main-menu {
  background: var(--ct-bg-leftbar-gradient);
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(64, 149, 216, 0.15)), to(transparent));
  background-image: linear-gradient(270deg, rgba(64, 149, 216, 0.15), transparent);
}
body[data-layout-mode=two-column][data-layout-width=boxed] .navbar-custom {
  max-width: calc(1300px - 70px);
}
body[data-layout-mode=two-column][data-layout-width=boxed] .sidebar-main-menu {
  position: absolute;
  top: 0;
}
body[data-layout-mode=two-column][data-layout-width=boxed]:not([data-leftbar-size=condensed]) .footer {
  max-width: calc(1300px - calc(70px + 220px));
}

.avatar-xs {
  height: 1.5rem;
  width: 1.5rem;
}

.avatar-sm {
  height: 2.25rem;
  width: 2.25rem;
}

.avatar-md {
  height: 3.5rem;
  width: 3.5rem;
}

.avatar-lg {
  height: 4.5rem;
  width: 4.5rem;
}

.avatar-xl {
  height: 6rem;
  width: 6rem;
}

.avatar-xxl {
  height: 7.5rem;
  width: 7.5rem;
}

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: var(--ct-card-bg);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%;
}

.avatar-group {
  padding-left: 12px;
}
.avatar-group .avatar-group-item {
  margin: 0 0 10px -12px;
  display: inline-block;
  border: 2px solid var(--ct-card-bg);
  border-radius: 50%;
}

.width-xs {
  min-width: 80px;
}

.width-sm {
  min-width: 100px;
}

.width-md {
  min-width: 120px;
}

.width-lg {
  min-width: 140px;
}

.width-xl {
  min-width: 160px;
}

.font-family-primary {
  font-family: "Roboto", sans-serif;
}

.font-family-secondary {
  font-family: "Roboto", sans-serif;
}

.sp-line-1,
.sp-line-2,
.sp-line-3,
.sp-line-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.sp-line-1 {
  -webkit-line-clamp: 1;
}

.sp-line-2 {
  -webkit-line-clamp: 2;
}

.sp-line-3 {
  -webkit-line-clamp: 3;
}

.sp-line-4 {
  -webkit-line-clamp: 4;
}

.icon-dual {
  color: var(--ct-text-muted);
  fill: rgba(var(--ct-gray-600-rgb), 0.12);
}

.icon-dual-primary {
  color: #6658dd;
  fill: rgba(102, 88, 221, 0.16);
}

.icon-dual-secondary {
  color: #6c757d;
  fill: rgba(108, 117, 125, 0.16);
}

.icon-dual-success {
  color: #1abc9c;
  fill: rgba(26, 188, 156, 0.16);
}

.icon-dual-info {
  color: #4fc6e1;
  fill: rgba(79, 198, 225, 0.16);
}

.icon-dual-warning {
  color: #f7b84b;
  fill: rgba(247, 184, 75, 0.16);
}

.icon-dual-danger {
  color: #f1556c;
  fill: rgba(241, 85, 108, 0.16);
}

.icon-dual-light {
  color: #eceff1;
  fill: rgba(236, 239, 241, 0.16);
}

.icon-dual-dark {
  color: #323a46;
  fill: rgba(50, 58, 70, 0.16);
}

.icon-dual-pink {
  color: #f672a7;
  fill: rgba(246, 114, 167, 0.16);
}

.icon-dual-blue {
  color: #4a81d4;
  fill: rgba(74, 129, 212, 0.16);
}

.icons-xs {
  height: 16px;
  width: 16px;
}

.icons-sm {
  height: 24px;
  width: 24px;
}

.icons-md {
  height: 32px;
  width: 32px;
}

.icons-lg {
  height: 40px;
  width: 40px;
}

.icons-xl {
  height: 48px;
  width: 48px;
}

.icons-xxl {
  height: 60px;
  width: 60px;
}

.item-hovered:hover {
  background-color: var(--ct-dropdown-link-hover-bg);
}

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 2px);
  display: block;
  border: 2px solid var(--ct-gray-500);
  border-radius: 50%;
  color: var(--ct-gray-500);
}

.widget-flat {
  position: relative;
  overflow: hidden;
}
.widget-flat i.widget-icon {
  font-size: 36px;
}

.inbox-widget .inbox-item {
  border-bottom: 1px solid var(--ct-gray-300);
  overflow: hidden;
  padding: 0.625rem 0;
  position: relative;
}
.inbox-widget .inbox-item:last-of-type {
  border-bottom: none;
}
.inbox-widget .inbox-item .inbox-item-img {
  display: block;
  float: left;
  margin-right: 15px;
  width: 40px;
}
.inbox-widget .inbox-item .inbox-item-img img {
  width: 40px;
}
.inbox-widget .inbox-item .inbox-item-author {
  color: var(--ct-heading-color);
  display: block;
  margin-bottom: 3px;
  font-weight: 500;
}
.inbox-widget .inbox-item .inbox-item-text {
  color: var(--ct-text-muted);
  display: block;
  font-size: 0.8125rem;
  margin: 0;
  overflow: hidden;
}
.inbox-widget .inbox-item .inbox-item-date {
  color: var(--ct-gray-600);
  font-size: 0.6875rem;
  position: absolute;
  right: 5px;
  top: 10px;
}

/* Chat widget */
.conversation-list {
  list-style: none;
  padding: 0 15px;
}
.conversation-list li {
  margin-bottom: 20px;
}
.conversation-list li .conversation-actions {
  float: right;
  display: none;
}
.conversation-list li:hover .conversation-actions {
  display: block;
}
.conversation-list .chat-avatar {
  float: left;
  text-align: center;
  width: 42px;
}
.conversation-list .chat-avatar img {
  border-radius: 100%;
  width: 100%;
}
.conversation-list .chat-avatar i {
  font-size: 12px;
  font-style: normal;
}
.conversation-list .ctext-wrap {
  background: var(--ct-chat-secondary-user-bg);
  border-radius: 3px;
  display: inline-block;
  padding: 12px;
  position: relative;
}
.conversation-list .ctext-wrap i {
  display: block;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  position: relative;
}
.conversation-list .ctext-wrap p {
  margin: 0;
  padding-top: 3px;
}
.conversation-list .ctext-wrap:after {
  left: -10px;
  top: 0;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-top-color: var(--ct-chat-secondary-user-bg);
  border-width: 6px;
  margin-right: -1px;
  border-right-color: var(--ct-chat-secondary-user-bg);
}
.conversation-list .conversation-text {
  float: left;
  font-size: 12px;
  margin-left: 12px;
  width: 70%;
}
.conversation-list .odd .chat-avatar {
  float: right !important;
}
.conversation-list .odd .conversation-text {
  float: right !important;
  margin-right: 12px;
  text-align: right;
  width: 70% !important;
}
.conversation-list .odd .ctext-wrap {
  background-color: var(--ct-chat-primary-user-bg);
}
.conversation-list .odd .ctext-wrap:after {
  border-color: transparent;
  border-left-color: var(--ct-chat-primary-user-bg);
  border-top-color: var(--ct-chat-primary-user-bg);
  right: -10px !important;
  left: auto;
}
.conversation-list .odd .conversation-actions {
  float: left;
}

.form-check.form-check-primary .form-check-input:checked {
  background-color: #6658dd !important;
  border-color: #6658dd !important;
}

.form-check.form-check-secondary .form-check-input:checked {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
}

.form-check.form-check-success .form-check-input:checked {
  background-color: #1abc9c !important;
  border-color: #1abc9c !important;
}

.form-check.form-check-info .form-check-input:checked {
  background-color: #4fc6e1 !important;
  border-color: #4fc6e1 !important;
}

.form-check.form-check-warning .form-check-input:checked {
  background-color: #f7b84b !important;
  border-color: #f7b84b !important;
}

.form-check.form-check-danger .form-check-input:checked {
  background-color: #f1556c !important;
  border-color: #f1556c !important;
}

.form-check.form-check-light .form-check-input:checked {
  background-color: #eceff1 !important;
  border-color: #eceff1 !important;
}

.form-check.form-check-dark .form-check-input:checked {
  background-color: #323a46 !important;
  border-color: #323a46 !important;
}

.form-check.form-check-pink .form-check-input:checked {
  background-color: #f672a7 !important;
  border-color: #f672a7 !important;
}

.form-check.form-check-blue .form-check-input:checked {
  background-color: #4a81d4 !important;
  border-color: #4a81d4 !important;
}

.ribbon-box {
  position: relative;
  /* Ribbon two */
}
.ribbon-box .ribbon {
  position: relative;
  clear: both;
  padding: 5px 12px;
  margin-bottom: 15px;
  -webkit-box-shadow: 2px 5px 10px rgba(var(--ct-gray-900-rgb), 0.15);
          box-shadow: 2px 5px 10px rgba(var(--ct-gray-900-rgb), 0.15);
  color: #fff;
  font-size: 13px;
  font-weight: 600;
}
.ribbon-box .ribbon:before {
  content: " ";
  border-style: solid;
  border-width: 10px;
  display: block;
  position: absolute;
  bottom: -10px;
  left: 0;
  margin-bottom: -10px;
  z-index: -1;
}
.ribbon-box .ribbon.float-start {
  margin-left: -30px;
  border-radius: 0 3px 3px 0;
}
.ribbon-box .ribbon.float-end {
  margin-right: -30px;
  border-radius: 3px 0 0 3px;
}
.ribbon-box .ribbon.float-end:before {
  right: 0;
}
.ribbon-box .ribbon.float-center span {
  margin: 0 auto 20px auto;
}
.ribbon-box .ribbon-content {
  clear: both;
}
.ribbon-box .ribbon-primary {
  background: #6658dd;
}
.ribbon-box .ribbon-primary:before {
  border-color: #3f2ed4 transparent transparent;
}
.ribbon-box .ribbon-secondary {
  background: #6c757d;
}
.ribbon-box .ribbon-secondary:before {
  border-color: #545b62 transparent transparent;
}
.ribbon-box .ribbon-success {
  background: #1abc9c;
}
.ribbon-box .ribbon-success:before {
  border-color: #148f77 transparent transparent;
}
.ribbon-box .ribbon-info {
  background: #4fc6e1;
}
.ribbon-box .ribbon-info:before {
  border-color: #25b7d8 transparent transparent;
}
.ribbon-box .ribbon-warning {
  background: #f7b84b;
}
.ribbon-box .ribbon-warning:before {
  border-color: #f5a51a transparent transparent;
}
.ribbon-box .ribbon-danger {
  background: #f1556c;
}
.ribbon-box .ribbon-danger:before {
  border-color: #ed2643 transparent transparent;
}
.ribbon-box .ribbon-light {
  background: #eceff1;
}
.ribbon-box .ribbon-light:before {
  border-color: #cfd6db transparent transparent;
}
.ribbon-box .ribbon-dark {
  background: #323a46;
}
.ribbon-box .ribbon-dark:before {
  border-color: #1d2128 transparent transparent;
}
.ribbon-box .ribbon-pink {
  background: #f672a7;
}
.ribbon-box .ribbon-pink:before {
  border-color: #f34289 transparent transparent;
}
.ribbon-box .ribbon-blue {
  background: #4a81d4;
}
.ribbon-box .ribbon-blue:before {
  border-color: #2d67be transparent transparent;
}
.ribbon-box .ribbon-two {
  position: absolute;
  left: -5px;
  top: -5px;
  z-index: 1;
  overflow: hidden;
  width: 75px;
  height: 75px;
  text-align: right;
}
.ribbon-box .ribbon-two span {
  font-size: 13px;
  color: #fff;
  text-align: center;
  line-height: 20px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  width: 100px;
  display: block;
  -webkit-box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  position: absolute;
  top: 19px;
  left: -21px;
  font-weight: 600;
}
.ribbon-box .ribbon-two span:before {
  content: "";
  position: absolute;
  left: 0;
  top: 100%;
  z-index: -1;
  border-right: 3px solid transparent;
  border-bottom: 3px solid transparent;
}
.ribbon-box .ribbon-two span:after {
  content: "";
  position: absolute;
  right: 0;
  top: 100%;
  z-index: -1;
  border-left: 3px solid transparent;
  border-bottom: 3px solid transparent;
}
.ribbon-box .ribbon-two-primary span {
  background: #6658dd;
}
.ribbon-box .ribbon-two-primary span:before {
  border-left: 3px solid #3827c1;
  border-top: 3px solid #3827c1;
}
.ribbon-box .ribbon-two-primary span:after {
  border-right: 3px solid #3827c1;
  border-top: 3px solid #3827c1;
}
.ribbon-box .ribbon-two-secondary span {
  background: #6c757d;
}
.ribbon-box .ribbon-two-secondary span:before {
  border-left: 3px solid #494f54;
  border-top: 3px solid #494f54;
}
.ribbon-box .ribbon-two-secondary span:after {
  border-right: 3px solid #494f54;
  border-top: 3px solid #494f54;
}
.ribbon-box .ribbon-two-success span {
  background: #1abc9c;
}
.ribbon-box .ribbon-two-success span:before {
  border-left: 3px solid #117964;
  border-top: 3px solid #117964;
}
.ribbon-box .ribbon-two-success span:after {
  border-right: 3px solid #117964;
  border-top: 3px solid #117964;
}
.ribbon-box .ribbon-two-info span {
  background: #4fc6e1;
}
.ribbon-box .ribbon-two-info span:before {
  border-left: 3px solid #21a5c2;
  border-top: 3px solid #21a5c2;
}
.ribbon-box .ribbon-two-info span:after {
  border-right: 3px solid #21a5c2;
  border-top: 3px solid #21a5c2;
}
.ribbon-box .ribbon-two-warning span {
  background: #f7b84b;
}
.ribbon-box .ribbon-two-warning span:before {
  border-left: 3px solid #eb990a;
  border-top: 3px solid #eb990a;
}
.ribbon-box .ribbon-two-warning span:after {
  border-right: 3px solid #eb990a;
  border-top: 3px solid #eb990a;
}
.ribbon-box .ribbon-two-danger span {
  background: #f1556c;
}
.ribbon-box .ribbon-two-danger span:before {
  border-left: 3px solid #e71332;
  border-top: 3px solid #e71332;
}
.ribbon-box .ribbon-two-danger span:after {
  border-right: 3px solid #e71332;
  border-top: 3px solid #e71332;
}
.ribbon-box .ribbon-two-light span {
  background: #eceff1;
}
.ribbon-box .ribbon-two-light span:before {
  border-left: 3px solid #c0cad1;
  border-top: 3px solid #c0cad1;
}
.ribbon-box .ribbon-two-light span:after {
  border-right: 3px solid #c0cad1;
  border-top: 3px solid #c0cad1;
}
.ribbon-box .ribbon-two-dark span {
  background: #323a46;
}
.ribbon-box .ribbon-two-dark span:before {
  border-left: 3px solid #121519;
  border-top: 3px solid #121519;
}
.ribbon-box .ribbon-two-dark span:after {
  border-right: 3px solid #121519;
  border-top: 3px solid #121519;
}
.ribbon-box .ribbon-two-pink span {
  background: #f672a7;
}
.ribbon-box .ribbon-two-pink span:before {
  border-left: 3px solid #f12a7a;
  border-top: 3px solid #f12a7a;
}
.ribbon-box .ribbon-two-pink span:after {
  border-right: 3px solid #f12a7a;
  border-top: 3px solid #f12a7a;
}
.ribbon-box .ribbon-two-blue span {
  background: #4a81d4;
}
.ribbon-box .ribbon-two-blue span:before {
  border-left: 3px solid #285ca9;
  border-top: 3px solid #285ca9;
}
.ribbon-box .ribbon-two-blue span:after {
  border-right: 3px solid #285ca9;
  border-top: 3px solid #285ca9;
}

@media print {
  .left-side-menu,
.right-bar,
.page-title-box,
.navbar-custom,
.footer {
    display: none;
  }
  .card-body,
.content-page,
.right-bar,
.content,
body {
    padding: 0;
    margin: 0;
  }
}
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--ct-card-bg);
  z-index: 9999;
}

#status {
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px;
}

.spinner {
  margin: 0 auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 5px solid var(--ct-gray-300);
  border-right: 5px solid var(--ct-gray-300);
  border-bottom: 5px solid var(--ct-gray-300);
  border-left: 5px solid #6658dd;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-animation: SpinnerAnimation 1.1s infinite linear;
          animation: SpinnerAnimation 1.1s infinite linear;
}

.spinner,
.spinner:after {
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

@-webkit-keyframes SpinnerAnimation {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes SpinnerAnimation {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.authentication-bg.enlarged {
  min-height: 100px;
}

.bg-pattern {
  background-image: url("../images/bg-pattern-2.png");
  background-size: cover;
}

body.authentication-bg {
  background-color: var(--ct-auth-bg-alt);
  background-size: cover;
  background-position: center;
}

body.authentication-bg-pattern {
  background-image: url("../images/bg-material.png");
}

.logout-icon {
  width: 140px;
}

.auth-fluid {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: 100vh;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  background: url("../images/bg-material.png") center;
  background-size: cover;
}
.auth-fluid .auth-fluid-form-box {
  max-width: 480px;
  border-radius: 0;
  z-index: 2;
  padding: 3rem 2rem;
  background-color: var(--ct-auth-bg);
  position: relative;
  width: 100%;
}
.auth-fluid .auth-fluid-right {
  padding: 6rem 3rem;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
}

.auth-brand {
  margin-bottom: 2rem;
}

.auth-user-testimonial {
  position: absolute;
  margin: 0 auto;
  padding: 0 1.75rem;
  bottom: 3rem;
  left: 0;
  right: 0;
}
.auth-user-testimonial p.lead {
  font-size: 1.125rem;
  margin: 0 auto 20px auto;
  max-width: 700px;
}

@media (min-width: 992px) {
  .auth-brand {
    position: absolute;
    top: 3rem;
  }
}
@media (max-width: 991.98px) {
  .auth-fluid {
    display: block;
  }
  .auth-fluid .auth-fluid-form-box {
    max-width: 100%;
    min-height: 100vh;
  }
  .auth-fluid .auth-fluid-right {
    display: none;
  }
}
.auth-logo .logo-light {
  display: none !important;
}
.auth-logo .logo-dark {
  display: block !important;
}

body[data-theme=dark] .auth-logo .logo-light {
  display: block !important;
}
body[data-theme=dark] .auth-logo .logo-dark {
  display: none !important;
}

.button-list {
  margin-left: -8px;
  margin-bottom: -12px;
}
.button-list .btn {
  margin-bottom: 12px;
  margin-left: 8px;
}

.grid-structure .grid-container {
  background-color: var(--ct-gray-100);
  margin-bottom: 10px;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 10px 20px;
}

.icons-list-demo div.col-sm-6 {
  cursor: pointer;
  line-height: 45px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  overflow: hidden;
}
.icons-list-demo div.col-sm-6 p {
  margin-bottom: 0;
  line-height: inherit;
}
.icons-list-demo i {
  text-align: center;
  vertical-align: middle;
  font-size: 22px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 12px;
  color: var(--ct-text-muted);
  border-radius: 3px;
  display: inline-block;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}
.icons-list-demo .col-md-4 {
  border-radius: 3px;
  background-clip: padding-box;
  margin-bottom: 10px;
}
.icons-list-demo .col-md-4:hover, .icons-list-demo .col-md-4:hover i {
  color: #6658dd;
}
.icons-list-demo .icon-item svg {
  margin-right: 10px;
}
.icons-list-demo .icon-item span {
  line-height: 30px;
  display: inline-block;
  vertical-align: middle;
}

.scrollspy-example {
  position: relative;
  height: 200px;
  margin-top: 0.5rem;
  overflow: auto;
}

.text-error {
  color: #6658dd;
  text-shadow: rgba(102, 88, 221, 0.3) 5px 1px, rgba(102, 88, 221, 0.2) 10px 3px;
  font-size: 84px;
  line-height: 90px;
  font-family: "Roboto", sans-serif;
}

.error-text-box {
  font-size: 10rem;
  font-family: "Roboto", sans-serif;
  min-height: 200px;
}
.error-text-box .text {
  fill: none;
  stroke-width: 6;
  stroke-linejoin: round;
  stroke-dasharray: 30 100;
  stroke-dashoffset: 0;
  -webkit-animation: stroke 9s infinite linear;
          animation: stroke 9s infinite linear;
}
.error-text-box .text:nth-child(5n+1) {
  stroke: #f1556c;
  -webkit-animation-delay: -1.2s;
          animation-delay: -1.2s;
}
.error-text-box .text:nth-child(5n+2) {
  stroke: #f7b84b;
  -webkit-animation-delay: -2.4s;
          animation-delay: -2.4s;
}
.error-text-box .text:nth-child(5n+3) {
  stroke: #6658dd;
  -webkit-animation-delay: -3.6s;
          animation-delay: -3.6s;
}
.error-text-box .text:nth-child(5n+4) {
  stroke: #4fc6e1;
  -webkit-animation-delay: -4.8s;
          animation-delay: -4.8s;
}
.error-text-box .text:nth-child(5n+5) {
  stroke: #1abc9c;
  -webkit-animation-delay: -6s;
          animation-delay: -6s;
}

@-webkit-keyframes stroke {
  100% {
    stroke-dashoffset: -400;
  }
}

@keyframes stroke {
  100% {
    stroke-dashoffset: -400;
  }
}
@media (forced-colors: active) {
  .error-text-box .text {
    fill: #f1556c;
    stroke: #f1556c;
    stroke-width: 6;
    stroke-dasharray: 0 0;
    stroke-dashoffset: 0;
    -webkit-animation: none;
            animation: none;
  }
}
.logout-checkmark {
  width: 100px;
  margin: 0 auto;
  padding: 20px 0;
}
.logout-checkmark .path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  -webkit-animation: dash 2s ease-in-out;
          animation: dash 2s ease-in-out;
}
.logout-checkmark .spin {
  -webkit-animation: spin 2s;
          animation: spin 2s;
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
}

@-webkit-keyframes dash {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@-webkit-keyframes text {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes text {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.faq-question-q-box {
  height: 30px;
  width: 30px;
  color: #6658dd;
  text-align: center;
  border-radius: 50%;
  float: left;
  font-weight: 700;
  line-height: 30px;
  background-color: rgba(102, 88, 221, 0.15);
}

.faq-question {
  margin-top: 0;
  margin-left: 50px;
  font-weight: 400;
  font-size: 16px;
}

.faq-answer {
  margin-left: 50px;
  color: var(--ct-text-muted);
}

.svg-computer {
  stroke-dasharray: 1134;
  stroke-dashoffset: -1134;
  -webkit-animation: draw-me 5s infinite;
          animation: draw-me 5s infinite;
  -webkit-animation-direction: normal;
          animation-direction: normal;
  height: 160px;
}

@-webkit-keyframes draw-me {
  from {
    stroke-dashoffset: -1134;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes draw-me {
  from {
    stroke-dashoffset: -1134;
  }
  to {
    stroke-dashoffset: 0;
  }
}
@media (forced-colors: active) {
  .svg-computer {
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
    -webkit-animation: none;
            animation: none;
    -webkit-animation-direction: normal;
            animation-direction: normal;
  }
}
.timeline {
  margin-bottom: 50px;
  position: relative;
}
.timeline:before {
  background-color: var(--ct-gray-300);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  top: 30px;
  width: 2px;
  z-index: 0;
}
.timeline .time-show {
  margin-bottom: 30px;
  margin-top: 30px;
  position: relative;
}
.timeline .timeline-box {
  background: var(--ct-card-bg);
  display: block;
  margin: 15px 0;
  position: relative;
  padding: 20px;
  border-radius: 0.25rem;
  -webkit-box-shadow: var(--ct-components-shadow);
          box-shadow: var(--ct-components-shadow);
}
.timeline .timeline-album {
  margin-top: 12px;
}
.timeline .timeline-album a {
  display: inline-block;
  margin-right: 5px;
}
.timeline .timeline-album img {
  height: 36px;
  width: auto;
  border-radius: 3px;
}

@media (min-width: 768px) {
  .timeline .time-show {
    margin-right: -69px;
    text-align: right;
  }
  .timeline .timeline-box {
    margin-left: 45px;
  }
  .timeline .timeline-icon {
    background: var(--ct-gray-300);
    border-radius: 50%;
    display: block;
    height: 20px;
    left: -54px;
    margin-top: -10px;
    position: absolute;
    text-align: center;
    top: 50%;
    width: 20px;
  }
  .timeline .timeline-icon i {
    color: var(--ct-gray-600);
    font-size: 13px;
    position: absolute;
    left: 4px;
    margin-top: 1px;
  }
  .timeline .timeline-desk {
    display: table-cell;
    vertical-align: top;
    width: 50%;
  }
  .timeline-item {
    display: table-row;
  }
  .timeline-item:before {
    content: "";
    display: block;
    width: 50%;
  }
  .timeline-item .timeline-desk .arrow {
    border-bottom: 12px solid transparent;
    border-right: 12px solid var(--ct-card-bg) !important;
    border-top: 12px solid transparent;
    display: block;
    height: 0;
    left: -12px;
    margin-top: -12px;
    position: absolute;
    top: 50%;
    width: 0;
  }
  .timeline-item.timeline-item-left:after {
    content: "";
    display: block;
    width: 50%;
  }
  .timeline-item.timeline-item-left .timeline-desk .arrow-alt {
    border-bottom: 12px solid transparent;
    border-left: 12px solid var(--ct-card-bg) !important;
    border-top: 12px solid transparent;
    display: block;
    height: 0;
    left: auto;
    margin-top: -12px;
    position: absolute;
    right: -12px;
    top: 50%;
    width: 0;
  }
  .timeline-item.timeline-item-left .timeline-desk .album {
    float: right;
    margin-top: 20px;
  }
  .timeline-item.timeline-item-left .timeline-desk .album a {
    float: right;
    margin-left: 5px;
  }
  .timeline-item.timeline-item-left .timeline-icon {
    left: auto;
    right: -56px;
  }
  .timeline-item.timeline-item-left:before {
    display: none;
  }
  .timeline-item.timeline-item-left .timeline-box {
    margin-right: 45px;
    margin-left: 0;
    text-align: right;
  }
}
@media (max-width: 767.98px) {
  .timeline .time-show {
    text-align: center;
    position: relative;
  }
  .timeline .timeline-icon {
    display: none;
  }
}
.timeline-sm {
  padding-left: 110px;
}
.timeline-sm .timeline-sm-item {
  position: relative;
  padding-bottom: 20px;
  padding-left: 40px;
  border-left: 2px solid var(--ct-gray-300);
}
.timeline-sm .timeline-sm-item:after {
  content: "";
  display: block;
  position: absolute;
  top: 3px;
  left: -7px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--ct-card-bg);
  border: 2px solid #6658dd;
}
.timeline-sm .timeline-sm-item .timeline-sm-date {
  position: absolute;
  left: -104px;
}

@media (max-width: 420px) {
  .timeline-sm {
    padding-left: 0px;
  }
  .timeline-sm .timeline-sm-date {
    position: relative !important;
    display: block;
    left: 0px !important;
    margin-bottom: 10px;
  }
}
.inbox-leftbar {
  width: 240px;
  float: left;
  padding: 0 20px 20px 10px;
  position: relative;
}
.inbox-leftbar:before {
  border-right: 5px solid var(--ct-body-bg);
  content: "";
  position: absolute;
  top: 0;
  right: -15px;
  bottom: -1.5rem;
}

.inbox-rightbar {
  margin: -1.5rem 0 -1.5rem 250px;
  border-left: 5px solid var(--ct-body-bg);
  padding: 1.5rem 0 1.5rem 25px;
}

.message-list {
  display: block;
  padding-left: 0;
}
.message-list li {
  position: relative;
  display: block;
  height: 51px;
  line-height: 50px;
  cursor: default;
  -webkit-transition-duration: 0.3s;
          transition-duration: 0.3s;
}
.message-list li a {
  color: var(--ct-dropdown-link-color);
}
.message-list li:hover {
  background: var(--ct-dropdown-link-hover-bg);
  -webkit-transition-duration: 0.05s;
          transition-duration: 0.05s;
}
.message-list li .col-mail {
  float: left;
  position: relative;
}
.message-list li .col-mail-1 {
  width: 320px;
}
.message-list li .col-mail-1 .star-toggle,
.message-list li .col-mail-1 .checkbox-wrapper-mail,
.message-list li .col-mail-1 .dot {
  display: block;
  float: left;
}
.message-list li .col-mail-1 .dot {
  border: 4px solid transparent;
  border-radius: 100px;
  margin: 22px 26px 0;
  height: 0;
  width: 0;
  line-height: 0;
  font-size: 0;
}
.message-list li .col-mail-1 .checkbox-wrapper-mail {
  margin: 15px 10px 0 20px;
}
.message-list li .col-mail-1 .star-toggle {
  margin-top: 18px;
  color: var(--ct-gray-500);
  margin-left: 10px;
}
.message-list li .col-mail-1 .title {
  position: absolute;
  top: 0;
  left: 100px;
  right: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-bottom: 0;
  line-height: 50px;
}
.message-list li .col-mail-2 {
  position: absolute;
  top: 0;
  left: 320px;
  right: 0;
  bottom: 0;
}
.message-list li .col-mail-2 .subject,
.message-list li .col-mail-2 .date {
  position: absolute;
  top: 0;
}
.message-list li .col-mail-2 .subject {
  left: 0;
  right: 110px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.message-list li .col-mail-2 .date {
  right: 0;
  width: 100px;
  padding-left: 10px;
}
.message-list li.active,
.message-list li.mail-selected {
  background: var(--ct-gray-100);
  -webkit-transition-duration: 0.05s;
          transition-duration: 0.05s;
}
.message-list li.active,
.message-list li.active:hover {
  -webkit-box-shadow: inset 3px 0 0 #4fc6e1;
          box-shadow: inset 3px 0 0 #4fc6e1;
}
.message-list li.unread a {
  font-weight: 500;
  color: var(--ct-gray-800);
}
.message-list .checkbox-wrapper-mail {
  cursor: pointer;
  height: 20px;
  width: 20px;
  position: relative;
  display: inline-block;
  -webkit-box-shadow: inset 0 0 0 2px var(--ct-gray-400);
          box-shadow: inset 0 0 0 2px var(--ct-gray-400);
  border-radius: 3px;
}
.message-list .checkbox-wrapper-mail input {
  opacity: 0;
  cursor: pointer;
}
.message-list .checkbox-wrapper-mail input:checked ~ label {
  opacity: 1;
}
.message-list .checkbox-wrapper-mail label {
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  cursor: pointer;
  background: var(--ct-gray-600);
  opacity: 0;
  margin-bottom: 0 !important;
  -webkit-transition-duration: 0.05s;
          transition-duration: 0.05s;
}
.message-list .checkbox-wrapper-mail label:active {
  background: #87949b;
}

.mail-list a {
  color: var(--ct-dropdown-link-color);
  padding: 9px 10px;
  display: block;
  font-size: 15px;
}

.reply-box {
  border: 2px solid var(--ct-gray-100);
}

@media (max-width: 648px) {
  .inbox-leftbar {
    width: 100%;
    float: none;
    padding: 0 20px;
  }
  .inbox-leftbar:before {
    border-right: none;
  }
  .inbox-rightbar {
    padding-top: 40px;
    margin: 0;
    border: 0;
    padding-left: 0;
  }
  .message-list li .col-mail-1 .checkbox-wrapper-mail {
    margin-left: 0;
  }
}
@media (max-width: 520px) {
  .inbox-rightbar > .btn-group {
    margin-bottom: 10px;
  }
  .message-list li .col-mail-1 {
    width: 150px;
  }
  .message-list li .col-mail-1 .title {
    left: 80px;
  }
  .message-list li .col-mail-2 {
    left: 160px;
  }
  .message-list li .col-mail-2 .date {
    text-align: right;
    padding-right: 10px;
    padding-left: 20px;
  }
}
.sitemap {
  list-style: none;
  padding-left: 0;
}
.sitemap > li > ul {
  margin-top: 1rem;
  padding-left: 0;
}
.sitemap li {
  line-height: 1.5rem;
  vertical-align: top;
  list-style: none;
  position: relative;
}
.sitemap li a {
  text-decoration: none;
  color: var(--ct-dropdown-link-color);
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sitemap li a i {
  display: inline-block;
}
.sitemap li a:hover {
  color: #6658dd;
}
.sitemap ul {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
  padding-top: 10px;
}
.sitemap ul li {
  position: relative;
}
.sitemap ul li a {
  margin-left: 2rem;
}
.sitemap ul li:before {
  content: "";
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border-bottom: 1px solid var(--ct-gray-300);
  border-left: 1px solid var(--ct-gray-300);
  position: absolute;
  top: -1rem;
}

.search-result-box .tab-content {
  padding: 30px 30px 10px 30px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.search-result-box .search-item {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--ct-gray-100);
  margin-bottom: 20px;
}
.search-result-box .nav-bordered .nav-link {
  padding: 10px 5px !important;
  margin-right: 10px;
}

.card-pricing {
  position: relative;
}
.card-pricing .card-pricing-plan-name {
  padding-bottom: 20px;
}
.card-pricing .card-pricing-icon {
  font-size: 22px;
  background-color: rgba(102, 88, 221, 0.1);
  height: 60px;
  display: inline-block;
  width: 60px;
  line-height: 62px;
  border-radius: 50%;
}
.card-pricing .card-pricing-price {
  padding: 30px 0 0;
}
.card-pricing .card-pricing-price span {
  font-size: 40%;
  color: var(--ct-text-muted);
  letter-spacing: 2px;
  text-transform: uppercase;
}
.card-pricing .card-pricing-features {
  color: var(--ct-text-muted);
  list-style: none;
  margin: 0;
  padding: 20px 0 0 0;
}
.card-pricing .card-pricing-features li {
  padding: 10px;
}

.card-pricing-recommended {
  background-color: #6658dd;
  color: #fff;
}
.card-pricing-recommended .card-pricing-icon {
  background-color: rgba(255, 255, 255, 0.1);
}
.card-pricing-recommended .card-pricing-features, .card-pricing-recommended .card-pricing-price span {
  color: rgba(255, 255, 255, 0.7);
}

.filter-menu {
  margin-bottom: 20px;
}
.filter-menu a {
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  color: var(--ct-gray-900);
  border-radius: 3px;
  padding: 5px 10px;
  display: inline-block;
  margin-bottom: 5px;
  font-weight: 500;
  font-family: "Roboto", sans-serif;
}
.filter-menu a:hover {
  background-color: rgba(102, 88, 221, 0.15);
  color: #6658dd;
}
.filter-menu a.active {
  background-color: #6658dd;
  color: #fff;
}

.gal-box {
  background-color: var(--ct-card-bg);
  border-radius: 3px;
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  margin-bottom: 24px;
}
.gal-box .image-popup {
  padding: 10px;
  display: block;
}
.gal-box .image-popup img {
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}
.gal-box .gall-info {
  padding: 15px;
  border-top: 1px solid var(--ct-gray-200);
  position: relative;
}
.gal-box .gall-info h4 {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.gal-box .gall-info .gal-like-btn {
  position: absolute;
  right: 15px;
  font-size: 22px;
  top: 24px;
}

.counter-number {
  font-size: 32px;
  font-weight: 700;
  color: #fff;
}
.counter-number span {
  font-size: 15px;
  font-weight: 400;
  display: block;
}

.coming-box {
  float: left;
  width: 25%;
}

.svg-rocket {
  height: 80px;
}

.rocket-clouds__bubble,
.rocket-clouds__cloud,
.rocket-rocket,
.rocket-inner__rocket-and-lines {
  fill: #fff;
}

.post-user-comment-box {
  background-color: var(--ct-gray-100);
  margin: 0 -0.75rem;
  padding: 1rem;
  margin-top: 20px;
}

.task-item {
  padding-left: 12px;
  position: relative;
}
.task-item:before {
  content: "\f01db";
  font-family: "Material Design Icons";
  position: absolute;
  left: 0;
  font-size: 19px;
  top: -3px;
}

.tasklist {
  min-height: 40px;
  margin-bottom: 0;
}
.tasklist li {
  background-color: var(--ct-card-bg);
  border: 1px solid var(--ct-gray-300);
  padding: 20px;
  margin-bottom: 15px;
  border-radius: 3px;
  -webkit-box-shadow: var(--ct-components-shadow);
          box-shadow: var(--ct-components-shadow);
}
.tasklist li:last-of-type {
  margin-bottom: 0;
}
.tasklist li .btn-sm {
  padding: 2px 8px;
  font-size: 12px;
}
.tasklist .form-check {
  margin-left: 20px;
  margin-top: 5px;
}
.tasklist .form-check .form-check-input {
  margin-left: 0;
}

.task-placeholder {
  border: 1px dashed var(--ct-gray-300) !important;
  background-color: var(--ct-gray-100) !important;
  padding: 20px;
}

.product-box {
  position: relative;
  overflow: hidden;
}
.product-box .product-action {
  position: absolute;
  right: 0;
  top: 0;
  padding: 1.5rem 1.5rem 0 1.5rem;
  z-index: 3;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.product-box:hover .product-action {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.product-box .product-info {
  padding-top: 1.5rem;
}
.product-box .product-price-tag {
  height: 48px;
  line-height: 48px;
  font-weight: 700;
  font-size: 20px;
  background-color: var(--ct-gray-100);
  text-align: center;
  padding: 0 10px;
  border-radius: 3px;
}

.product-thumb {
  padding: 3px;
  margin-top: 3px;
}
.product-thumb.active {
  background-color: var(--ct-gray-700) !important;
}

.track-order-list ul li {
  position: relative;
  border-left: 2px solid var(--ct-gray-300);
  padding: 0px 0px 14px 21px;
}
.track-order-list ul li:first-child {
  padding-top: 0px;
}
.track-order-list ul li:last-child {
  padding-bottom: 0px;
}
.track-order-list ul li:before {
  content: "";
  position: absolute;
  left: -7px;
  top: 0;
  height: 12px;
  width: 12px;
  background-color: #6658dd;
  border-radius: 50%;
  border: 3px solid var(--ct-card-bg);
}
.track-order-list ul li.completed {
  border-color: #6658dd;
}
.track-order-list ul li .active-dot.dot {
  top: -9px;
  left: -16px;
  border-color: #6658dd;
}

.dot {
  border: 4px solid #6658dd;
  background: 0 0;
  border-radius: 60px;
  height: 30px;
  width: 30px;
  -webkit-animation: pulse 3s ease-out;
          animation: pulse 3s ease-out;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  position: absolute;
  top: -15px;
  right: -2px;
  z-index: 1;
  opacity: 0;
}

@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }
  25% {
    -webkit-transform: scale(0);
    opacity: 0.1;
  }
  50% {
    -webkit-transform: scale(0.1);
    opacity: 0.3;
  }
  75% {
    -webkit-transform: scale(0.5);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }
  25% {
    -webkit-transform: scale(0);
    opacity: 0.1;
  }
  50% {
    -webkit-transform: scale(0.1);
    opacity: 0.3;
  }
  75% {
    -webkit-transform: scale(0.5);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 0;
  }
}
/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
          transform: scale(0) translate(0, 0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important;
}

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-mask-image: radial-gradient(circle, white 100%, black 100%);
          mask-image: radial-gradient(circle, white 100%, black 100%);
}

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1;
}

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em;
}

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
  padding: 0;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
}

.waves-float {
  -webkit-mask-image: none;
          mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
          box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
          box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
  display: block;
}

.apex-charts {
  min-height: 10px !important;
}
.apex-charts text {
  font-family: "Roboto", sans-serif !important;
  fill: var(--ct-text-muted);
  font-weight: 500;
}
.apex-charts .apexcharts-canvas {
  margin: 0 auto;
}

.apexcharts-tooltip-title,
.apexcharts-tooltip-text,
.apexcharts-legend-text {
  font-family: "Roboto", sans-serif !important;
}

.apexcharts-legend-series {
  font-weight: 500;
}

.apexcharts-gridline {
  pointer-events: none;
  stroke: var(--ct-apex-grid-color);
}

.apexcharts-legend-text {
  color: var(--ct-text-muted) !important;
}

.apexcharts-yaxis text,
.apexcharts-xaxis text {
  font-family: "Roboto", sans-serif !important;
  fill: var(--ct-text-muted);
  font-weight: 500;
}

.apexcharts-point-annotations text,
.apexcharts-xaxis-annotations text,
.apexcharts-yaxis-annotations text {
  fill: var(--ct-text-muted);
}

.apexcharts-radar-series polygon {
  fill: transparent;
  stroke: var(--ct-gray-300);
}
.apexcharts-radar-series line {
  stroke: var(--ct-gray-300);
}

.apexcharts-pie-label,
.apexcharts-datalabel,
.apexcharts-datalabel-label,
.apexcharts-datalabel-value {
  fill: var(--ct-text-muted) !important;
}

.apexcharts-plot-series .apexcharts-datalabel {
  fill: #fff !important;
}

.apexcharts-datalabels-group text {
  fill: var(--ct-text-muted) !important;
  font-family: "Roboto", sans-serif !important;
}

.scatter-images-chart .apexcharts-legend {
  overflow: hidden !important;
  min-height: 17px;
}
.scatter-images-chart .apexcharts-legend-marker {
  background: none !important;
  margin-right: 7px !important;
}
.scatter-images-chart .apexcharts-legend-series {
  -webkit-box-align: start !important;
      -ms-flex-align: start !important;
          align-items: flex-start !important;
}

.apexcharts-pie-series path {
  stroke: transparent !important;
}

.apexcharts-track path {
  stroke: var(--ct-lighten-300);
}

.apexcharts-xaxis line {
  stroke: var(--ct-gray-400) !important;
}

.irs {
  position: relative;
  display: block;
  -webkit-touch-callout: none;
  -ms-user-select: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 12px;
}

.irs-line {
  position: relative;
  display: block;
  overflow: hidden;
  outline: none !important;
}

.irs-bar {
  position: absolute;
  display: block;
  width: 0;
}

.irs-shadow {
  position: absolute;
  display: none;
  width: 0;
}

.irs-handle {
  position: absolute;
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  cursor: default;
  z-index: 1;
}
.irs-handle.type_last {
  z-index: 2;
}

.irs-min,
.irs-max {
  position: absolute;
  display: block;
  cursor: default;
}

.irs-min {
  left: 0;
}

.irs-max {
  right: 0;
}

.irs-from,
.irs-to,
.irs-single {
  position: absolute;
  display: block;
  top: 0;
  cursor: default;
  white-space: nowrap;
}

.irs-grid {
  position: absolute;
  display: none;
  bottom: 0;
  width: 100%;
  height: 20px;
}

.irs-with-grid .irs-grid {
  display: block;
}

.irs-grid-pol {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 8px;
  background: var(--ct-gray-700);
}
.irs-grid-pol.small {
  height: 4px;
}

.irs-grid-text {
  position: absolute;
  bottom: 0;
  /*rtl:ignore*/
  left: 0;
  white-space: nowrap;
  text-align: center;
  font-size: 9px;
  line-height: 9px;
  padding: 0 3px;
  color: var(--ct-gray-700);
}

.irs-disable-mask {
  position: absolute;
  display: block;
  top: 0;
  left: -1%;
  width: 102%;
  height: 100%;
  cursor: default;
  z-index: 2;
}

.lt-ie9 .irs-disable-mask {
  background: var(--ct-gray-700);
  filter: alpha(opacity=0);
  cursor: not-allowed;
}

.irs-disabled {
  opacity: 0.4;
}

.irs-hidden-input {
  position: absolute !important;
  display: block !important;
  top: 0 !important;
  left: 0 !important;
  width: 0 !important;
  height: 0 !important;
  font-size: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden;
  outline: none !important;
  z-index: -9999 !important;
  background: none !important;
  border-style: solid !important;
  border-color: transparent !important;
}

.irs--flat {
  height: 40px;
}
.irs--flat.irs-with-grid {
  height: 60px;
}
.irs--flat .irs-line {
  top: 25px;
  height: 12px;
  background-color: var(--ct-progress-bg);
  border-radius: 4px;
}
.irs--flat .irs-bar {
  top: 25px;
  height: 12px;
  background-color: #6658dd;
}
.irs--flat .irs-bar--single {
  /*rtl:ignore*/
  border-radius: 4px 0 0 4px;
}
.irs--flat .irs-shadow {
  height: 1px;
  bottom: 16px;
  background-color: var(--ct-progress-bg);
}
.irs--flat .irs-handle {
  top: 22px;
  width: 16px;
  height: 18px;
  background-color: transparent;
}
.irs--flat .irs-handle > i:first-child {
  position: absolute;
  display: block;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  margin-left: -1px;
  background-color: #4b3ad7 !important;
}
.irs--flat .irs-min,
.irs--flat .irs-max {
  top: 0;
  padding: 1px 3px;
  color: var(--ct-gray-700);
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  background-color: var(--ct-progress-bg);
  border-radius: 4px;
}
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single {
  color: #fff;
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  background-color: #6658dd;
  border-radius: 4px;
}
.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #6658dd;
}
.irs--flat .irs-grid-pol {
  background-color: var(--ct-progress-bg);
}
.irs--flat .irs-grid-text {
  color: var(--ct-gray-700);
}

.irs--modern .irs-line {
  border: none;
  background: var(--ct-progress-bg);
}
.irs--modern .irs-bar {
  background: #6658dd;
  background: -webkit-gradient(linear, left top, left bottom, from(#6658dd), to(#3f2ed4));
  background: linear-gradient(to bottom, #6658dd 0%, #3f2ed4 100%);
}
.irs--modern .irs-min,
.irs--modern .irs-max {
  top: 0;
  padding: 1px 3px;
  color: var(--ct-gray-700);
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  background-color: var(--ct-progress-bg);
  border-radius: 4px;
}

.irs--sharp .irs-from,
.irs--sharp .irs-to,
.irs--sharp .irs-single,
.irs--sharp .irs-min,
.irs--sharp .irs-max,
.irs--sharp .irs-handle,
.irs--sharp .irs-bar {
  background-color: #6658dd;
}
.irs--sharp .irs-line {
  background: var(--ct-progress-bg);
}
.irs--sharp .irs-from:before,
.irs--sharp .irs-to:before,
.irs--sharp .irs-single:before,
.irs--sharp .irs-handle > i:first-child {
  border-top-color: #6658dd;
}
.irs--sharp .irs-handle.state_hover,
.irs--sharp .irs-handle:hover {
  background-color: #3827c1;
}
.irs--sharp .irs-handle.state_hover > i:first-child,
.irs--sharp .irs-handle:hover > i:first-child {
  border-top-color: #3827c1;
}

.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single,
.irs--round .irs-bar {
  background-color: #6658dd;
}
.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before,
.irs--round .irs-bar:before {
  border-top-color: #6658dd;
}
.irs--round .irs-handle {
  background-color: var(--ct-progress-bg);
  border: 4px solid #6658dd;
  -webkit-box-shadow: 0 1px 3px rgba(102, 88, 221, 0.3);
          box-shadow: 0 1px 3px rgba(102, 88, 221, 0.3);
}
.irs--round .irs-min,
.irs--round .irs-max {
  color: var(--ct-gray-700);
  background-color: var(--ct-gray-300);
}
.irs--round .irs-line {
  background: var(--ct-progress-bg);
}

.irs--square .irs-from,
.irs--square .irs-to,
.irs--square .irs-single,
.irs--square .irs-bar {
  background-color: var(--ct-progress-bg);
}
.irs--square .irs-handle {
  border: 3px solid #6658dd;
  background-color: var(--ct-progress-bg);
}
.irs--square .irs-line {
  background: var(--ct-progress-bg);
}
.irs--square .irs-min,
.irs--square .irs-max {
  top: 0;
  padding: 1px 3px;
  color: var(--ct-gray-700);
  font-size: 10px;
  line-height: 1.333;
  text-shadow: none;
  background-color: var(--ct-progress-bg);
  border-radius: 4px;
}

.calendar {
  float: left;
  margin-bottom: 0;
}

#calendar .table-bordered td,
#calendar .table-bordered th {
  border: 1px solid var(--ct-table-border-color);
}

.none-border .modal-footer {
  border-top: none;
}

.fc-toolbar {
  margin: 10px 0 5px 0;
}
.fc-toolbar .fc-toolbar-title {
  font-size: 1.25rem;
  line-height: 1.875rem;
  text-transform: uppercase;
}

.fc-day-grid-event .fc-time {
  font-weight: 500;
}

.fc-event-time,
.fc-event-title {
  color: #fff;
}

th.fc-col-header-cell {
  padding: 0.3rem 0;
}

.fc-day {
  background: transparent;
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0;
}

.fc th.fc-widget-header {
  background: var(--ct-gray-300);
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase;
  font-weight: 500;
}

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
  border-color: var(--ct-gray-300);
}
.fc-unthemed td.fc-today,
.fc-unthemed .fc-divider {
  background: var(--ct-gray-300);
}

.fc-button {
  background: var(--ct-gray-300);
  border: none;
  color: var(--ct-gray-700);
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  margin: 0 3px;
  padding: 6px 12px;
  height: auto;
}

.fc-text-arrow {
  font-family: inherit;
  font-size: 1rem;
}

.fc-state-hover,
.fc-state-highlight,
.fc-cell-overlay {
  background: var(--ct-gray-300);
}

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background-color: #6658dd;
  color: #fff;
  text-shadow: none;
}

.fc-unthemed .fc-today {
  background: #fff;
}

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center;
}

.external-event {
  cursor: move;
  margin: 10px 0;
  padding: 8px 10px;
  color: #fff;
  border-radius: 4px;
}

.fc-basic-view td.fc-week-number span {
  padding-right: 8px;
}
.fc-basic-view td.fc-day-number {
  padding-right: 8px;
}
.fc-basic-view .fc-content {
  color: #fff;
}

.fc-time-grid-event .fc-content {
  color: #fff;
}

.fc-content-skeleton .fc-day-top .fc-day-number {
  float: right;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  background-color: var(--ct-gray-100);
  border-radius: 50%;
  margin: 5px;
  font-size: 11px;
}

@media (max-width: 767.98px) {
  .fc-toolbar {
    display: block !important;
  }
  .fc-toolbar .fc-left,
.fc-toolbar .fc-right,
.fc-toolbar .fc-center {
    float: none;
    display: block;
    clear: both;
    margin: 10px 0;
  }
  .fc .fc-toolbar > * > * {
    float: none;
  }
  .fc-today-button {
    display: none;
  }
}
.fc-toolbar .btn {
  --ct-btn-padding-y: 0.28rem;
  --ct-btn-padding-x: 0.8rem;
  --ct-btn-font-size: 0.7875rem;
  --ct-btn-border-radius: 0.2rem;
}

.fc-list-item-title,
.fc-list-item-time {
  color: #fff;
}

[dir=rtl] .fc-toolbar .btn-group .btn:first-child {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-radius: 0.2rem;
}
[dir=rtl] .fc-toolbar .btn-group .btn:last-child {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
  border-radius: 0.2rem;
}

.colorpicker {
  background: var(--ct-dropdown-bg);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  border: 1px solid var(--ct-dropdown-border-color);
}

.sp-picker-container .sp-alpha-handle {
  right: 0;
  left: auto;
}

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #fff !important;
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #fff !important;
}

.jqsfield {
  color: #000 !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: "Roboto", sans-serif !important;
  font-weight: 500 !important;
}

.dataTables_wrapper.container-fluid {
  padding: 0;
}

table.dataTable {
  border-collapse: collapse !important;
  margin-bottom: 15px !important;
}
table.dataTable tbody > tr.selected,
table.dataTable tbody > tr > .selected {
  background-color: #6658dd;
}
table.dataTable tbody > tr.selected td,
table.dataTable tbody > tr > .selected td {
  border-color: #6658dd;
}
table.dataTable tbody td:focus {
  outline: none !important;
}
table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
  outline: 2px solid #6658dd !important;
  outline-offset: -1px;
  color: #6658dd;
  background-color: rgba(102, 88, 221, 0.15);
}
table.dataTable thead > tr > th.sorting, table.dataTable thead > tr > th.sorting_asc, table.dataTable thead > tr > th.sorting_desc, table.dataTable thead > tr > th.sorting_asc_disabled, table.dataTable thead > tr > th.sorting_desc_disabled {
  padding-right: 30px;
  padding-left: 0.85rem;
}
table.dataTable thead > tr > th.sorting:before, table.dataTable thead > tr > th.sorting_asc:before, table.dataTable thead > tr > th.sorting_desc:before, table.dataTable thead > tr > th.sorting_asc_disabled:before, table.dataTable thead > tr > th.sorting_desc_disabled:before {
  right: 1em;
  left: auto;
  content: "↑";
}
table.dataTable thead > tr > th.sorting:after, table.dataTable thead > tr > th.sorting_asc:after, table.dataTable thead > tr > th.sorting_desc:after, table.dataTable thead > tr > th.sorting_asc_disabled:after, table.dataTable thead > tr > th.sorting_desc_disabled:after {
  right: 0.5em;
  left: auto;
  content: "↓";
}

.dataTables_info {
  font-weight: 500;
}

table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
  background-color: #1abc9c;
  top: 0.85rem;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  background-color: #f1556c;
  top: 0.85rem;
}

div.dt-button-info {
  background-color: #6658dd;
  border: none;
  color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 3px;
  text-align: center;
  z-index: 21;
}
div.dt-button-info h2 {
  border-bottom: none;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

@media (max-width: 767.98px) {
  li.paginate_button.previous,
li.paginate_button.next {
    display: inline-block;
    font-size: 1.5rem;
  }
  li.paginate_button {
    display: none;
  }
  .dataTables_paginate ul {
    text-align: center;
    display: block;
    margin: 1.5rem 0 0 !important;
  }
  div.dt-buttons {
    display: inline-table;
    margin-bottom: 1.5rem;
  }
}
.activate-select .sorting_1 {
  background-color: var(--ct-dropdown-link-hover-bg);
}

div.dataTables_wrapper div.dataTables_filter {
  text-align: right;
}
@media (max-width: 767px) {
  div.dataTables_wrapper div.dataTables_filter {
    text-align: center;
  }
}
div.dataTables_wrapper div.dataTables_filter input {
  margin-left: 0.5em;
  margin-right: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {
  padding-right: 0;
}
div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {
  padding-left: 0;
}

table.dataTable > thead .sorting:before,
table.dataTable > thead .sorting:after,
table.dataTable > thead .sorting_asc:before,
table.dataTable > thead .sorting_asc:after,
table.dataTable > thead .sorting_desc:before,
table.dataTable > thead .sorting_desc:after,
table.dataTable > thead .sorting_asc_disabled:before,
table.dataTable > thead .sorting_asc_disabled:after,
table.dataTable > thead .sorting_desc_disabled:before,
table.dataTable > thead .sorting_desc_disabled:after {
  bottom: 1em;
}

.daterangepicker {
  font-family: "Roboto", sans-serif;
}
.daterangepicker td.active,
.daterangepicker td.active:hover,
.daterangepicker .ranges li.active {
  background-color: #6658dd;
}

.form-wizard-header {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.select2-container .select2-selection--single {
  border: 1px solid var(--ct-input-border-color);
  height: calc(1.5em + 0.9rem + 2px);
  background-color: var(--ct-input-bg);
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
  outline: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  line-height: 36px;
  padding-left: 12px;
  color: var(--ct-input-color);
  text-align: left;
}
.select2-container .select2-selection--single .select2-selection__arrow {
  height: 34px;
  width: 34px;
  right: 3px;
}
.select2-container .select2-selection--single .select2-selection__arrow b {
  border-color: var(--ct-gray-500) transparent transparent transparent;
  border-width: 6px 6px 0 6px;
}
.select2-container input::-webkit-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.select2-container input::-moz-placeholder {
  color: var(--ct-input-placeholder-color);
}
.select2-container input:-ms-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.select2-container input::-ms-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.select2-container input::placeholder {
  color: var(--ct-input-placeholder-color);
}

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent var(--ct-gray-500) transparent !important;
  border-width: 0 6px 6px 6px !important;
}

.select2-results__option {
  padding: 6px 12px;
  text-align: left;
}

.select2-dropdown {
  border: 1px solid var(--ct-dropdown-border-color);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  background-color: var(--ct-dropdown-bg);
}

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: var(--ct-dropdown-bg);
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  outline: none;
  border: 1px solid var(--ct-input-border-color);
  background-color: var(--ct-input-bg);
  color: var(--ct-input-color);
  text-align: left;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #6658dd;
}
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: var(--ct-dropdown-bg);
  color: var(--ct-dropdown-link-active-color);
}
.select2-container--default .select2-results__option[aria-selected=true]:hover {
  background-color: #6658dd;
  color: #fff;
}

.select2-container .select2-selection--multiple {
  min-height: calc(1.5em + 0.9rem + 2px);
  border: 1px solid var(--ct-input-border-color) !important;
  background-color: var(--ct-input-bg);
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
  padding: 1px 10px;
}
.select2-container .select2-selection--multiple .select2-search__field {
  border: 0;
  color: var(--ct-input-color);
}
.select2-container .select2-selection--multiple .select2-selection__choice {
  background-color: #6658dd;
  border: none;
  color: #fff;
  border-radius: 3px;
  padding: 0 7px;
  margin-top: 6px;
}
.select2-container .select2-selection--multiple .select2-selection__choice__remove {
  color: #fff;
  margin-right: 5px;
}
.select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}
.select2-container .select2-search--inline .select2-search__field {
  margin-top: 7px;
}

.select2-selection {
  overflow: hidden;
}

.select2-selection__rendered {
  white-space: normal;
  word-break: break-all;
}

[data-simplebar] {
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-line-pack: start;
      align-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

.simplebar-offset {
  direction: inherit !important;
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0 !important;
  bottom: 0;
  right: 0 !important;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

.simplebar-content-wrapper {
  direction: inherit;
  -webkit-box-sizing: border-box !important;
          box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  overflow: auto;
  /* Scroll on this element otherwise element can't have a padding applied properly */
  max-width: 100%;
  /* Not required for horizontal scroll to trigger */
  max-height: 100%;
  /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  padding: 0px !important;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.simplebar-content:before,
.simplebar-content:after {
  content: " ";
  display: table;
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

.simplebar-height-auto-observer-wrapper {
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  -webkit-box-flex: inherit;
      -ms-flex-positive: inherit;
          flex-grow: inherit;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
}

.simplebar-height-auto-observer {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

.simplebar-scrollbar {
  position: absolute;
  right: 2px;
  width: 4px;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #a2adb7;
  border-radius: 7px;
  left: 0;
  right: 0;
  opacity: 0;
  -webkit-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  -webkit-transition: opacity 0s linear;
  transition: opacity 0s linear;
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px;
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
}

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  left: 2px;
  right: 2px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0;
}

.hs-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
}

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
}

.custom-scroll {
  height: 100%;
}

.jq-toast-single {
  padding: 15px;
  font-family: "Roboto", sans-serif;
  background-color: #6658dd;
  font-size: 13px;
  line-height: 22px;
}
.jq-toast-single h2 {
  font-family: "Roboto", sans-serif;
}
.jq-toast-single a {
  font-size: 0.875rem;
}
.jq-toast-single a:hover {
  color: #fff;
}

.jq-has-icon {
  padding: 10px;
}

.close-jq-toast-single {
  position: absolute;
  top: -12px;
  right: -12px;
  font-size: 20px;
  cursor: pointer;
  height: 32px;
  width: 32px;
  background-color: var(--ct-gray-900);
  border-radius: 50%;
  text-align: center;
  line-height: 32px;
  color: var(--ct-gray-100);
}

.jq-toast-loader {
  height: 3px;
  top: 0;
  border-radius: 0;
}

.jq-icon-primary {
  background-color: #6658dd;
  color: #fff;
  border-color: #6658dd;
}

.jq-icon-secondary {
  background-color: #6c757d;
  color: #fff;
  border-color: #6c757d;
}

.jq-icon-success {
  background-color: #1abc9c;
  color: #fff;
  border-color: #1abc9c;
}

.jq-icon-info {
  background-color: #4fc6e1;
  color: #fff;
  border-color: #4fc6e1;
}

.jq-icon-warning {
  background-color: #f7b84b;
  color: #fff;
  border-color: #f7b84b;
}

.jq-icon-danger {
  background-color: #f1556c;
  color: #fff;
  border-color: #f1556c;
}

.jq-icon-light {
  background-color: #eceff1;
  color: #fff;
  border-color: #eceff1;
}

.jq-icon-dark {
  background-color: #323a46;
  color: #fff;
  border-color: #323a46;
}

.jq-icon-pink {
  background-color: #f672a7;
  color: #fff;
  border-color: #f672a7;
}

.jq-icon-blue {
  background-color: #4a81d4;
  color: #fff;
  border-color: #4a81d4;
}

.jq-icon-error {
  background-color: #f1556c;
  color: var(--ct-gray-100);
  border-color: #f1556c;
}

.jq-icon-info, .jq-icon-warning, .jq-icon-error, .jq-icon-success {
  background-image: none;
}

.close-jq-toast-single {
  color: var(--ct-gray-100);
}

.swal2-modal {
  font-family: "Roboto", sans-serif;
  -webkit-box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 33px rgba(0, 0, 0, 0.1);
}
.swal2-modal .swal2-title {
  font-size: 24px;
}
.swal2-modal .swal2-content {
  font-size: 16px;
}
.swal2-modal .swal2-spacer {
  margin: 10px 0;
}
.swal2-modal .swal2-file,
.swal2-modal .swal2-input,
.swal2-modal .swal2-textarea {
  border: 2px solid var(--ct-gray-300);
  font-size: 16px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.swal2-modal .swal2-confirm {
  background-color: #6658dd !important;
  font-size: 0.875rem !important;
}
.swal2-modal .swal2-cancel.btn-cancel {
  background-color: #f1556c !important;
  font-size: 0.875rem;
}
.swal2-modal .swal2-confirm, .swal2-modal .swal2-cancel {
  margin: 0.3125em;
  padding: 0.625em 2em;
  font-weight: 500;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 0.875rem !important;
}
.swal2-modal .swal2-confirm:focus, .swal2-modal .swal2-cancel:focus {
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}
.swal2-modal .swal2-file:focus,
.swal2-modal .swal2-input:focus,
.swal2-modal .swal2-textarea:focus {
  outline: 0;
  border: 2px solid #6658dd;
}

.swal2-icon.swal2-question {
  color: #6658dd !important;
  border-color: #6658dd !important;
}
.swal2-icon.swal2-success {
  border-color: #1abc9c;
}
.swal2-icon.swal2-success .line,
.swal2-icon.swal2-success [class^=swal2-success-line][class$=long],
.swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: #1abc9c !important;
}
.swal2-icon.swal2-success .placeholder,
.swal2-icon.swal2-success .swal2-success-ring {
  border-color: #1abc9c !important;
}
.swal2-icon.swal2-warning {
  color: #f7b84b !important;
  border-color: #f7b84b !important;
}
.swal2-icon.swal2-error {
  border-color: #f1556c !important;
}
.swal2-icon.swal2-error .line {
  background-color: #f1556c !important;
}
.swal2-icon.swal2-info {
  border-color: #4fc6e1;
  color: #4fc6e1;
}

.swal2-actions {
  margin: 1.6em auto 0 !important;
}

.swal2-container.swal2-backdrop-show, .swal2-container.swal2-noanimation {
  background-color: rgba(var(--ct-modal-backdrop-bg), 0.5) !important;
}

.selectize-input {
  min-height: calc(1.5em + 0.9rem + 2px);
  padding: 0.45rem 0.9rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--ct-input-color);
  background-color: var(--ct-input-bg) !important;
  border: 1px solid var(--ct-input-border-color);
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
}
.selectize-input > input {
  color: var(--ct-input-color);
}
.selectize-input > input::-webkit-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.selectize-input > input::-moz-placeholder {
  color: var(--ct-input-placeholder-color);
}
.selectize-input > input:-ms-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.selectize-input > input::-ms-input-placeholder {
  color: var(--ct-input-placeholder-color);
}
.selectize-input > input::placeholder {
  color: var(--ct-input-placeholder-color);
}
.selectize-input.focus {
  color: var(--ct-input-focus-color);
  background-color: var(--ct-input-focus-bg);
  border-color: var(--ct-input-focus-border-color);
  outline: 0;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.selectize-control.multi .selectize-input > div {
  padding: 1px 8px;
  background: var(--ct-lighten-300);
  color: var(--ct-gray-800);
}
.selectize-control.multi .selectize-input > div > a {
  color: var(--ct-gray-800);
}
.selectize-control.multi .selectize-input > div.active {
  background: #6658dd;
}
.selectize-control.multi .selectize-input > div.active > a {
  color: color-yiq(#6658dd);
}
.selectize-control.single .selectize-input:after {
  border-style: solid;
  border-width: 0 2px 2px 0;
  border-color: transparent var(--ct-gray-500) var(--ct-gray-500) transparent;
  content: "";
  display: block;
  height: 7px;
  margin-top: -5px;
  pointer-events: none;
  position: absolute;
  right: 15px;
  left: auto;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
          transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  width: 7px;
}
.selectize-control.single .selectize-input.dropdown-active:after {
  border-width: 0 2px 2px 0;
  margin-top: -5px;
  border-color: transparent var(--ct-gray-500) var(--ct-gray-500) transparent;
  -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
}

.selectize-dropdown {
  padding: 0.3rem;
  color: var(--ct-dropdown-color);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  background-color: var(--ct-dropdown-bg);
  background-clip: padding-box;
  border: 1px solid var(--ct-dropdown-border-color);
  border-radius: 0.25rem;
  -webkit-animation-name: DropDownSlide;
          animation-name: DropDownSlide;
  -webkit-animation-duration: 0.3s;
          animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  margin: 0;
  font-size: 0.875rem;
  position: absolute;
  z-index: 1000;
}
.selectize-dropdown.show {
  top: 100% !important;
}
.selectize-dropdown.active {
  color: var(--ct-dropdown-link-hover-color);
  background-color: var(--ct-dropdown-link-hover-bg);
}
.selectize-dropdown .scientific {
  color: var(--ct-gray-600);
}
.selectize-dropdown .option,
.selectize-dropdown .optgroup-header {
  display: block;
  width: 100%;
  padding: 0.375rem 1.2rem;
  clear: both;
  cursor: pointer;
  font-weight: 400;
  color: var(--ct-dropdown-link-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
}
.selectize-dropdown .option:hover, .selectize-dropdown .option:focus,
.selectize-dropdown .optgroup-header:hover,
.selectize-dropdown .optgroup-header:focus {
  color: var(--ct-dropdown-link-hover-color);
  text-decoration: none;
  background-color: var(--ct-dropdown-link-hover-bg) !important;
}
.selectize-dropdown.plugin-optgroup_columns .optgroup {
  border-right-color: var(--ct-gray-300);
}
.selectize-dropdown .optgroup:before {
  background-color: var(--ct-gray-300);
}

.selectize-dropdown-header {
  border-bottom: 1px solid transparent;
  color: var(--ct-dropdown-link-active-color);
  background-color: var(--ct-dropdown-link-active-bg);
  text-decoration: none;
}
.selectize-dropdown-header .selectize-dropdown-header-close {
  right: 12px;
  left: auto;
}

.selectize-dropdown-content > div {
  background-color: transparent !important;
  color: var(--ct-input-color) !important;
}

.tippy-tooltip .light-theme[data-animatefill] {
  background-color: transparent;
}

.light-theme {
  color: color-yiq(#fff);
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
  background-color: #fff;
}
.light-theme .tippy-backdrop {
  background-color: #fff;
}
.light-theme .tippy-roundarrow {
  fill: #fff;
}

.gradient-theme .tippy-backdrop {
  background: #6658dd;
  background: -webkit-gradient(linear, right top, left top, from(#f1556c), to(#6658dd));
  background: linear-gradient(to left, #f1556c, #6658dd);
}

.tippy-popper[x-placement^=top] .tippy-tooltip.light-theme .tippy-arrow {
  border-top: 7px solid #fff;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
}
.tippy-popper[x-placement^=bottom] .tippy-tooltip.light-theme .tippy-arrow {
  border-bottom: 7px solid #fff;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
}
.tippy-popper[x-placement^=left] .tippy-tooltip.light-theme .tippy-arrow {
  border-left: 7px solid #fff;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}
.tippy-popper[x-placement^=right] .tippy-tooltip.light-theme .tippy-arrow {
  border-right: 7px solid #fff;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

.dd-list .dd-item .dd-handle {
  border: none;
  padding: 8px 16px;
  height: auto;
  font-weight: 600;
  border-radius: 3px;
  background: var(--ct-dropdown-link-hover-bg);
  color: var(--ct-dropdown-link-color);
}
.dd-list .dd-item .dd-handle:hover {
  color: #6658dd;
}
.dd-list .dd-item button {
  height: 36px;
  font-size: 17px;
  margin: 0;
  color: var(--ct-gray-600);
  width: 36px;
}
.dd-list .dd3-item {
  margin: 5px 0;
}
.dd-list .dd3-item .dd-item button {
  width: 36px;
  height: 36px;
}
.dd-list .dd3-handle {
  margin: 0;
  height: 36px !important;
  float: left;
}
.dd-list .dd3-content {
  height: auto;
  border: none;
  padding: 8px 16px 8px 46px;
  background: var(--ct-dropdown-link-hover-bg);
  color: var(--ct-dropdown-link-color);
  font-weight: 600;
}
.dd-list .dd3-content:hover {
  color: #6658dd;
}
.dd-list .dd3-handle:before {
  content: "\f035c";
  font-family: "Material Design Icons";
  color: var(--ct-gray-500);
}

.dd-empty,
.dd-placeholder {
  background: rgba(var(--ct-gray-400-rgb), 0.2);
}

.dd-dragel .dd-handle {
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
}

div.hopscotch-bubble {
  border: 3px solid #6658dd;
  border-radius: 5px;
  z-index: 999;
}
div.hopscotch-bubble .hopscotch-next,
div.hopscotch-bubble .hopscotch-prev {
  background-color: #6658dd !important;
  background-image: none !important;
  border-color: #6658dd !important;
  text-shadow: none !important;
  margin: 0 0 0 5px !important;
  font-family: "Roboto", sans-serif;
  color: #fff !important;
}
div.hopscotch-bubble .hopscotch-bubble-number {
  background: #f1556c;
  padding: 0;
  border-radius: 50%;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow-border {
  border-right: 19px solid #6658dd;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.left .hopscotch-bubble-arrow {
  border: none;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow {
  border-left: 19px solid #6658dd;
  left: -2px;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.right .hopscotch-bubble-arrow-border {
  border-left: 0 solid #6658dd;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {
  border-bottom: 19px solid #6658dd;
  top: 0;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow-border {
  border-bottom: 0 solid rgba(0, 0, 0, 0.5);
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow {
  border-top: 19px solid #6658dd;
  top: -2px;
}
div.hopscotch-bubble .hopscotch-bubble-arrow-container.down .hopscotch-bubble-arrow-border {
  border-top: 0 solid rgba(0, 0, 0, 0.5);
}
div.hopscotch-bubble h3 {
  font-family: "Roboto", sans-serif;
  margin-bottom: 10px;
}
div.hopscotch-bubble .hopscotch-content {
  font-family: "Roboto", sans-serif;
}

.flotTip {
  padding: 8px 12px;
  background-color: var(--ct-gray-900);
  z-index: 100;
  color: var(--ct-gray-100);
  opacity: 1;
  border-radius: 3px;
}

.legend {
  font-size: 14px;
}
.legend tr {
  height: 30px;
  font-family: "Roboto", sans-serif;
}
.legend > div {
  background-color: transparent !important;
}

.legendLabel {
  padding-left: 5px !important;
  line-height: 10px;
  padding-right: 10px;
  font-size: 13px;
  font-weight: 500;
  color: var(--ct-gray-600);
  text-transform: uppercase;
}

.legendColorBox div div {
  border-radius: 50%;
}

.flot-text {
  color: var(--ct-text-muted) !important;
}

.flot-svg text {
  fill: var(--ct-text-muted) !important;
}

@media (max-width: 767.98px) {
  .legendLabel {
    display: none;
  }
}
.legendIcon {
  width: 1.5em;
  height: 1.5em;
}

.morris-chart text {
  font-family: "Roboto", sans-serif !important;
  fill: var(--ct-body-color);
}

.morris-hover {
  position: absolute;
  z-index: 10;
}
.morris-hover.morris-default-style {
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  padding: 10px 12px;
  background: var(--ct-gray-900);
  color: var(--ct-gray-100);
  font-family: "Roboto", sans-serif;
}
.morris-hover.morris-default-style .morris-hover-row-label {
  font-weight: bold;
  margin: 0.25em 0;
  font-family: "Roboto", sans-serif;
}
.morris-hover.morris-default-style .morris-hover-point {
  white-space: nowrap;
  margin: 0.1em 0;
  color: #fff;
}

.chartjs-chart {
  margin: auto;
  position: relative;
  width: 100%;
}

.ct-golden-section:before {
  float: none;
}

.ct-grid {
  stroke: rgba(var(--ct-text-muted), 0.2);
}

.ct-chart {
  max-height: 300px;
}
.ct-chart .ct-label {
  fill: var(--ct-gray-500);
  color: var(--ct-gray-500);
  font-size: 12px;
  line-height: 1;
}

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #fff;
  fill: #fff;
  font-size: 16px;
}

.ct-chart .ct-series.ct-series-a .ct-bar,
.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #4a81d4;
}
.ct-chart .ct-series.ct-series-b .ct-bar,
.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: #1abc9c;
}
.ct-chart .ct-series.ct-series-c .ct-bar,
.ct-chart .ct-series.ct-series-c .ct-line,
.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #f7b84b;
}
.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point,
.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #f1556c;
}
.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point,
.ct-chart .ct-series.ct-series-e .ct-slice-donut {
  stroke: #4fc6e1;
}
.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point,
.ct-chart .ct-series.ct-series-f .ct-slice-donut {
  stroke: #f672a7;
}
.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point,
.ct-chart .ct-series.ct-series-g .ct-slice-donut {
  stroke: #6658dd;
}

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #4a81d4;
}

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #1abc9c;
}

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #f7b84b;
}

.ct-series-d .ct-area,
.ct-series-d .ct-slice-pie {
  fill: #f1556c;
}

.ct-area {
  fill-opacity: 0.33;
}

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  background: var(--ct-gray-900);
  color: var(--ct-gray-100);
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}
.chartist-tooltip.tooltip-show {
  opacity: 1;
}

.c3-tooltip {
  -webkit-box-shadow: var(--ct-box-shadow-lg);
          box-shadow: var(--ct-box-shadow-lg);
  opacity: 1;
}
.c3-tooltip td {
  border-left: none;
  font-family: "Roboto", sans-serif;
}
.c3-tooltip td > span {
  background: var(--ct-gray-900);
}
.c3-tooltip tr {
  border: none !important;
}
.c3-tooltip th {
  background-color: var(--ct-gray-900);
  color: var(--ct-gray-100);
}

.c3-chart-arcs-title {
  font-size: 18px;
  font-weight: 600;
}

.c3 text {
  font-family: "Roboto", sans-serif;
  fill: var(--ct-body-color);
}

.c3-legend-item {
  font-family: "Roboto", sans-serif;
  font-size: 14px;
}

.c3 line,
.c3 path {
  stroke: var(--ct-gray-400);
}

.c3-chart-arc.c3-target g path {
  stroke: #fff;
}

#legend {
  background: #fff;
  position: absolute;
  top: 0;
  right: 15px;
}
#legend .line {
  color: var(--ct-gray-900);
}

.rickshaw_graph svg {
  max-width: 100%;
}

.rickshaw_legend .label {
  font-family: inherit;
  letter-spacing: 0.01em;
  font-weight: 600;
}

.rickshaw_graph .detail .item,
.rickshaw_graph .detail .x_label,
.rickshaw_graph .x_tick .title {
  font-family: "Roboto", sans-serif;
}

.gauge-chart text {
  font-family: "Roboto", sans-serif !important;
}

.responsive-table-plugin .dropdown-menu li.checkbox-row {
  padding: 7px 15px;
  color: var(--ct-dropdown-link-color);
}
.responsive-table-plugin .table-responsive {
  border: none;
  margin-bottom: 0;
}
.responsive-table-plugin .btn-toolbar {
  display: block;
}
.responsive-table-plugin tbody th {
  font-size: 14px;
  font-weight: normal;
}
.responsive-table-plugin .checkbox-row {
  padding-left: 40px;
}
.responsive-table-plugin .checkbox-row label {
  display: inline-block;
  padding-left: 5px;
  position: relative;
  margin-bottom: 0;
}
.responsive-table-plugin .checkbox-row label::before {
  background-color: transparent;
  border-radius: 3px;
  border: 1px solid var(--ct-gray-400);
  content: "";
  display: inline-block;
  height: 17px;
  left: 0;
  margin-left: -20px;
  position: absolute;
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
  width: 17px;
  outline: none;
}
.responsive-table-plugin .checkbox-row label::after {
  color: var(--ct-gray-400);
  display: inline-block;
  font-size: 9px;
  height: 16px;
  left: 0;
  margin-left: -19px;
  padding-left: 3px;
  padding-top: 1px;
  position: absolute;
  top: -2px;
  width: 16px;
}
.responsive-table-plugin .checkbox-row input[type=checkbox] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:disabled + label {
  opacity: 0.65;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:focus + label::before {
  outline-offset: -2px;
  outline: none;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:checked + label::after {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:disabled + label::before {
  background-color: var(--ct-gray-300);
  cursor: not-allowed;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:checked + label::before {
  background-color: transparent;
  border-color: #6658dd;
}
.responsive-table-plugin .checkbox-row input[type=checkbox]:checked + label::after {
  color: #6658dd;
}
.responsive-table-plugin table.focus-on tbody tr.focused th,
.responsive-table-plugin table.focus-on tbody tr.focused td,
.responsive-table-plugin .sticky-table-header {
  background: #6658dd;
  --ct-table-accent-bg: $primary !important;
  border-color: #6658dd;
  color: #fff;
}
.responsive-table-plugin table.focus-on tbody tr.focused th table,
.responsive-table-plugin table.focus-on tbody tr.focused td table,
.responsive-table-plugin .sticky-table-header table {
  color: #fff;
}
.responsive-table-plugin .fixed-solution .sticky-table-header {
  top: 70px !important;
}
.responsive-table-plugin .btn-default {
  --ct-btn-color: var(--ct-gray-900);
  --ct-btn-hover-bg: var(--ct-gray-100);
  --ct-btn-border-color: var(--ct-gray-300);
  --ct-btn-hover-border-color: var(--ct-gray-100);
}
.responsive-table-plugin .btn-default.btn-primary {
  background-color: #6658dd !important;
  border-color: #6658dd !important;
  color: #fff !important;
  -webkit-box-shadow: 0 0 0 2px rgba(102, 88, 221, 0.5);
          box-shadow: 0 0 0 2px rgba(102, 88, 221, 0.5);
}
.responsive-table-plugin .btn-group.pull-right {
  float: right;
}
.responsive-table-plugin .btn-group.pull-right .dropdown-menu {
  left: auto;
  right: 0;
}

.no-touch .dropdown-menu > .checkbox-row:hover, .no-touch .dropdown-menu > .checkbox-row:active {
  color: var(--ct-dropdown-link-active-color);
  background-color: var(--ct-dropdown-link-active-bg);
}

@font-face {
  font-family: "footable";
  src: url("../fonts/footable.eot");
  src: url("../fonts/footable.eot?#iefix") format("embedded-opentype"), url("../fonts/footable.woff") format("woff"), url("../fonts/footable.ttf") format("truetype"), url("../fonts/footable.svg#footable") format("svg");
  font-weight: normal;
  font-style: normal;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  @font-face {
    font-family: "footable";
    src: url("../fonts/footable.svg#footable") format("svg");
    font-weight: normal;
    font-style: normal;
  }
}
.footable-odd {
  background-color: var(--ct-table-bg);
}

.footable-row-detail,
.footable-detail-show {
  background-color: var(--ct-table-striped-bg);
}

.footable-pagination li {
  margin-left: 5px;
  float: left;
}
.footable-pagination li a {
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.5;
  color: var(--ct-pagination-color);
  background-color: var(--ct-pagination-bg);
  display: block;
  border: 1px solid var(--ct-pagination-border-color);
  border-radius: 0.25rem;
}
.footable-pagination li a:hover {
  z-index: 2;
  color: var(--ct-pagination-hover-color);
  text-decoration: none;
  background-color: var(--ct-pagination-hover-bg);
  border-color: var(--ct-pagination-hover-border-color);
}
.footable-pagination li.active a {
  color: #fff;
  background-color: #6658dd;
  border-color: #6658dd;
}

.footable > thead > tr > th > span.footable-sort-indicator {
  float: right;
}

.footable {
  width: 100%;
}

.footable.breakpoint > tbody > tr.footable-detail-show > td {
  border-bottom: none;
}

.footable.breakpoint > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e001";
}

.footable.breakpoint > tbody > tr:hover:not(.footable-row-detail) {
  cursor: pointer;
}

.footable.breakpoint > tbody > tr > td.footable-cell-detail {
  border-top: none;
}

.footable.breakpoint > tbody > tr > td > span.footable-toggle {
  display: inline-block;
  font-family: "footable";
  padding-right: 5px;
  font-size: 14px;
}

.footable.breakpoint > tbody > tr > td > span.footable-toggle:before {
  content: "\e000";
}

.footable.breakpoint.toggle-circle > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e005";
}

.footable.breakpoint.toggle-circle > tbody > tr > td > span.footable-toggle:before {
  content: "\e004";
}

.footable.breakpoint.toggle-circle-filled > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e003";
}

.footable.breakpoint.toggle-circle-filled > tbody > tr > td > span.footable-toggle:before {
  content: "\e002";
}

.footable.breakpoint.toggle-square > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e007";
}

.footable.breakpoint.toggle-square > tbody > tr > td > span.footable-toggle:before {
  content: "\e006";
}

.footable.breakpoint.toggle-square-filled > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e009";
}

.footable.breakpoint.toggle-square-filled > tbody > tr > td > span.footable-toggle:before {
  content: "\e008";
}

.footable.breakpoint.toggle-arrow > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e00f";
}

.footable.breakpoint.toggle-arrow > tbody > tr > td > span.footable-toggle:before {
  content: "\e011";
}

.footable.breakpoint.toggle-arrow-small > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e013";
}

.footable.breakpoint.toggle-arrow-small > tbody > tr > td > span.footable-toggle:before {
  content: "\e015";
}

.footable.breakpoint.toggle-arrow-circle > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e01b";
}

.footable.breakpoint.toggle-arrow-circle > tbody > tr > td > span.footable-toggle:before {
  content: "\e01d";
}

.footable.breakpoint.toggle-arrow-circle-filled > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e00b";
}

.footable.breakpoint.toggle-arrow-circle-filled > tbody > tr > td > span.footable-toggle:before {
  content: "\e00d";
}

.footable.breakpoint.toggle-arrow-tiny > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e01f";
}

.footable.breakpoint.toggle-arrow-tiny > tbody > tr > td > span.footable-toggle:before {
  content: "\e021";
}

.footable.breakpoint.toggle-arrow-alt > tbody > tr.footable-detail-show > td > span.footable-toggle:before {
  content: "\e017";
}

.footable.breakpoint.toggle-arrow-alt > tbody > tr > td > span.footable-toggle:before {
  content: "\e019";
}

.footable.breakpoint.toggle-medium > tbody > tr > td > span.footable-toggle {
  font-size: 18px;
}

.footable.breakpoint.toggle-large > tbody > tr > td > span.footable-toggle {
  font-size: 24px;
}

.footable > thead > tr > th {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: -moz-none;
  -ms-user-select: none;
  user-select: none;
}

.footable > thead > tr > th.footable-sortable:hover {
  cursor: pointer;
}

.footable > thead > tr > th.footable-sorted > span.footable-sort-indicator:before {
  content: "\e012";
}

.footable > thead > tr > th.footable-sorted-desc > span.footable-sort-indicator:before {
  content: "\e013";
}

.footable > thead > tr > th > span.footable-sort-indicator {
  display: inline-block;
  font-family: "footable";
  padding-left: 5px;
  opacity: 0.3;
}

.footable > thead > tr > th > span.footable-sort-indicator:before {
  content: "\e022";
}

.footable > tfoot .pagination {
  margin: 0;
}

.footable.no-paging .hide-if-no-paging {
  display: none;
}

.footable-row-detail-inner {
  display: table;
}

.footable-row-detail-row {
  display: table-row;
  line-height: 1.5em;
}

.footable-row-detail-group {
  display: block;
  line-height: 2em;
  font-size: 1.2em;
  font-weight: 700;
}

.footable-row-detail-name {
  display: table-cell;
  font-weight: 700;
  padding-right: 0.5em;
}

.footable-row-detail-value {
  display: table-cell;
}

.bootstrap-table .table:not(.table-sm) > tbody > tr > td,
.bootstrap-table .table:not(.table-sm) > tbody > tr > th,
.bootstrap-table .table:not(.table-sm) > tfoot > tr > td,
.bootstrap-table .table:not(.table-sm) > tfoot > tr > th,
.bootstrap-table .table:not(.table-sm) > thead > tr > td {
  padding: 0.85rem;
}
.bootstrap-table .table {
  border-bottom: none;
}
.bootstrap-table .table > thead > tr > th {
  border-bottom: none;
}
.bootstrap-table table.table-borderless tr {
  border: none !important;
}
.bootstrap-table table.table-borderless tr th, .bootstrap-table table.table-borderless tr td {
  border: none !important;
}
.bootstrap-table table.table-bordered tr {
  border-color: var(--ct-table-border-color) !important;
}

.table-borderless.table-bordered {
  border: none !important;
}

table[data-toggle=table] {
  display: none;
}

.fixed-table-pagination .pagination-detail,
.fixed-table-pagination div.pagination {
  margin-top: 20px;
  margin-bottom: 0;
}
.fixed-table-pagination .pagination .page-link {
  border-radius: 30px !important;
  margin: 0 3px;
  border: none;
}

.fixed-table-container {
  border: none;
}
.fixed-table-container thead th .th-inner {
  padding: 0.85rem;
}

.fixed-table-toolbar .fa {
  font-family: "Font Awesome 5 Free";
  font-weight: 400;
}
.fixed-table-toolbar .fa.fa-sync {
  font-weight: 900;
}
.fixed-table-toolbar .fa-toggle-down:before {
  content: "\f150";
}
.fixed-table-toolbar .fa-toggle-up:before {
  content: "\f151";
}
.fixed-table-toolbar .fa-refresh:before {
  content: "\f01e";
  font-weight: 900;
}
.fixed-table-toolbar .fa-th-list:before {
  content: "\f0ca";
  font-weight: 900;
}

.tablesaw thead {
  background: var(--ct-gray-100);
  background-image: none;
  border: none;
}
.tablesaw thead th {
  text-shadow: none;
}
.tablesaw thead tr:first-child th {
  border: none;
  font-weight: 500;
  font-family: "Roboto", sans-serif;
}
.tablesaw td {
  border-top: 1px solid var(--ct-gray-100) !important;
  border-bottom-width: 0px;
}

.tablesaw td,
.tablesaw tbody th {
  font-size: inherit;
  line-height: inherit;
  padding: 10px !important;
}

.tablesaw-stack tbody tr,
.tablesaw tbody tr {
  border-bottom: none;
}

.tablesaw-bar .btn-select.btn-small:after,
.tablesaw-bar .btn-select.btn-micro:after {
  font-size: 8px;
  padding-right: 10px;
}

.tablesaw-swipe .tablesaw-cell-persist {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: var(--ct-gray-100);
}

.tablesaw-enhanced .tablesaw-bar .btn {
  text-shadow: none;
  background-image: none;
  text-transform: none;
  border: 1px solid var(--ct-gray-300);
  padding: 3px 10px;
  color: var(--ct-gray-900);
}
.tablesaw-enhanced .tablesaw-bar .btn:after {
  display: none;
}

.tablesaw-enhanced .tablesaw-bar .btn.btn-select:hover {
  background: #fff;
}

.tablesaw-enhanced .tablesaw-bar .btn:hover,
.tablesaw-enhanced .tablesaw-bar .btn:focus,
.tablesaw-enhanced .tablesaw-bar .btn:active {
  color: #6658dd !important;
  background-color: var(--ct-table-hover-bg);
  outline: none !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background-image: none;
}

.tablesaw-columntoggle-popup .btn-group {
  display: block;
}

.tablesaw-swipe .tablesaw-swipe-cellpersist {
  border-right: 2px solid var(--ct-gray-100);
}

.tablesaw-sortable-btn {
  cursor: pointer;
}

.tablesaw-swipe-cellpersist {
  width: auto !important;
}

.tablesaw-bar-section label {
  color: inherit;
  margin-bottom: 0.5rem;
}

.jsgrid-cell {
  padding: 0.85rem;
  border: 1px solid var(--ct-table-border-color);
}

.jsgrid-grid-header,
.jsgrid-grid-body,
.jsgrid-header-row > .jsgrid-header-cell,
.jsgrid-filter-row > .jsgrid-cell,
.jsgrid-insert-row > .jsgrid-cell,
.jsgrid-edit-row > .jsgrid-cell {
  border: none;
}

.jsgrid-row > .jsgrid-cell {
  background: transparent !important;
}

.jsgrid-alt-row > .jsgrid-cell {
  background: var(--ct-table-striped-bg);
}

.jsgrid-selected-row > .jsgrid-cell {
  background: var(--ct-table-hover-bg);
  border-color: var(--ct-table-border-color);
}

.jsgrid-header-row > .jsgrid-header-cell {
  background: var(--ct-gray-100);
  text-align: center !important;
}

.jsgrid-filter-row > .jsgrid-cell {
  background: var(--ct-gray-200);
}

.jsgrid-edit-row > .jsgrid-cell,
.jsgrid-insert-row > .jsgrid-cell {
  background: var(--ct-gray-100);
}

.jsgrid input,
.jsgrid select,
.jsgrid textarea {
  padding: 0.4em 0.6em;
  outline: none !important;
  color: var(--ct-input-color);
  background-color: var(--ct-input-bg);
  border: 1px solid var(--ct-input-border-color);
  border-radius: 0.2rem;
}

.jsgrid-pager-container {
  margin-top: 10px;
}

.jsgrid-pager-page {
  padding: 0;
  margin: 0 2px;
}
.jsgrid-pager-page.jsgrid-pager-current-page {
  background-color: #6658dd;
  color: #fff;
}

.jsgrid-pager-page a,
.jsgrid-pager-current-page {
  background-color: var(--ct-gray-100);
  border-radius: 50%;
  height: 24px;
  width: 24px;
  display: inline-block;
  text-align: center;
  line-height: 24px;
  color: var(--ct-gray-700);
}

.jsgrid-pager-nav-button a {
  color: var(--ct-gray-700);
  font-weight: 600;
}
.jsgrid-pager-nav-button a:hover {
  color: #6658dd;
}

.jsgrid .jsgrid-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-image: url("../images/jsgrid.png");
  background-color: var(--ct-gray-200);
  outline: none !important;
}
.jsgrid .jsgrid-button:hover {
  opacity: 0.9;
  background-color: var(--ct-gray-100);
}

.jsgrid-search-mode-button {
  background-position: 0 -295px;
}

.jsgrid-insert-button {
  background-position: 0 -160px;
}

.jsgrid-header-sort:before {
  position: absolute;
}

.ms-container {
  background: transparent url("../images/multiple-arrow.png") no-repeat 50% 50%;
  width: auto;
  max-width: 370px;
}
.ms-container .ms-list {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid var(--ct-input-border-color);
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
}
.ms-container .ms-list.ms-focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid var(--ct-input-focus-border-color);
}
.ms-container .ms-selectable li.ms-elem-selectable {
  border: none;
  padding: 5px 10px;
  color: var(--ct-dropdown-link-color);
}
.ms-container .ms-selectable li.ms-hover {
  background-color: #6658dd;
  color: #fff;
}
.ms-container .ms-selection li.ms-elem-selection {
  border: none;
  padding: 5px 10px;
  color: var(--ct-dropdown-link-color);
}
.ms-container .ms-selection li.ms-hover {
  background-color: #6658dd;
  color: #fff;
}

.ms-selectable {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none !important;
}

.ms-optgroup-label {
  font-weight: 500;
  font-family: "Roboto", sans-serif;
  color: var(--ct-gray-900) !important;
  font-size: 13px;
}

.ms-container .ms-selectable, .ms-container .ms-selection {
  background-color: var(--ct-input-bg);
}

.autocomplete-suggestions {
  border: 1px solid var(--ct-dropdown-border-color);
  background-color: var(--ct-dropdown-bg);
  cursor: default;
  overflow: auto;
  max-height: 200px !important;
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
}

.autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden;
}

.autocomplete-no-suggestion {
  padding: 5px;
}

.autocomplete-selected {
  background: var(--ct-gray-200);
  cursor: pointer;
}

.autocomplete-suggestions strong {
  font-weight: bold;
  color: var(--ct-gray-900);
}

.autocomplete-group {
  padding: 5px;
  font-weight: 500;
  font-family: "Roboto", sans-serif;
}

.autocomplete-group strong {
  font-weight: bold;
  font-size: 16px;
  color: var(--ct-gray-900);
  display: block;
}

.bootstrap-touchspin .btn .input-group-text {
  padding: 0;
  border: none;
  background-color: transparent;
  color: inherit;
}

.parsley-errors-list {
  margin: 0;
  padding: 0;
}
.parsley-errors-list > li {
  list-style: none;
  color: #f1556c;
  margin-top: 5px;
  padding-left: 20px;
  position: relative;
}
.parsley-errors-list > li:before {
  content: "\f0159";
  font-family: "Material Design Icons";
  position: absolute;
  left: 2px;
  top: -1px;
}

.parsley-error {
  border-color: #f1556c;
}

.parsley-success {
  border-color: #1abc9c;
}

.flatpickr-calendar {
  background: var(--ct-dropdown-bg);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  border: 1px solid var(--ct-dropdown-border-color);
}
.flatpickr-calendar.arrowTop:before {
  border-bottom-color: var(--ct-dropdown-bg);
}
.flatpickr-calendar.arrowTop:after {
  border-bottom-color: var(--ct-dropdown-bg);
}
.flatpickr-calendar.arrowBottom:before, .flatpickr-calendar.arrowBottom:after {
  border-top-color: var(--ct-dropdown-bg);
}

.flatpickr-current-month {
  font-size: 110%;
}

.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay {
  background: #6658dd;
  border-color: #6658dd;
}
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  -webkit-box-shadow: -10px 0 0 #6658dd;
          box-shadow: -10px 0 0 #6658dd;
}

.flatpickr-time input:hover,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time .flatpickr-am-pm:focus {
  background: var(--ct-input-bg);
  color: var(--ct-dropdown-link-active-color);
}

.flatpickr-months .flatpickr-month {
  height: 36px;
}
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-month {
  color: var(--ct-gray-500);
  fill: var(--ct-gray-500);
}

.flatpickr-weekdays {
  background-color: var(--ct-dropdown-link-active-bg);
}

span.flatpickr-weekday,
.flatpickr-day,
.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover,
.flatpickr-time input,
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  color: var(--ct-dropdown-link-color);
  fill: var(--ct-dropdown-link-color);
}

.flatpickr-day.disabled, .flatpickr-day.disabled:hover, .flatpickr-day.prevMonthDay, .flatpickr-day.nextMonthDay, .flatpickr-day.notAllowed, .flatpickr-day.notAllowed.prevMonthDay, .flatpickr-day.notAllowed.nextMonthDay {
  color: var(--ct-dropdown-link-disabled-color);
}
.flatpickr-day.inRange, .flatpickr-day.prevMonthDay.inRange, .flatpickr-day.nextMonthDay.inRange, .flatpickr-day.today.inRange, .flatpickr-day.prevMonthDay.today.inRange, .flatpickr-day.nextMonthDay.today.inRange, .flatpickr-day:hover, .flatpickr-day.prevMonthDay:hover, .flatpickr-day.nextMonthDay:hover, .flatpickr-day:focus, .flatpickr-day.prevMonthDay:focus, .flatpickr-day.nextMonthDay:focus {
  background: var(--ct-dropdown-link-hover-bg);
  border-color: var(--ct-dropdown-link-hover-bg);
}

.flatpickr-calendar.showTimeInput.hasTime .flatpickr-time {
  border-top: 1px solid var(--ct-dropdown-bg);
}

.numInputWrapper:hover,
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background-color: transparent;
  color: var(--ct-dropdown-link-active-color);
}

.flatpickr-day.inRange {
  -webkit-box-shadow: -5px 0 0 var(--ct-dropdown-link-hover-bg), 5px 0 0 var(--ct-dropdown-link-hover-bg);
          box-shadow: -5px 0 0 var(--ct-dropdown-link-hover-bg), 5px 0 0 var(--ct-dropdown-link-hover-bg);
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
  color: var(--ct-dropdown-link-disabled-color);
}

.sp-container {
  background-color: var(--ct-dropdown-bg);
  z-index: 9;
}
.sp-container button {
  padding: 0.25rem 0.5rem;
  font-size: 0.71094rem;
  border-radius: 0.2rem;
  font-weight: 400;
  color: var(--ct-gray-900);
}
.sp-container button.sp-palette-toggle {
  background-color: var(--ct-gray-100);
}
.sp-container button.sp-choose {
  background-color: #1abc9c;
  margin-left: 5px;
  margin-right: 0;
}

.sp-palette-container {
  border-right: 1px solid var(--ct-gray-300);
}

.sp-input {
  background-color: var(--ct-input-bg);
  border-color: var(--ct-input-border-color) !important;
  color: var(--ct-input-color);
}
.sp-input:focus {
  outline: none;
}

.clockpicker-popover .btn-default {
  background-color: #6658dd;
  color: #fff;
}

.clockpicker-popover {
  background: var(--ct-dropdown-bg);
  -webkit-box-shadow: var(--ct-box-shadow);
          box-shadow: var(--ct-box-shadow);
  border: 1px solid var(--ct-dropdown-border-color);
}
.clockpicker-popover .popover-title {
  background-color: transparent;
  font-weight: 500;
}
.clockpicker-popover .clockpicker-plate {
  background: var(--ct-dropdown-link-hover-bg);
  border: 1px solid var(--ct-dropdown-border-color);
}
.clockpicker-popover .popover-content {
  background-color: transparent;
}

.clockpicker-tick {
  color: var(--ct-dropdown-link-color);
}
.clockpicker-tick:hover {
  background-color: rgba(102, 88, 221, 0.35);
}

.clockpicker-canvas line {
  stroke: #6658dd;
}

.clockpicker-canvas-bg {
  fill: rgba(102, 88, 221, 0.35);
}

.clockpicker-canvas-bearing,
.clockpicker-canvas-fg {
  fill: #6658dd;
}

.clockpicker-button {
  display: block;
  width: 100%;
}

[dir=rtl] .sp-alpha {
  direction: ltr;
}
[dir=rtl] .sp-original-input-container .sp-add-on {
  border: none;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}
[dir=rtl] input.spectrum.with-add-on {
  border: 1px solid var(--ct-input-border-color);
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.sp-original-input-container .sp-add-on {
  border: none !important;
}

.ql-container {
  font-family: "Roboto", sans-serif;
  border-bottom-left-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}
.ql-container.ql-snow {
  border-color: var(--ct-input-border-color);
}

.ql-bubble {
  border: 1px solid var(--ct-input-border-color);
  border-radius: 0.2rem;
}

.ql-toolbar {
  font-family: "Roboto", sans-serif !important;
  border-top-left-radius: 0.2rem;
  border-top-right-radius: 0.2rem;
}
.ql-toolbar span {
  outline: none !important;
  color: var(--ct-dropdown-link-color);
}
.ql-toolbar span:hover {
  color: #6658dd !important;
}
.ql-toolbar.ql-snow {
  border-color: var(--ct-input-border-color);
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: transparent;
}
.ql-toolbar.ql-snow .ql-formats {
  margin-right: 15px;
  margin-left: 0;
}

.ql-snow .ql-stroke,
.ql-snow .ql-script,
.ql-snow .ql-strike svg {
  stroke: var(--ct-dropdown-link-color);
}
.ql-snow .ql-fill {
  fill: var(--ct-dropdown-link-color);
}

.ql-snow .ql-picker-options {
  background-color: var(--ct-dropdown-bg);
  border-color: var(--ct-dropdown-border-color) !important;
}

.ql-editor {
  text-align: left;
}

.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  position: absolute;
  margin-top: -9px;
  right: 0;
  left: auto;
  top: 50%;
  width: 18px;
}

.ql-editor ol li:not(.ql-direction-rtl), .ql-editor ul li:not(.ql-direction-rtl) {
  padding-left: 1.5em;
  padding-right: 0;
}
.ql-editor li:not(.ql-direction-rtl):before {
  margin-left: -1.5em;
  margin-right: 0.3em;
  text-align: right;
}

.ql-editor ol, .ql-editor ul {
  padding-left: 1.5em;
  padding-right: 0;
}

.dropzone {
  border: 2px dashed var(--ct-input-border-color);
  background: var(--ct-input-bg);
  border-radius: 6px;
  cursor: pointer;
  min-height: 150px;
  padding: 20px;
  -webkit-box-shadow: var(--ct-components-shadow-sm);
          box-shadow: var(--ct-components-shadow-sm);
}
.dropzone .dz-message {
  text-align: center;
  margin: 2rem 0;
}
.dropzone.dz-started .dz-message {
  display: none;
}

@font-face {
  font-family: "dropify";
  src: url("../fonts/dropify.eot");
  src: url("../fonts/dropify.eot#iefix") format("embedded-opentype"), url("../fonts/dropify.woff") format("woff"), url("../fonts/dropify.ttf") format("truetype"), url("../fonts/dropify.svg#dropify") format("svg");
  font-weight: normal;
  font-style: normal;
}
.dropify-wrapper {
  border: 2px dashed var(--ct-input-border-color);
  background: var(--ct-input-bg);
  border-radius: 6px;
  color: var(--ct-input-color);
}
.dropify-wrapper:hover {
  background-image: linear-gradient(-45deg, var(--ct-card-cap-bg) 25%, transparent 25%, transparent 50%, var(--ct-card-cap-bg) 50%, var(--ct-card-cap-bg) 75%, transparent 75%, transparent);
}
.dropify-wrapper .dropify-preview {
  background-color: var(--ct-gray-200);
}

.file-icon p {
  font-size: 20px;
}

.editable-clear-x {
  background: url("../images/clear.png") center center no-repeat;
}

.editableform-loading {
  background: url("../images/loading.gif") center center no-repeat;
}

.editable-checklist label {
  display: block;
}

.editable-input .form-control, .editable-input .form-select {
  display: inline-block;
}

.editable-buttons {
  margin-left: 7px;
}
.editable-buttons .editable-cancel {
  margin-left: 7px;
}

.editable-checklist > div {
  margin-bottom: 7px;
}

.image-crop-preview .img-preview {
  float: left;
  margin-bottom: 0.5rem;
  margin-right: 0.5rem;
  overflow: hidden;
  background-color: var(--ct-gray-100);
  text-align: center;
  width: 100%;
}
.image-crop-preview .img-preview > img {
  max-width: 100%;
}
.image-crop-preview .preview-lg {
  height: 9rem;
  width: 16rem;
}
.image-crop-preview .preview-md {
  height: 4.5rem;
  width: 8rem;
}
.image-crop-preview .preview-sm {
  height: 2.25rem;
  width: 4rem;
}
.image-crop-preview .preview-xs {
  height: 1.125rem;
  margin-right: 0;
  width: 2rem;
}

.img-crop-preview-btns > .btn,
.img-crop-preview-btns > .btn-group {
  margin-bottom: 8px;
  margin-right: 8px;
}

.docs-cropped .modal-body > img,
.docs-cropped .modal-body > canvas {
  max-width: 100%;
}

.docs-drop-options {
  max-height: 400px;
  overflow-y: auto;
}

.gmaps,
.gmaps-panaroma {
  height: 300px;
  background: var(--ct-gray-100);
  border-radius: 3px;
}

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #6658dd;
  border-radius: 4px;
  padding: 10px 20px;
}

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute;
}
.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #6658dd;
}
.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #6658dd;
}

.jvectormap-label {
  border: none;
  background: var(--ct-gray-900);
  color: var(--ct-gray-100);
  font-family: "Roboto", sans-serif;
  font-size: 0.875rem;
  padding: 5px 8px;
}

.mapael .map {
  position: relative;
}
.mapael .map .zoomIn {
  top: 25px;
}
.mapael .map .zoomOut {
  top: 50px;
}
.mapael .mapTooltip {
  position: absolute;
  background-color: #6658dd;
  opacity: 0.95;
  border-radius: 3px;
  padding: 2px 10px;
  z-index: 1000;
  max-width: 200px;
  display: none;
  color: #fff;
  font-family: "Roboto", sans-serif;
}
.mapael .zoomIn,
.mapael .zoomOut,
.mapael .zoomReset {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  border-radius: 2px;
  font-weight: 500;
  cursor: pointer;
  background-color: #6658dd;
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  position: absolute;
  top: 0;
  left: 10px;
  width: 24px;
  height: 24px;
  line-height: 24px;
}
.mapael .plotLegend text {
  font-family: "Roboto", sans-serif !important;
  fill: var(--ct-text-muted);
}

.datepicker {
  padding: 10px !important;
  -webkit-animation: none;
          animation: none;
}
.datepicker td,
.datepicker th {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.datepicker table tr td.active.active, .datepicker table tr td.active.disabled, .datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled.disabled, .datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.active.disabled:hover.active, .datepicker table tr td.active.disabled:hover.disabled, .datepicker table tr td.active.disabled:hover:active, .datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td .active.disabled:hover[disabled],
.datepicker table tr td .active.disabled[disabled],
.datepicker table tr td .active:active,
.datepicker table tr td .active:hover,
.datepicker table tr td .active:hover.active,
.datepicker table tr td .active:hover.disabled,
.datepicker table tr td .active:hover:active,
.datepicker table tr td .active:hover:hover,
.datepicker table tr td .active:hover[disabled],
.datepicker table tr td .active[disabled],
.datepicker table tr td span.active.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active.disabled:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active[disabled], .datepicker table tr td.today, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover {
  background-color: #6658dd !important;
  background-image: none !important;
  color: #fff;
}
.datepicker table tr td.day.focused, .datepicker table tr td.day:hover,
.datepicker table tr td span.focused,
.datepicker table tr td span:hover {
  background: var(--ct-gray-200);
}
.datepicker table tr td.new, .datepicker table tr td.old,
.datepicker table tr td span.new,
.datepicker table tr td span.old {
  color: var(--ct-gray-700);
  opacity: 0.4;
}
.datepicker .datepicker-switch:hover,
.datepicker .next:hover,
.datepicker .prev:hover,
.datepicker tfoot tr th:hover {
  background: var(--ct-gray-200);
}
.datepicker .datepicker-switch:hover {
  background: none;
}

.datepicker-dropdown:after {
  border-bottom: 6px solid var(--ct-dropdown-bg);
}
.datepicker-dropdown:before {
  border-bottom-color: var(--ct-dropdown-border-color);
}
.datepicker-dropdown.datepicker-orient-top:before {
  border-top: 7px solid var(--ct-dropdown-border-color);
}
.datepicker-dropdown.datepicker-orient-top:after {
  border-top: 6px solid var(--ct-dropdown-bg);
}

.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
}

.gu-hide {
  display: none !important;
}

.gu-unselectable {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
}

.gu-transit {
  opacity: 0.2;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
  filter: alpha(opacity=20);
}

.dragula-handle {
  position: relative;
  width: 36px;
  height: 36px;
  font-size: 24px;
  text-align: center;
  cursor: move;
}
.dragula-handle:before {
  content: "\f01db";
  font-family: "Material Design Icons";
  position: absolute;
}