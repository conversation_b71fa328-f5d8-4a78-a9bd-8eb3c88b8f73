<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.sponsored-ads.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to List
                            </a>
                            <a href="<?php echo e(route('admin.sponsored-ads.show', $sponsoredAd->id)); ?>" class="btn btn-info">
                                <i class="mdi mdi-eye"></i> View Details
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Edit Sponsored Ad</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title d-flex align-items-center">
                            <div><i class="mdi mdi-pencil me-1 font-22 text-warning"></i></div>
                            <h5 class="mb-0 text-warning">Edit Sponsored Ad: <?php echo e($sponsoredAd->title); ?></h5>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <form method="POST" action="<?php echo e(route('admin.sponsored-ads.update', $sponsoredAd->id)); ?>" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            
                            <div class="row">
                                <!-- Content Information -->
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Sponsored Ad Title <span class="text-danger">*</span></label>
                                        <input type="text" name="title" class="form-control" id="title" value="<?php echo e(old('title', $sponsoredAd->title)); ?>" required>
                                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                        <textarea name="content" class="form-control" id="content" rows="6" required><?php echo e(old('content', $sponsoredAd->content)); ?></textarea>
                                        <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="text-muted">Main content for the sponsored ad</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="sponsor_name" class="form-label">Sponsor Name <span class="text-danger">*</span></label>
                                        <input type="text" name="sponsor_name" class="form-control" id="sponsor_name" value="<?php echo e(old('sponsor_name', $sponsoredAd->sponsor_name)); ?>" required>
                                        <?php $__errorArgs = ['sponsor_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="link_url" class="form-label">Link URL</label>
                                        <input type="url" name="link_url" class="form-control" id="link_url" value="<?php echo e(old('link_url', $sponsoredAd->link_url)); ?>" placeholder="https://example.com">
                                        <?php $__errorArgs = ['link_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="image" class="form-label">Ad Image</label>
                                                <?php if($sponsoredAd->image): ?>
                                                    <div class="mb-2">
                                                        <img src="<?php echo e(asset($sponsoredAd->image)); ?>" alt="Current Image" class="img-thumbnail" width="200">
                                                        <p class="text-muted">Current image</p>
                                                    </div>
                                                <?php endif; ?>
                                                <input type="file" name="image" class="form-control" id="image" accept="image/*">
                                                <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="text-danger"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <small class="text-muted">Leave empty to keep current image. Max size: 2MB</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="sponsor_logo" class="form-label">Sponsor Logo</label>
                                                <?php if($sponsoredAd->sponsor_logo): ?>
                                                    <div class="mb-2">
                                                        <img src="<?php echo e(asset($sponsoredAd->sponsor_logo)); ?>" alt="Current Logo" class="img-thumbnail" width="100">
                                                        <p class="text-muted">Current logo</p>
                                                    </div>
                                                <?php endif; ?>
                                                <input type="file" name="sponsor_logo" class="form-control" id="sponsor_logo" accept="image/*">
                                                <?php $__errorArgs = ['sponsor_logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <div class="text-danger"><?php echo e($message); ?></div>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <small class="text-muted">Leave empty to keep current logo. Max size: 1MB</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Settings -->
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ad_format" class="form-label">Ad Format <span class="text-danger">*</span></label>
                                        <select name="ad_format" class="form-select" id="ad_format" required>
                                            <option value="">Select Format</option>
                                            <option value="native" <?php echo e(old('ad_format', $sponsoredAd->ad_format) == 'native' ? 'selected' : ''); ?>>Native</option>
                                            <option value="display" <?php echo e(old('ad_format', $sponsoredAd->ad_format) == 'display' ? 'selected' : ''); ?>>Display</option>
                                            <option value="video" <?php echo e(old('ad_format', $sponsoredAd->ad_format) == 'video' ? 'selected' : ''); ?>>Video</option>
                                            <option value="carousel" <?php echo e(old('ad_format', $sponsoredAd->ad_format) == 'carousel' ? 'selected' : ''); ?>>Carousel</option>
                                        </select>
                                        <?php $__errorArgs = ['ad_format'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="placement" class="form-label">Placement <span class="text-danger">*</span></label>
                                        <select name="placement" class="form-select" id="placement" required>
                                            <option value="">Select Placement</option>
                                            <option value="homepage_top" <?php echo e(old('placement', $sponsoredAd->placement) == 'homepage_top' ? 'selected' : ''); ?>>Homepage Top</option>
                                            <option value="homepage_middle" <?php echo e(old('placement', $sponsoredAd->placement) == 'homepage_middle' ? 'selected' : ''); ?>>Homepage Middle</option>
                                            <option value="homepage_bottom" <?php echo e(old('placement', $sponsoredAd->placement) == 'homepage_bottom' ? 'selected' : ''); ?>>Homepage Bottom</option>
                                            <option value="category_top" <?php echo e(old('placement', $sponsoredAd->placement) == 'category_top' ? 'selected' : ''); ?>>Category Top</option>
                                            <option value="article_top" <?php echo e(old('placement', $sponsoredAd->placement) == 'article_top' ? 'selected' : ''); ?>>Article Top</option>
                                            <option value="article_middle" <?php echo e(old('placement', $sponsoredAd->placement) == 'article_middle' ? 'selected' : ''); ?>>Article Middle</option>
                                            <option value="article_bottom" <?php echo e(old('placement', $sponsoredAd->placement) == 'article_bottom' ? 'selected' : ''); ?>>Article Bottom</option>
                                            <option value="sidebar" <?php echo e(old('placement', $sponsoredAd->placement) == 'sidebar' ? 'selected' : ''); ?>>Sidebar</option>
                                        </select>
                                        <?php $__errorArgs = ['placement'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="display_order" class="form-label">Display Order</label>
                                        <input type="number" name="display_order" class="form-control" id="display_order" value="<?php echo e(old('display_order', $sponsoredAd->display_order)); ?>" min="0">
                                        <?php $__errorArgs = ['display_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="budget" class="form-label">Budget ($)</label>
                                        <input type="number" name="budget" class="form-control" id="budget" value="<?php echo e(old('budget', $sponsoredAd->budget)); ?>" min="0" step="0.01">
                                        <?php $__errorArgs = ['budget'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="text-muted">Optional budget tracking</small>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', $sponsoredAd->is_active) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_active">
                                                Active Sponsored Ad
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="is_premium" id="is_premium" value="1" <?php echo e(old('is_premium', $sponsoredAd->is_premium) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="is_premium">
                                                Premium Placement
                                            </label>
                                        </div>
                                        <small class="text-muted">Premium ads get priority positioning</small>
                                    </div>

                                    <!-- Performance Stats -->
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Performance Stats</h6>
                                            <div class="d-flex justify-content-between">
                                                <span>Views:</span>
                                                <strong><?php echo e(number_format($sponsoredAd->view_count)); ?></strong>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>Clicks:</span>
                                                <strong><?php echo e(number_format($sponsoredAd->click_count)); ?></strong>
                                            </div>
                                            <?php if($sponsoredAd->view_count > 0): ?>
                                            <div class="d-flex justify-content-between">
                                                <span>CTR:</span>
                                                <strong><?php echo e($sponsoredAd->click_through_rate); ?>%</strong>
                                            </div>
                                            <?php endif; ?>
                                            <?php if($sponsoredAd->budget): ?>
                                            <div class="d-flex justify-content-between">
                                                <span>Budget:</span>
                                                <strong>$<?php echo e(number_format($sponsoredAd->budget, 2)); ?></strong>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Scheduling -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Start Date</label>
                                        <input type="date" name="start_date" class="form-control" id="start_date" value="<?php echo e(old('start_date', $sponsoredAd->start_date?->format('Y-m-d'))); ?>">
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">End Date</label>
                                        <input type="date" name="end_date" class="form-control" id="end_date" value="<?php echo e(old('end_date', $sponsoredAd->end_date?->format('Y-m-d'))); ?>">
                                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Targeting -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="target_demographics" class="form-label">Target Demographics</label>
                                        <select name="target_demographics[]" class="form-select" id="target_demographics" multiple>
                                            <option value="18-24" <?php echo e(in_array('18-24', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Age 18-24</option>
                                            <option value="25-34" <?php echo e(in_array('25-34', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Age 25-34</option>
                                            <option value="35-44" <?php echo e(in_array('35-44', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Age 35-44</option>
                                            <option value="45-54" <?php echo e(in_array('45-54', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Age 45-54</option>
                                            <option value="55+" <?php echo e(in_array('55+', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Age 55+</option>
                                            <option value="male" <?php echo e(in_array('male', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Male</option>
                                            <option value="female" <?php echo e(in_array('female', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Female</option>
                                            <option value="urban" <?php echo e(in_array('urban', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Urban</option>
                                            <option value="rural" <?php echo e(in_array('rural', old('target_demographics', $sponsoredAd->target_demographics ?? [])) ? 'selected' : ''); ?>>Rural</option>
                                        </select>
                                        <small class="text-muted">Hold Ctrl to select multiple demographics</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="target_categories" class="form-label">Target Categories</label>
                                        <select name="target_categories[]" class="form-select" id="target_categories" multiple>
                                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($category->id); ?>" <?php echo e(in_array($category->id, old('target_categories', $sponsoredAd->target_categories ?? [])) ? 'selected' : ''); ?>><?php echo e($category->category_name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <small class="text-muted">Hold Ctrl to select multiple categories</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-warning px-5">
                                    <i class="mdi mdi-content-save"></i> Update Sponsored Ad
                                </button>
                                <button type="reset" class="btn btn-secondary px-5">
                                    <i class="mdi mdi-refresh"></i> Reset Changes
                                </button>
                                <a href="<?php echo e(route('admin.sponsored-ads.index')); ?>" class="btn btn-outline-secondary px-5">
                                    <i class="mdi mdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/sponsored_ads/edit.blade.php ENDPATH**/ ?>