<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.advertisements.create')); ?>" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Add Advertisement
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Advertisement Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-advertisement text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['total']); ?></h5>
                                <p class="text-muted mb-0">Total Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['active']); ?></h5>
                                <p class="text-muted mb-0">Active Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-image text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['banner']); ?></h5>
                                <p class="text-muted mb-0">Banner Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-view-column text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0"><?php echo e($stats['sidebar']); ?></h5>
                                <p class="text-muted mb-0">Sidebar Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Advertisements</h4>
                        <p class="card-title-desc">Drag and drop to reorder advertisements, or use the action buttons to manage ads.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchAds" placeholder="Search advertisements...">
                                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="filterType">
                                    <option value="">All Types</option>
                                    <option value="banner">Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="adsTable">
                                <thead>
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Advertisement</th>
                                        <th>Type</th>
                                        <th>Position</th>
                                        <th>Status</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-ads">
                                    <?php $__currentLoopData = $advertisements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr data-ad-id="<?php echo e($ad->id); ?>" data-order="<?php echo e($ad->display_order); ?>" 
                                        class="ad-row <?php echo e($ad->is_active ? '' : 'table-secondary'); ?>">
                                        <td>
                                            <span class="badge bg-secondary order-badge"><?php echo e($ad->display_order); ?></span>
                                            <div class="drag-handle-container ms-2 d-inline-block">
                                                <i class="mdi mdi-drag-vertical text-muted" style="cursor: move;"></i>
                                                <div class="drag-tooltip">Drag to reorder</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if($ad->image): ?>
                                                <img src="<?php echo e(asset($ad->image)); ?>" alt="<?php echo e($ad->title); ?>" 
                                                     class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-1"><?php echo e(Str::limit($ad->title, 40)); ?></h6>
                                                    <small class="text-muted"><?php echo e($ad->created_at->format('M d, Y')); ?></small>
                                                    <?php if($ad->link_url): ?>
                                                        <br><small class="text-info"><?php echo e(Str::limit($ad->link_url, 30)); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e(ucfirst($ad->ad_type)); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e(ucfirst($ad->position)); ?></span>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input status-toggle" type="checkbox" 
                                                       data-ad-id="<?php echo e($ad->id); ?>" <?php echo e($ad->is_active ? 'checked' : ''); ?>>
                                                <label class="form-check-label">
                                                    <?php echo e($ad->is_active ? 'Active' : 'Inactive'); ?>

                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column performance-metrics" data-ad-id="<?php echo e($ad->id); ?>" data-ad-type="advertisement">
                                                <small><strong>Views:</strong> <span class="view-count"><?php echo e(number_format($ad->view_count)); ?></span></small>
                                                <small><strong>Clicks:</strong> <span class="click-count"><?php echo e(number_format($ad->click_count)); ?></span></small>
                                                <?php if($ad->view_count > 0): ?>
                                                <small><strong>CTR:</strong> <span class="ctr-rate"><?php echo e(round(($ad->click_count / $ad->view_count) * 100, 2)); ?>%</span></small>
                                                <?php endif; ?>
                                                <?php
                                                    $performance = $ad->getPerformanceMetrics();
                                                    $statusClass = match($performance['status']) {
                                                        'excellent' => 'success',
                                                        'good' => 'primary',
                                                        'average' => 'warning',
                                                        'poor' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                ?>
                                                <small>
                                                    <span class="badge bg-<?php echo e($statusClass); ?> performance-badge">
                                                        Score: <span class="performance-score"><?php echo e($performance['performance_score']); ?></span>
                                                    </span>
                                                </small>
                                                <div class="performance-refresh mt-1">
                                                    <button class="btn btn-xs btn-outline-secondary refresh-performance" data-ad-id="<?php echo e($ad->id); ?>" data-ad-type="advertisement">
                                                        <i class="mdi mdi-refresh"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- View Button -->
                                                <a href="<?php echo e(route('admin.advertisements.show', $ad->id)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                
                                                <!-- Edit Button -->
                                                <a href="<?php echo e(route('admin.advertisements.edit', $ad->id)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>

                                                <!-- Status Toggle Button -->
                                                <?php if($ad->is_active): ?>
                                                    <button class="btn btn-sm btn-outline-secondary action-toggle"
                                                            data-ad-id="<?php echo e($ad->id); ?>" data-action="deactivate" title="Deactivate">
                                                        <i class="mdi mdi-pause"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-outline-success action-toggle"
                                                            data-ad-id="<?php echo e($ad->id); ?>" data-action="activate" title="Activate">
                                                        <i class="mdi mdi-play"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <!-- Delete Button -->
                                                <form method="POST" action="<?php echo e(route('admin.advertisements.destroy', $ad->id)); ?>" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this advertisement?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Showing <?php echo e($advertisements->firstItem()); ?> to <?php echo e($advertisements->lastItem()); ?> of <?php echo e($advertisements->total()); ?> advertisements
                            </div>
                            <div>
                                <?php echo e($advertisements->links()); ?>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Advertisement Management Specific Styles */
.ad-row {
    transition: all 0.2s ease;
    cursor: default;
}

.ad-row:hover {
    background-color: #f8f9fa !important;
}

.ad-row.ui-sortable-helper {
    background-color: #fff !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
    border: 2px solid #007bff !important;
    opacity: 0.9 !important;
    transform: rotate(1deg);
    z-index: 1000 !important;
}

.ad-row.table-secondary {
    opacity: 0.6;
    background-color: #f8f9fa !important;
}

.ui-state-highlight {
    height: 60px !important;
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    border-radius: 4px;
    margin: 2px 0;
}

.order-badge {
    min-width: 35px;
    display: inline-block;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.order-badge.updating {
    background-color: #ffc107 !important;
    color: #000 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

#sortable-ads .mdi-drag-vertical {
    opacity: 0.3;
    transition: all 0.2s ease;
    cursor: grab;
    font-size: 18px;
    color: #6c757d;
}

#sortable-ads .mdi-drag-vertical:active {
    cursor: grabbing;
}

#sortable-ads tr:hover .mdi-drag-vertical {
    opacity: 1;
    color: #007bff;
}

.drag-handle-container {
    position: relative;
    display: inline-block;
}

.drag-tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
    z-index: 1000;
}

.drag-handle-container:hover .drag-tooltip {
    opacity: 1;
}

/* Performance metrics styling */
.performance-updated {
    background-color: #d4edda !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}

/* Search and filter styling */
.search-filter-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group:last-child {
    margin-bottom: 0;
}
</style>

<script>
// Advertisement Management - Complete Clean Implementation
(function() {
    'use strict';

    // Wait for jQuery to be available
    function waitForJQuery(callback) {
        if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
            callback();
        } else {
            setTimeout(function() { waitForJQuery(callback); }, 100);
        }
    }

    // Initialize when ready
    waitForJQuery(function() {
        $(document).ready(function() {
            console.log('=== Advertisement Management Initialization ===');
            console.log('jQuery version:', $.fn.jquery);

            // Initialize toastr
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    "closeButton": true,
                    "progressBar": true,
                    "positionClass": "toast-top-right",
                    "timeOut": "3000",
                    "preventDuplicates": true
                };
            }

            // Initialize all features
            initializeSearchFilters();
            initializeDragAndDrop();
            initializeStatusToggles();
            initializePerformanceTracking();

            console.log('Advertisement Management initialized successfully');
        });
    });

    // Search and Filter Functionality
    function initializeSearchFilters() {
        console.log('Initializing search filters...');

        $('#searchAds').on('input', filterAdvertisements);
        $('#filterStatus').on('change', filterAdvertisements);
        $('#filterType').on('change', filterAdvertisements);

        function filterAdvertisements() {
            var searchTerm = $('#searchAds').val().toLowerCase();
            var statusFilter = $('#filterStatus').val();
            var typeFilter = $('#filterType').val();
            var visibleCount = 0;

            $('#sortable-ads tr').each(function() {
                var $row = $(this);
                var title = $row.find('td').eq(1).find('h6').text().toLowerCase();
                var url = $row.find('td').eq(1).find('.text-info').text().toLowerCase();
                var typeText = $row.find('td').eq(2).text().toLowerCase();
                var isActive = !$row.hasClass('table-secondary');

                var searchMatch = searchTerm === '' ||
                                title.includes(searchTerm) ||
                                url.includes(searchTerm) ||
                                typeText.includes(searchTerm);

                var statusMatch = statusFilter === '' ||
                                (statusFilter === 'active' && isActive) ||
                                (statusFilter === 'inactive' && !isActive);

                var typeMatch = typeFilter === '' || typeText.includes(typeFilter.toLowerCase());

                if (searchMatch && statusMatch && typeMatch) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            updateResultsCount(visibleCount);
        }

        function updateResultsCount(count) {
            var $resultsInfo = $('.results-info');
            if ($resultsInfo.length === 0) {
                $('#adsTable').before('<div class="results-info mb-2"><small class="text-muted">Showing <span class="results-count">' + count + '</span> advertisements</small></div>');
            } else {
                $resultsInfo.find('.results-count').text(count);
            }
        }
    }

    // Drag and Drop Functionality
    function initializeDragAndDrop() {
        console.log('Initializing drag and drop...');

        if (typeof $.ui === 'undefined' || typeof $.ui.sortable === 'undefined') {
            console.warn('jQuery UI not available, loading dynamically...');
            var script = document.createElement('script');
            script.src = 'https://code.jquery.com/ui/1.13.2/jquery-ui.min.js';
            script.onload = function() {
                setTimeout(initializeDragAndDrop, 100);
            };
            document.head.appendChild(script);
            return;
        }

        var $table = $('#sortable-ads');
        if ($table.length === 0) {
            console.warn('Sortable table not found');
            return;
        }

        try {
            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr:visible",
                axis: "y",
                cursor: "grabbing",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                opacity: 0.8,
                distance: 5,
                update: function(event, ui) {
                    var order = [];
                    $table.find('tr:visible').each(function(index) {
                        var adId = $(this).data('ad-id');
                        if (adId) {
                            order.push({
                                id: adId,
                                order: index + 1
                            });
                            $(this).find('.order-badge').text(index + 1);
                        }
                    });

                    updateAdOrder(order);
                }
            });

            console.log('Drag and drop initialized successfully');
        } catch (error) {
            console.error('Drag and drop initialization error:', error);
        }
    }

    function updateAdOrder(order) {
        console.log('Updating advertisement order:', order);

        $.ajax({
            url: '<?php echo e(route("admin.advertisements.update-order")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>',
                advertisements: order
            },
            beforeSend: function() {
                console.log('Sending advertisement order update request...');
            },
            success: function(response) {
                console.log('Advertisement order update response:', response);

                if (response.success) {
                    toastr.success('Advertisement order updated successfully!');
                } else {
                    toastr.error('Failed to update advertisement order');
                    setTimeout(function() { location.reload(); }, 2000);
                }
            },
            error: function(xhr, status, error) {
                console.error('Advertisement order update error:', xhr.responseText);
                toastr.error('Failed to update advertisement order');
                setTimeout(function() { location.reload(); }, 2000);
            }
        });
    }

    // Status Toggle Functionality
    function initializeStatusToggles() {
        console.log('Initializing status toggles...');

        $(document).on('change', '.status-toggle', function() {
            var $toggle = $(this);
            var adId = $toggle.data('ad-id');
            var isActive = $toggle.is(':checked');

            $toggle.prop('disabled', true);

            $.ajax({
                url: '/admin/advertisements/' + adId + '/toggle-status',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    is_active: isActive
                },
                success: function(response) {
                    if (response.success) {
                        var $row = $toggle.closest('tr');
                        if (isActive) {
                            $row.removeClass('table-secondary');
                            toastr.success('Advertisement activated successfully!');
                        } else {
                            $row.addClass('table-secondary');
                            toastr.success('Advertisement deactivated successfully!');
                        }
                    } else {
                        $toggle.prop('checked', !isActive);
                        toastr.error(response.message || 'Failed to update status');
                    }
                },
                error: function(xhr) {
                    $toggle.prop('checked', !isActive);
                    toastr.error('Failed to update advertisement status');
                },
                complete: function() {
                    $toggle.prop('disabled', false);
                }
            });
        });
    }

    // Performance Tracking Functionality
    function initializePerformanceTracking() {
        console.log('Initializing performance tracking...');

        // Auto-refresh performance stats every 30 seconds
        setInterval(function() {
            refreshPerformanceStats();
        }, 30000);

        // Manual refresh button
        $(document).on('click', '.refresh-performance', function() {
            refreshPerformanceStats();
        });
    }

    function refreshPerformanceStats() {
        $.ajax({
            url: '<?php echo e(route("admin.advertisements.performance-stats")); ?>',
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updatePerformanceDisplay(response.data);
                }
            },
            error: function() {
                console.error('Failed to refresh performance stats');
            }
        });
    }

    function updatePerformanceDisplay(data) {
        // Update performance badges and stats
        $('.total-ads').text(data.total || 0);
        $('.active-ads').text(data.active || 0);
        $('.total-views').text(data.total_views || 0);
        $('.total-clicks').text(data.total_clicks || 0);
    }

})(); // End of IIFE
</script>

<style>
.ui-state-highlight {
    height: 60px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
}

.drag-handle {
    cursor: grab;
}

.drag-handle:active {
    cursor: grabbing;
}

.refresh-performance {
    opacity: 0.7;
    transition: opacity 0.3s;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/advertisements/index.blade.php ENDPATH**/ ?>