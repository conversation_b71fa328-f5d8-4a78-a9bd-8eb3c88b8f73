<?php $__env->startSection('admin'); ?>

<div class="container-fluid">
    
    <!-- start page title -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Site Settings</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    <i class="mdi mdi-cog text-primary me-2"></i>Site Settings
                </h4>
                <p class="page-title-desc">Manage your website's general settings, appearance, and configuration.</p>
            </div>
        </div>
    </div>     
    <!-- end page title --> 

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="mdi mdi-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form action="<?php echo e(route('admin.site-settings.update')); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <div class="row">
            <?php $__currentLoopData = $settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group => $groupSettings): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xl-6 col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">
                            <?php switch($group):
                                case ('general'): ?>
                                    <i class="mdi mdi-cog text-primary me-2"></i>General Settings
                                    <?php break; ?>
                                <?php case ('appearance'): ?>
                                    <i class="mdi mdi-palette text-info me-2"></i>Appearance Settings
                                    <?php break; ?>
                                <?php case ('contact'): ?>
                                    <i class="mdi mdi-phone text-success me-2"></i>Contact Information
                                    <?php break; ?>
                                <?php case ('social'): ?>
                                    <i class="mdi mdi-share-variant text-warning me-2"></i>Social Media
                                    <?php break; ?>
                                <?php default: ?>
                                    <i class="mdi mdi-settings text-secondary me-2"></i><?php echo e(ucfirst($group)); ?> Settings
                            <?php endswitch; ?>
                        </h4>
                        <p class="card-title-desc">
                            <?php switch($group):
                                case ('general'): ?>
                                    Configure basic site information and metadata
                                    <?php break; ?>
                                <?php case ('appearance'): ?>
                                    Manage logos, favicons, and visual elements
                                    <?php break; ?>
                                <?php case ('contact'): ?>
                                    Update contact details and address information
                                    <?php break; ?>
                                <?php case ('social'): ?>
                                    Add social media links and profiles
                                    <?php break; ?>
                                <?php default: ?>
                                    Manage <?php echo e($group); ?> related settings
                            <?php endswitch; ?>
                        </p>
                    </div>
                    <div class="card-body">
                        <?php $__currentLoopData = $groupSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="mb-3">
                            <label for="<?php echo e($setting->key); ?>" class="form-label">
                                <?php echo e($setting->label); ?>

                                <?php if($setting->description): ?>
                                    <i class="mdi mdi-information-outline text-muted ms-1" 
                                       data-bs-toggle="tooltip" 
                                       title="<?php echo e($setting->description); ?>"></i>
                                <?php endif; ?>
                            </label>
                            
                            <?php switch($setting->type):
                                case ('textarea'): ?>
                                    <textarea name="settings[<?php echo e($setting->key); ?>]" 
                                              id="<?php echo e($setting->key); ?>" 
                                              class="form-control" 
                                              rows="3"
                                              placeholder="<?php echo e($setting->description); ?>"><?php echo e($setting->value); ?></textarea>
                                    <?php break; ?>
                                    
                                <?php case ('image'): ?>
                                    <div class="input-group">
                                        <input type="file" 
                                               name="settings[<?php echo e($setting->key); ?>]" 
                                               id="<?php echo e($setting->key); ?>" 
                                               class="form-control" 
                                               accept="image/*">
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearImage('<?php echo e($setting->key); ?>')">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    <?php if($setting->value): ?>
                                        <div class="mt-2">
                                            <img src="<?php echo e(asset($setting->value)); ?>"
                                                 alt="<?php echo e($setting->label); ?>"
                                                 class="img-thumbnail"
                                                 style="max-height: 100px;">
                                            <small class="text-muted d-block">Current: <?php echo e(basename($setting->value)); ?></small>
                                        </div>
                                    <?php endif; ?>
                                    <?php break; ?>
                                    
                                <?php case ('file'): ?>
                                    <div class="input-group">
                                        <input type="file" 
                                               name="settings[<?php echo e($setting->key); ?>]" 
                                               id="<?php echo e($setting->key); ?>" 
                                               class="form-control">
                                        <button type="button" class="btn btn-outline-secondary" onclick="clearFile('<?php echo e($setting->key); ?>')">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                    <?php if($setting->value): ?>
                                        <div class="mt-2">
                                            <a href="<?php echo e(asset('storage/' . $setting->value)); ?>" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="mdi mdi-download me-1"></i><?php echo e(basename($setting->value)); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <?php break; ?>
                                    
                                <?php case ('boolean'): ?>
                                    <div class="form-check form-switch">
                                        <input type="hidden" name="settings[<?php echo e($setting->key); ?>]" value="0">
                                        <input type="checkbox" 
                                               name="settings[<?php echo e($setting->key); ?>]" 
                                               id="<?php echo e($setting->key); ?>" 
                                               class="form-check-input" 
                                               value="1" 
                                               <?php echo e($setting->value ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="<?php echo e($setting->key); ?>">
                                            <?php echo e($setting->description ?: 'Enable this option'); ?>

                                        </label>
                                    </div>
                                    <?php break; ?>
                                    
                                <?php case ('email'): ?>
                                    <input type="email" 
                                           name="settings[<?php echo e($setting->key); ?>]" 
                                           id="<?php echo e($setting->key); ?>" 
                                           class="form-control" 
                                           value="<?php echo e($setting->value); ?>" 
                                           placeholder="<?php echo e($setting->description); ?>">
                                    <?php break; ?>
                                    
                                <?php case ('url'): ?>
                                    <input type="url" 
                                           name="settings[<?php echo e($setting->key); ?>]" 
                                           id="<?php echo e($setting->key); ?>" 
                                           class="form-control" 
                                           value="<?php echo e($setting->value); ?>" 
                                           placeholder="<?php echo e($setting->description); ?>">
                                    <?php break; ?>
                                    
                                <?php default: ?>
                                    <input type="text" 
                                           name="settings[<?php echo e($setting->key); ?>]" 
                                           id="<?php echo e($setting->key); ?>" 
                                           class="form-control" 
                                           value="<?php echo e($setting->value); ?>" 
                                           placeholder="<?php echo e($setting->description); ?>">
                            <?php endswitch; ?>
                            
                            <?php if($setting->description && $setting->type !== 'boolean'): ?>
                                <small class="form-text text-muted"><?php echo e($setting->description); ?></small>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="mdi mdi-content-save me-2"></i>Save Settings
                        </button>
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-secondary btn-lg ms-2">
                            <i class="mdi mdi-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
</div>

<script>
// Check if Waves is defined, if not, define a dummy to prevent errors
if (typeof Waves === 'undefined') {
    window.Waves = {
        init: function() {},
        attach: function() {},
        ripple: function() {}
    };
}

// Check if feather is defined, if not, define a dummy to prevent errors
if (typeof feather === 'undefined') {
    window.feather = {
        replace: function() {},
        icons: {}
    };
}

function clearImage(key) {
    document.getElementById(key).value = '';
}

function clearFile(key) {
    document.getElementById(key).value = '';
}

// Ensure jQuery is loaded before initializing
function initializeSiteSettings() {
    if (typeof $ === 'undefined') {
        console.log('Waiting for jQuery to load...');
        setTimeout(initializeSiteSettings, 100);
        return;
    }

    $(document).ready(function() {
        console.log('=== Site Settings System Starting ===');
        console.log('jQuery loaded:', typeof $ !== 'undefined');
        console.log('jQuery version:', $.fn.jquery);
    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
    
    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        
        // Validate required fields
        $('input[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            toastr.error('Please fill in all required fields.');
        }
    });
    
    // Real-time validation
    $('input, textarea').on('blur', function() {
        if ($(this).attr('required') && !$(this).val()) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

        console.log('=== Site Settings System Initialized Successfully ===');
    });
}

// Initialize when page loads
initializeSiteSettings();
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/site_settings/index.blade.php ENDPATH**/ ?>