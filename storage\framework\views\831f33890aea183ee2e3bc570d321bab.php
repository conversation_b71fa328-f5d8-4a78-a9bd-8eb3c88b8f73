

<?php $__env->startSection('title'); ?>
NitiKotha - Premium News Portal
<?php $__env->stopSection(); ?>

<?php $__env->startSection('home'); ?>

<!-- Content sections will be added here section by section -->
<div class="page-content">
    <div class="container">
        <?php if(request()->is('subscriber/posts')): ?>
            <!-- My Posts Welcome -->
            <div class="dashboard-welcome">
                <div class="welcome-card posts-welcome">
                    <div class="welcome-content">
                        <h1>My Posts</h1>
                        <p>Manage all your posts, track their approval status, and create new content.</p>
                        <div class="posts-stats">
                            <?php if(isset($posts)): ?>
                                <span class="stat-item">
                                    <i class="fas fa-file-alt"></i>
                                    <?php echo e($posts->total()); ?> Total Posts
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-check-circle"></i>
                                    <?php echo e($posts->where('approval_status', 'approved')->count()); ?> Approved
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i>
                                    <?php echo e($posts->where('approval_status', 'pending')->count()); ?> Pending
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif(request()->is('subscriber/profile')): ?>
            <!-- Edit Profile Welcome -->
            <div class="dashboard-welcome">
                <div class="welcome-card profile-welcome">
                    <div class="welcome-content">
                        <h1>Edit Profile</h1>
                        <p>Update your personal information, change your avatar, and manage your account settings.</p>
                        <div class="form-tips">
                            <span class="tip-item">
                                <i class="fas fa-shield-alt"></i>
                                Keep your information secure
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-envelope"></i>
                                Email changes require verification
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-key"></i>
                                Leave password empty to keep current
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif(request()->is('subscriber/posts/*/edit')): ?>
            <!-- Edit Post Welcome -->
            <div class="dashboard-welcome">
                <div class="welcome-card edit-post-welcome">
                    <div class="welcome-content">
                        <h1>Edit Post</h1>
                        <p>Update your post content and settings. Your changes will be submitted for review.</p>
                        <div class="form-tips">
                            <span class="tip-item">
                                <i class="fas fa-info-circle"></i>
                                Only pending/rejected posts can be edited
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-save"></i>
                                Changes require re-approval
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-image"></i>
                                Leave image empty to keep current
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif(request()->is('subscriber/posts/create')): ?>
            <!-- Create Post Welcome -->
            <div class="dashboard-welcome">
                <div class="welcome-card create-post-welcome">
                    <div class="welcome-content">
                        <h1>Create New Post</h1>
                        <p>Share your story with our community. Fill out the form below to submit your post for review.</p>
                        <div class="form-tips">
                            <span class="tip-item">
                                <i class="fas fa-lightbulb"></i>
                                Use engaging titles
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-image"></i>
                                Add quality images
                            </span>
                            <span class="tip-item">
                                <i class="fas fa-spell-check"></i>
                                Proofread content
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        <?php elseif(request()->is('subscriber/dashboard')): ?>
            <!-- Subscriber Dashboard Welcome -->
            <div class="dashboard-welcome">
                <div class="welcome-card">
                    <div class="welcome-content">
                        <h1>Welcome back, <?php echo e(auth()->user()->name); ?>!</h1>
                        <p>Manage your posts and track their approval status</p>
                        <a href="<?php echo e(route('subscriber.posts.create')); ?>" class="create-post-btn">
                            <i class="fas fa-plus"></i>
                            Create New Post
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Default Home Content -->
            <div class="content-placeholder">
                <div class="placeholder-content">
                    <h1>Welcome to NitiKotha</h1>
                    <p>Premium news portal with modern design. Content sections will be added progressively.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->stopSection(); ?>
    <div class="container">

<?php echo $__env->make('frontend.home_dashboard_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/index_modern.blade.php ENDPATH**/ ?>