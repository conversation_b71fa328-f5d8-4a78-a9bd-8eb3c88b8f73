@php
$cdate = new DateTime();
@endphp

<!-- Top Navigation Bar -->
<div class="top-nav">
    <div class="container">
        <div class="top-nav-content">
            <div class="top-nav-left">
                <div class="date-display">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ $cdate->format('l, F d, Y') }}</span>
                </div>
                
                <div class="language-selector">
                    <select class="changeLang">
                        <option value="en" {{ session()->get('locale') == 'en' ? 'selected' : '' }}>English</option>
                        <option value="bn" {{ session()->get('locale') == 'bn' ? 'selected' : '' }}>বাংলা</option>
                        <option value="hi" {{ session()->get('locale') == 'hi' ? 'selected' : '' }}>हिंदी</option>
                        <option value="fr" {{ session()->get('locale') == 'fr' ? 'selected' : '' }}>Français</option>
                    </select>
                </div>
            </div>
            
            <div class="top-nav-right">
                <div class="social-links">
                    <a href="https://www.facebook.com/" target="_blank" title="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://twitter.com/" target="_blank" title="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.instagram.com/" target="_blank" title="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://www.youtube.com/" target="_blank" title="YouTube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                
                <div class="auth-links">
                    @auth
                        @if(auth()->user()->hasRole('subscriber'))
                            <a href="{{ route('subscriber.dashboard') }}" class="dashboard-link">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        @endif
                        <a href="{{ route('user.logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    @else
                        <a href="{{ route('login') }}">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                        <a href="{{ route('register') }}">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="main-header">
    <div class="container">
        <div class="header-content">
            <div class="header-row">
                <div class="logo-section">
                    <a href="{{ url('/') }}" title="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}">
                        @php
                            $siteLogo = \App\Models\SiteSetting::get('site_logo');
                            $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.png');
                        @endphp
                        <img src="{{ $logoPath }}" alt="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}" title="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}">
                    </a>
                </div>
                
                <div class="search-section">
                    <form class="search-form" action="{{ route('news.search') }}" method="post">
                        @csrf
                        <input type="text" name="search" placeholder="Search news, articles, topics..." required>
                        <button type="submit" title="Search">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <div class="header-actions">
                    <div class="weather-widget">
                        <i class="fas fa-cloud-sun"></i>
                        <span>25°C</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Main Navigation -->
<nav class="main-navigation">
    <div class="container">
        <div class="nav-container">
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <ul class="nav-menu" id="navMenu">
                <li class="nav-item active">
                    <a href="{{ url('/') }}">
                        <i class="fas fa-home"></i>
                        Home
                    </a>
                </li>
                
                {{-- Use optimized cached data from HeaderDataProvider --}}
                @foreach($header_categories as $category)
                <li class="nav-item {{ $category->subcategories->count() > 0 ? 'has-dropdown' : '' }}">
                    <a href="{{ url('news/category/'.$category->id.'/'.$category->category_slug) }}">
                        {{ GoogleTranslate::trans($category->category_name, app()->getLocale()) }}
                    </a>
                    
                    @if($category->subcategories->count() > 0)
                    <div class="dropdown-menu">
                        @foreach($category->subcategories as $subcategory)
                        <a href="{{ url('news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug) }}">
                            <i class="fas fa-angle-right"></i>
                            {{ GoogleTranslate::trans($subcategory->subcategory_name, app()->getLocale()) }}
                        </a>
                        @endforeach
                    </div>
                    @endif
                </li>
                @endforeach
                
                <li class="nav-item">
                    <a href="#">
                        <i class="fas fa-archive"></i>
                        Archive
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
// Language change functionality
document.addEventListener('DOMContentLoaded', function() {
    const langSelector = document.querySelector('.changeLang');
    if (langSelector) {
        langSelector.addEventListener('change', function() {
            const url = "{{ route('changeLang') }}";
            window.location.href = url + "?lang=" + this.value;
        });
    }
    
    // Mobile menu toggle
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.getElementById('navMenu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            this.querySelector('i').classList.toggle('fa-bars');
            this.querySelector('i').classList.toggle('fa-times');
        });
    }
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.nav-container') && navMenu) {
            navMenu.classList.remove('active');
            if (mobileToggle) {
                mobileToggle.querySelector('i').classList.add('fa-bars');
                mobileToggle.querySelector('i').classList.remove('fa-times');
            }
        }
    });
    
    // Search form enhancement
    const searchForm = document.querySelector('.search-form');
    const searchInput = searchForm?.querySelector('input');
    
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            searchForm.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            searchForm.style.transform = 'scale(1)';
        });
    }
});
</script>
