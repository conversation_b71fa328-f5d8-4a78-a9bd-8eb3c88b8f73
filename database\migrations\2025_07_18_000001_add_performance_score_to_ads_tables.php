<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->decimal('performance_score', 5, 2)->default(0)->after('view_count')->comment('Performance score 0-100');
        });

        Schema::table('sponsored_ads', function (Blueprint $table) {
            $table->decimal('performance_score', 5, 2)->default(0)->after('view_count')->comment('Performance score 0-100');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('advertisements', function (Blueprint $table) {
            $table->dropColumn('performance_score');
        });

        Schema::table('sponsored_ads', function (Blueprint $table) {
            $table->dropColumn('performance_score');
        });
    }
};
