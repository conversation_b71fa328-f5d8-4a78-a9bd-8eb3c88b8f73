{{-- Sponsored Ad Component --}}
@if($ad->isCurrentlyActive())
<div class="fb-sponsored-ad fb-sponsored-ad-{{ $position }}" data-ad-id="{{ $ad->id }}">
    <div class="fb-sponsored-header">
        <span class="fb-sponsored-label">
            <i class="fas fa-bullhorn"></i>
            Sponsored
        </span>
        @if($ad->is_premium)
            <span class="fb-premium-badge">
                <i class="fas fa-crown"></i>
                Premium
            </span>
        @endif
    </div>

    @if($ad->ad_format === 'native')
        {{-- Native Ad Format --}}
        <div class="fb-native-ad">
            <div class="fb-native-header">
                @if($ad->sponsor_logo)
                    <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-logo">
                @endif
                <div class="fb-sponsor-info">
                    <h6 class="fb-sponsor-name">{{ $ad->sponsor_name }}</h6>
                    <span class="fb-sponsor-label">Sponsored</span>
                </div>
            </div>
            
            @if($ad->image)
                <div class="fb-native-image">
                    <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" loading="lazy">
                </div>
            @endif
            
            <div class="fb-native-content">
                <h5 class="fb-native-title">{{ $ad->title }}</h5>
                <p class="fb-native-description">{{ Str::limit($ad->content, 120) }}</p>
                
                @if($ad->link_url)
                    <a href="{{ $ad->link_url }}" target="_blank" class="fb-native-cta" onclick="trackAdClick({{ $ad->id }})">
                        Learn More
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                @endif
            </div>
        </div>

    @elseif($ad->ad_format === 'display')
        {{-- Display Banner Ad --}}
        <div class="fb-display-ad">
            @if($ad->link_url)
                <a href="{{ $ad->link_url }}" target="_blank" onclick="trackAdClick({{ $ad->id }})">
            @endif
            
            @if($ad->image)
                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-display-image">
            @else
                <div class="fb-display-text">
                    <h5>{{ $ad->title }}</h5>
                    <p>{{ $ad->content }}</p>
                </div>
            @endif
            
            @if($ad->link_url)
                </a>
            @endif
        </div>

    @elseif($ad->ad_format === 'video')
        {{-- Video Ad Format --}}
        <div class="fb-video-ad">
            <div class="fb-video-header">
                <h6>{{ $ad->title }}</h6>
                <span class="fb-sponsored-label">Sponsored</span>
            </div>
            
            @if($ad->video_url)
                <div class="fb-video-container">
                    <video controls poster="{{ $ad->image ? asset($ad->image) : '' }}">
                        <source src="{{ $ad->video_url }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            @elseif($ad->image)
                <div class="fb-video-placeholder">
                    <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}">
                    <div class="fb-play-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
            @endif
            
            <div class="fb-video-content">
                <p>{{ Str::limit($ad->content, 100) }}</p>
                @if($ad->link_url)
                    <a href="{{ $ad->link_url }}" target="_blank" class="fb-video-cta" onclick="trackAdClick({{ $ad->id }})">
                        Watch Now
                    </a>
                @endif
            </div>
        </div>

    @elseif($ad->ad_format === 'carousel')
        {{-- Carousel Ad Format --}}
        <div class="fb-carousel-ad">
            <div class="fb-carousel-header">
                <h6>{{ $ad->title }}</h6>
                <span class="fb-sponsored-label">Sponsored</span>
            </div>
            
            <div class="fb-carousel-container">
                <div class="fb-carousel-item">
                    @if($ad->image)
                        <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}">
                    @endif
                    <div class="fb-carousel-content">
                        <h6>{{ $ad->title }}</h6>
                        <p>{{ Str::limit($ad->content, 80) }}</p>
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" target="_blank" onclick="trackAdClick({{ $ad->id }})">
                                Learn More
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- Ad Actions --}}
    <div class="fb-ad-actions">
        <button class="fb-ad-action" onclick="hideAd({{ $ad->id }})">
            <i class="fas fa-times"></i>
            Hide Ad
        </button>
        <button class="fb-ad-action" onclick="reportAd({{ $ad->id }})">
            <i class="fas fa-flag"></i>
            Report
        </button>
    </div>
</div>

{{-- Track Ad View --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track ad view when it comes into viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                trackAdView({{ $ad->id }});
                observer.unobserve(entry.target);
            }
        });
    });
    
    const adElement = document.querySelector('[data-ad-id="{{ $ad->id }}"]');
    if (adElement) {
        observer.observe(adElement);
    }
});
</script>
@endif
