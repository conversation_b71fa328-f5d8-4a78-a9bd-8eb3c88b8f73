{{-- Sponsored Advertisement Display Component --}}
@if($ad && $ad->is_active)
<div class="fb-sponsored-container" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored" data-position="{{ $position ?? 'default' }}">
    <div class="fb-sponsored-ad fb-sponsored-{{ $ad->ad_format }} fb-sponsored-position-{{ $position ?? 'default' }}">
        <!-- Sponsored Ad Header -->
        <div class="fb-sponsored-header">
            <div class="fb-sponsored-label">
                <i class="fas fa-star"></i>
                <span>Sponsored</span>
                @if($ad->is_premium)
                    <span class="fb-premium-badge">
                        <i class="fas fa-crown"></i>
                        Premium
                    </span>
                @endif
            </div>
            <div class="fb-sponsored-actions">
                <button class="fb-sponsored-action" data-action="hide" data-ad-id="{{ $ad->id }}" title="Hide this ad">
                    <i class="fas fa-times"></i>
                </button>
                <button class="fb-sponsored-action" data-action="report" data-ad-id="{{ $ad->id }}" title="Report ad">
                    <i class="fas fa-flag"></i>
                </button>
            </div>
        </div>

        <!-- Sponsored Ad Content -->
        <div class="fb-sponsored-content">
            @if($ad->ad_format === 'native')
                <!-- Native Sponsored Ad -->
                <div class="fb-native-sponsored">
                    <div class="fb-native-header">
                        @if($ad->sponsor_logo)
                            <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-logo">
                        @endif
                        <div class="fb-sponsor-info">
                            <h6 class="fb-sponsor-name">{{ $ad->sponsor_name }}</h6>
                            <span class="fb-sponsor-verified">
                                <i class="fas fa-check-circle"></i>
                                Verified Sponsor
                            </span>
                        </div>
                    </div>
                    
                    @if($ad->image)
                        <div class="fb-native-image">
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                        </div>
                    @endif
                    
                    <div class="fb-native-text">
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        <p class="fb-sponsored-description">{{ Str::limit($ad->content, 150) }}</p>
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-native-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-external-link-alt"></i>
                                Learn More
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'display')
                <!-- Display Banner Sponsored Ad -->
                <div class="fb-display-sponsored">
                    @if($ad->image)
                        <div class="fb-display-image">
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                            @if($ad->link_url)
                                <div class="fb-display-overlay">
                                    <a href="{{ $ad->link_url }}" class="fb-display-link" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                    
                    <div class="fb-display-content">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        @if($ad->content)
                            <p class="fb-sponsored-description">{{ Str::limit($ad->content, 100) }}</p>
                        @endif
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-display-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                Visit Now
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'video')
                <!-- Video Sponsored Ad -->
                <div class="fb-video-sponsored">
                    <div class="fb-video-container">
                        @if($ad->video_url)
                            <video class="fb-sponsored-video" poster="{{ $ad->image ? asset($ad->image) : '' }}" controls>
                                <source src="{{ $ad->video_url }}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        @elseif($ad->image)
                            <div class="fb-video-placeholder">
                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                                <div class="fb-video-play-overlay">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <div class="fb-video-info">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        @if($ad->content)
                            <p class="fb-sponsored-description">{{ Str::limit($ad->content, 120) }}</p>
                        @endif
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-video-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-play"></i>
                                Watch Now
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'carousel')
                <!-- Carousel Sponsored Ad -->
                <div class="fb-carousel-sponsored">
                    <div class="fb-carousel-header">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                    </div>
                    
                    <div class="fb-carousel-container">
                        <div class="fb-carousel-items">
                            @if($ad->image)
                                <div class="fb-carousel-item">
                                    <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-carousel-image">
                                    <div class="fb-carousel-text">
                                        <h5>{{ $ad->title }}</h5>
                                        <p>{{ Str::limit($ad->content, 80) }}</p>
                                    </div>
                                </div>
                            @endif
                            
                            <!-- Additional carousel items can be added here -->
                            <div class="fb-carousel-item">
                                <div class="fb-carousel-placeholder">
                                    <i class="fas fa-image"></i>
                                    <span>More Items</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fb-carousel-controls">
                            <button class="fb-carousel-prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="fb-carousel-next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    
                    @if($ad->link_url)
                        <div class="fb-carousel-footer">
                            <a href="{{ $ad->link_url }}" class="fb-carousel-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-shopping-cart"></i>
                                Shop Now
                            </a>
                        </div>
                    @endif
                </div>

            @endif
        </div>

        {{-- Sponsored Ad Footer removed for clean frontend display --}}
    </div>
</div>

@push('scripts')
<script>
// Sponsored advertisement interaction tracking
(function() {
    var adId = {{ $ad->id }};
    var adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="sponsored"]');

    console.log('=== Sponsored Ad Tracking Initialization ===');
    console.log('Ad ID:', adId);
    console.log('Container found:', adContainer.length > 0);

    // Ensure jQuery is available
    function initializeSponsoredAdTracking() {
        if (typeof $ === 'undefined') {
            console.warn('jQuery not available for sponsored ad tracking, retrying...');
            setTimeout(initializeSponsoredAdTracking, 500);
            return;
        }

        console.log('Initializing tracking for sponsored ad:', adId);

        // Re-find container after jQuery is available
        adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="sponsored"]');
        console.log('Container found after jQuery load:', adContainer.length > 0);

        // Track sponsored ad view using Intersection Observer for accurate tracking
        if (typeof IntersectionObserver !== 'undefined') {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        console.log('Sponsored ad came into view, tracking view for ad:', adId);
                        trackSponsoredAdView(adId);
                        observer.unobserve(entry.target); // Track view only once
                    }
                });
            }, { threshold: 0.5 });

            if (adContainer.length > 0) {
                observer.observe(adContainer[0]);
                console.log('Intersection observer set up for sponsored ad:', adId);
            } else {
                console.warn('Container not found for intersection observer, sponsored ad:', adId);
                // Fallback: track view immediately
                setTimeout(function() { trackSponsoredAdView(adId); }, 1000);
            }
        } else {
            // Fallback for older browsers
            console.log('IntersectionObserver not available, tracking view immediately for sponsored ad:', adId);
            trackSponsoredAdView(adId);
        }

        setupSponsoredAdClickTracking();
    }

    function setupSponsoredAdClickTracking() {
        // Track sponsored ad clicks
        $(document).on('click', '[data-ad-id="' + adId + '"][data-ad-type="sponsored"] a[href]', function(e) {
            console.log('Sponsored ad click detected for ad:', adId);
            trackSponsoredAdClick(adId);
        });

        // Handle sponsored ad actions (hide, report)
        $(document).on('click', '[data-ad-id="' + adId + '"][data-ad-type="sponsored"] .fb-sponsored-action', function(e) {
            e.preventDefault();
            var action = $(this).data('action');

            console.log('Sponsored ad action:', action, 'for ad:', adId);

            if (action === 'hide') {
                hideSponsoredAd(adId);
            } else if (action === 'report') {
                reportSponsoredAd(adId);
            }
        });

        // Handle carousel controls
        @if($ad->ad_format === 'carousel')
        $(document).on('click', '[data-ad-id="' + adId + '"] .fb-carousel-prev', function() {
            var container = $(this).closest('.fb-carousel-container').find('.fb-carousel-items');
            container.scrollLeft(container.scrollLeft() - 200);
        });

        $(document).on('click', '[data-ad-id="' + adId + '"] .fb-carousel-next', function() {
            var container = $(this).closest('.fb-carousel-container').find('.fb-carousel-items');
            container.scrollLeft(container.scrollLeft() + 200);
        });
        @endif

        console.log('Click tracking set up for sponsored ad:', adId);
    }

    // Initialize when DOM is ready or jQuery is available
    if (typeof $ !== 'undefined') {
        $(document).ready(initializeSponsoredAdTracking);
    } else {
        // Wait for jQuery to load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSponsoredAdTracking();
        });
    }

})(); // End of IIFE

function trackSponsoredAdView(adId) {
    console.log('=== Tracking View for Sponsored Ad ===');
    console.log('Ad ID:', adId);
    console.log('CSRF Token:', '{{ csrf_token() }}');

    if (typeof $ === 'undefined') {
        console.error('jQuery not available for sponsored ad view tracking');
        return;
    }

    $.ajax({
        url: '/api/ads/track-view',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        beforeSend: function() {
            console.log('Sending view tracking request for sponsored ad:', adId);
        },
        success: function(response) {
            console.log('✅ View tracked successfully for sponsored ad:', adId);
            console.log('Response:', response);
        },
        error: function(xhr, status, error) {
            console.error('❌ Failed to track view for sponsored ad:', adId);
            console.error('Status:', status);
            console.error('Error:', error);
            console.error('Response:', xhr.responseText);
        }
    });
}

function trackSponsoredAdClick(adId) {
    console.log('=== Tracking Click for Sponsored Ad ===');
    console.log('Ad ID:', adId);
    console.log('CSRF Token:', '{{ csrf_token() }}');

    if (typeof $ === 'undefined') {
        console.error('jQuery not available for sponsored ad click tracking');
        return;
    }

    $.ajax({
        url: '/api/ads/track-click',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        beforeSend: function() {
            console.log('Sending click tracking request for sponsored ad:', adId);
        },
        success: function(response) {
            console.log('✅ Click tracked successfully for sponsored ad:', adId);
            console.log('Response:', response);
        },
        error: function(xhr, status, error) {
            console.error('❌ Failed to track click for sponsored ad:', adId);
            console.error('Status:', status);
            console.error('Error:', error);
            console.error('Response:', xhr.responseText);
        }
    });
}

function hideSponsoredAd(adId) {
    $('[data-ad-id="' + adId + '"]').fadeOut(300);

    $.ajax({
        url: '/api/ads/track-hide',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Hide tracked successfully for sponsored ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track hide for sponsored ad:', adId, error);
        }
    });
}

function reportSponsoredAd(adId) {
    if (confirm('Report this sponsored advertisement as inappropriate?')) {
        $.ajax({
            url: '/api/ads/report',
            method: 'POST',
            data: {
                ad_id: adId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                console.log('Report tracked successfully for sponsored ad:', adId);
                alert('Sponsored advertisement reported successfully.');
                $('[data-ad-id="' + adId + '"]').fadeOut(300);
            },
            error: function(xhr, status, error) {
                console.error('Failed to track report for sponsored ad:', adId, error);
                alert('Failed to report sponsored advertisement. Please try again.');
            }
        });
    }
}
</script>
@endpush
@endif
