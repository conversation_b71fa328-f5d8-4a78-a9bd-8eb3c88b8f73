{{-- Sponsored Advertisement Display Component --}}
@if($ad && $ad->is_active)
<div class="fb-sponsored-container" data-ad-id="{{ $ad->id }}" data-ad-type="sponsored" data-position="{{ $position ?? 'default' }}">
    <div class="fb-sponsored-ad fb-sponsored-{{ $ad->ad_format }} fb-sponsored-position-{{ $position ?? 'default' }}">
        <!-- Sponsored Ad Header -->
        <div class="fb-sponsored-header">
            <div class="fb-sponsored-label">
                <i class="fas fa-star"></i>
                <span>Sponsored</span>
                @if($ad->is_premium)
                    <span class="fb-premium-badge">
                        <i class="fas fa-crown"></i>
                        Premium
                    </span>
                @endif
            </div>
            <div class="fb-sponsored-actions">
                <button class="fb-sponsored-action" data-action="hide" data-ad-id="{{ $ad->id }}" title="Hide this ad">
                    <i class="fas fa-times"></i>
                </button>
                <button class="fb-sponsored-action" data-action="report" data-ad-id="{{ $ad->id }}" title="Report ad">
                    <i class="fas fa-flag"></i>
                </button>
            </div>
        </div>

        <!-- Sponsored Ad Content -->
        <div class="fb-sponsored-content">
            @if($ad->ad_format === 'native')
                <!-- Native Sponsored Ad -->
                <div class="fb-native-sponsored">
                    <div class="fb-native-header">
                        @if($ad->sponsor_logo)
                            <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-logo">
                        @endif
                        <div class="fb-sponsor-info">
                            <h6 class="fb-sponsor-name">{{ $ad->sponsor_name }}</h6>
                            <span class="fb-sponsor-verified">
                                <i class="fas fa-check-circle"></i>
                                Verified Sponsor
                            </span>
                        </div>
                    </div>
                    
                    @if($ad->image)
                        <div class="fb-native-image">
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                        </div>
                    @endif
                    
                    <div class="fb-native-text">
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        <p class="fb-sponsored-description">{{ Str::limit($ad->content, 150) }}</p>
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-native-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-external-link-alt"></i>
                                Learn More
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'display')
                <!-- Display Banner Sponsored Ad -->
                <div class="fb-display-sponsored">
                    @if($ad->image)
                        <div class="fb-display-image">
                            <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                            @if($ad->link_url)
                                <div class="fb-display-overlay">
                                    <a href="{{ $ad->link_url }}" class="fb-display-link" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                    
                    <div class="fb-display-content">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        @if($ad->content)
                            <p class="fb-sponsored-description">{{ Str::limit($ad->content, 100) }}</p>
                        @endif
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-display-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                Visit Now
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'video')
                <!-- Video Sponsored Ad -->
                <div class="fb-video-sponsored">
                    <div class="fb-video-container">
                        @if($ad->video_url)
                            <video class="fb-sponsored-video" poster="{{ $ad->image ? asset($ad->image) : '' }}" controls>
                                <source src="{{ $ad->video_url }}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        @elseif($ad->image)
                            <div class="fb-video-placeholder">
                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-sponsored-image">
                                <div class="fb-video-play-overlay">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <div class="fb-video-info">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                        @if($ad->content)
                            <p class="fb-sponsored-description">{{ Str::limit($ad->content, 120) }}</p>
                        @endif
                        
                        @if($ad->link_url)
                            <a href="{{ $ad->link_url }}" class="fb-video-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-play"></i>
                                Watch Now
                            </a>
                        @endif
                    </div>
                </div>

            @elseif($ad->ad_format === 'carousel')
                <!-- Carousel Sponsored Ad -->
                <div class="fb-carousel-sponsored">
                    <div class="fb-carousel-header">
                        <div class="fb-sponsor-branding">
                            @if($ad->sponsor_logo)
                                <img src="{{ asset($ad->sponsor_logo) }}" alt="{{ $ad->sponsor_name }}" class="fb-sponsor-mini-logo">
                            @endif
                            <span class="fb-sponsor-name">{{ $ad->sponsor_name }}</span>
                        </div>
                        <h4 class="fb-sponsored-title">{{ $ad->title }}</h4>
                    </div>
                    
                    <div class="fb-carousel-container">
                        <div class="fb-carousel-items">
                            @if($ad->image)
                                <div class="fb-carousel-item">
                                    <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" class="fb-carousel-image">
                                    <div class="fb-carousel-text">
                                        <h5>{{ $ad->title }}</h5>
                                        <p>{{ Str::limit($ad->content, 80) }}</p>
                                    </div>
                                </div>
                            @endif
                            
                            <!-- Additional carousel items can be added here -->
                            <div class="fb-carousel-item">
                                <div class="fb-carousel-placeholder">
                                    <i class="fas fa-image"></i>
                                    <span>More Items</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fb-carousel-controls">
                            <button class="fb-carousel-prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="fb-carousel-next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    
                    @if($ad->link_url)
                        <div class="fb-carousel-footer">
                            <a href="{{ $ad->link_url }}" class="fb-carousel-cta" target="_blank" rel="noopener" data-ad-id="{{ $ad->id }}">
                                <i class="fas fa-shopping-cart"></i>
                                Shop Now
                            </a>
                        </div>
                    @endif
                </div>

            @endif
        </div>

        <!-- Sponsored Ad Footer (Hidden on Frontend) -->
        <div class="fb-sponsored-footer d-none">
            <div class="fb-sponsored-performance">
                <span class="fb-sponsored-views">{{ number_format($ad->view_count ?? 0) }} views</span>
                <span class="fb-sponsored-clicks">{{ number_format($ad->click_count ?? 0) }} clicks</span>
                @if($ad->budget)
                    <span class="fb-sponsored-budget">Budget: ${{ number_format($ad->budget, 2) }}</span>
                @endif
            </div>
            <div class="fb-sponsored-meta">
                <span class="fb-sponsored-format">{{ ucfirst($ad->ad_format) }} Ad</span>
                <span class="fb-sponsored-placement">{{ ucfirst($ad->placement) }}</span>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Sponsored advertisement interaction tracking
$(document).ready(function() {
    var adId = {{ $ad->id }};
    var adContainer = $('[data-ad-id="' + adId + '"][data-ad-type="sponsored"]');

    console.log('Initializing tracking for sponsored ad:', adId);

    // Track sponsored ad view using Intersection Observer for accurate tracking
    if (typeof IntersectionObserver !== 'undefined') {
        var observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                    trackSponsoredAdView(adId);
                    observer.unobserve(entry.target); // Track view only once
                }
            });
        }, { threshold: 0.5 });

        if (adContainer.length > 0) {
            observer.observe(adContainer[0]);
        }
    } else {
        // Fallback for older browsers
        trackSponsoredAdView(adId);
    }

    // Track sponsored ad clicks
    adContainer.on('click', 'a[href]', function(e) {
        console.log('Sponsored ad click tracked:', adId);
        trackSponsoredAdClick(adId);
    });

    // Handle sponsored ad actions (hide, report)
    adContainer.on('click', '.fb-sponsored-action', function(e) {
        e.preventDefault();
        var action = $(this).data('action');

        console.log('Sponsored ad action:', action, 'for ad:', adId);

        if (action === 'hide') {
            hideSponsoredAd(adId);
        } else if (action === 'report') {
            reportSponsoredAd(adId);
        }
    });

    // Handle carousel controls
    @if($ad->ad_format === 'carousel')
    adContainer.find('.fb-carousel-prev').on('click', function() {
        var container = $(this).closest('.fb-carousel-container').find('.fb-carousel-items');
        container.scrollLeft(container.scrollLeft() - 200);
    });

    adContainer.find('.fb-carousel-next').on('click', function() {
        var container = $(this).closest('.fb-carousel-container').find('.fb-carousel-items');
        container.scrollLeft(container.scrollLeft() + 200);
    });
    @endif
});

function trackSponsoredAdView(adId) {
    console.log('Tracking view for sponsored ad:', adId);

    $.ajax({
        url: '/api/ads/track-view',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('View tracked successfully for sponsored ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track view for sponsored ad:', adId, error);
        }
    });
}

function trackSponsoredAdClick(adId) {
    console.log('Tracking click for sponsored ad:', adId);

    $.ajax({
        url: '/api/ads/track-click',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Click tracked successfully for sponsored ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track click for sponsored ad:', adId, error);
        }
    });
}

function hideSponsoredAd(adId) {
    $('[data-ad-id="' + adId + '"]').fadeOut(300);

    $.ajax({
        url: '/api/ads/track-hide',
        method: 'POST',
        data: {
            ad_id: adId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Hide tracked successfully for sponsored ad:', adId);
        },
        error: function(xhr, status, error) {
            console.error('Failed to track hide for sponsored ad:', adId, error);
        }
    });
}

function reportSponsoredAd(adId) {
    if (confirm('Report this sponsored advertisement as inappropriate?')) {
        $.ajax({
            url: '/api/ads/report',
            method: 'POST',
            data: {
                ad_id: adId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                console.log('Report tracked successfully for sponsored ad:', adId);
                alert('Sponsored advertisement reported successfully.');
                $('[data-ad-id="' + adId + '"]').fadeOut(300);
            },
            error: function(xhr, status, error) {
                console.error('Failed to track report for sponsored ad:', adId, error);
                alert('Failed to report sponsored advertisement. Please try again.');
            }
        });
    }
}
</script>
@endpush
@endif
