<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use DB;

class RoleController extends Controller
{
    public function AllPermission(){

        $permissions = Permission::all();
        return view('backend.pages.permission.all_permission',compact('permissions'));

    } // End Method 

    public function AddPermission(){
        // Get existing group names from permissions
        $existingGroups = Permission::select('group_name')
            ->whereNotNull('group_name')
            ->distinct()
            ->orderBy('group_name')
            ->pluck('group_name');

        return view('backend.pages.permission.add_permission', compact('existingGroups'));
    }// End Method


    public function StorePermission(Request $request){

        $role = Permission::create([
            'name' => $request->name,
            'group_name' => $request->group_name,
        ]);

        $notification = array(
            'message' => 'Permission Inserted Successfully',
            'alert-type' => 'success'

        );
        return redirect()->route('all.permission')->with($notification);  

    }// End Method 


    public function EditPermission($id){

        $permission = Permission::findOrFail($id);
        return view('backend.pages.permission.edit_permission',compact('permission'));

    }// End Method 


     public function UpdatePermission(Request $request){

        $per_id = $request->id;

         Permission::findOrFail($per_id)->update([    
            'name' => $request->name,
            'group_name' => $request->group_name,
        ]);

        $notification = array(
            'message' => 'Permission Updated Successfully',
            'alert-type' => 'success'

        );
        return redirect()->route('all.permission')->with($notification);  

    }// End Method 


    public function DeletePermission($id){

        Permission::findOrFail($id)->delete();

           $notification = array(
            'message' => 'Permission Deleted Successfully',
            'alert-type' => 'success'

        );
        return redirect()->back()->with($notification); 

    }// End Method 


     ///////////////// All Roles Method ////////////////



    public function AllRoles(){

        $roles = Role::all();
        return view('backend.pages.roles.all_roles',compact('roles'));

    } // End Method 


    public function AddRoles(){
        return view('backend.pages.roles.add_roles');
    }// End Method 


public function StoreRoles(Request $request){

        $role = Role::create([
            'name' => $request->name, 
        ]);

        $notification = array(
            'message' => 'Role Inserted Successfully',
            'alert-type' => 'success'

        );
        return redirect()->route('all.roles')->with($notification);  

    }// End Method 


    public function EditRoles($id){

        $roles = Role::findOrFail($id);
        return view('backend.pages.roles.edit_roles',compact('roles'));

    }// End Method


    public function UpdateRoles(Request $request){

        $role_id = $request->id;

         Role::findOrFail($role_id)->update([
            'name' => $request->name, 
        ]);

        $notification = array(
            'message' => 'Role Updated Successfully',
            'alert-type' => 'success'

        );
        return redirect()->route('all.roles')->with($notification);  

    }// End Method

    public function DeleteRoles($id){

    Role::findOrFail($id)->delete();

        $notification = array(
            'message' => 'Role Deleted Successfully',
            'alert-type' => 'success'

        );
        return redirect()->back()->with($notification);  

    }// End Method

    //// ///////// Add Role For Permission ////////////////


    public function AddRolesPermission(){

        $roles = Role::all();
        $permissions = Permission::all();
        $permission_groups = User::getpermissionGroups();
        return view('backend.pages.roles.add_roles_permission',compact('roles','permissions','permission_groups'));

    }// End Method


    public function RolePermisssionStore(Request $request){

        try {
            $request->validate([
                'role_id' => 'required|exists:roles,id',
                'permission' => 'array',
                'permission.*' => 'exists:permissions,id'
            ]);

            $role = Role::findOrFail($request->role_id);
            $permissionIds = $request->permission ?? [];

            if (!empty($permissionIds)) {
                // Convert permission IDs to permission names
                $permissionNames = Permission::whereIn('id', $permissionIds)->pluck('name')->toArray();

                // Sync permissions using permission names (this prevents duplicates)
                $role->syncPermissions($permissionNames);
            } else {
                // If no permissions selected, remove all permissions
                $role->syncPermissions([]);
            }

            $notification = array(
                'message' => 'Role Permission Assigned Successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('all.roles.permission')->with($notification);

        } catch (\Exception $e) {
            $notification = array(
                'message' => 'Error assigning role permissions: ' . $e->getMessage(),
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification)->withInput();
        }

    }// End Method


    public function AllRolesPermission(){

        $roles = Role::all();
        return view('backend.pages.roles.all_roles_permission',compact('roles'));

    }// End Method


    public function AdminEditRoles($id){

        $role = Role::findOrFail($id);
        $permissions = Permission::all();
        $permission_groups = User::getpermissionGroups();
        return view('backend.pages.roles.role_permission_edit',compact('role','permissions','permission_groups'));

    }// End Method


    public function RolePermissionUpdate(Request $request, $id){

        try {
            $role = Role::findOrFail($id);
            $permissionIds = $request->permission;

            if (!empty($permissionIds)) {
                // Convert permission IDs to permission names
                $permissionNames = Permission::whereIn('id', $permissionIds)->pluck('name')->toArray();

                // Sync permissions using permission names
                $role->syncPermissions($permissionNames);
            } else {
                // If no permissions selected, remove all permissions
                $role->syncPermissions([]);
            }

            $notification = array(
                'message' => 'Role Permission Updated Successfully',
                'alert-type' => 'success'
            );
            return redirect()->route('all.roles.permission')->with($notification);

        } catch (\Exception $e) {
            $notification = array(
                'message' => 'Error updating role permissions: ' . $e->getMessage(),
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }

    }// End Method


    public function AdminDeleteRoles($id){
        $role = Role::findOrFail($id);
        if (!is_null($role)) {
           $role->delete();
        }

         $notification = array(
            'message' => 'Role Permission Deleted Successfully',
            'alert-type' => 'success'

        );
        return redirect()->back()->with($notification); 

    }// End Method

    /**
     * Get current permissions for a role (AJAX)
     */
    public function getRolePermissions($roleId)
    {
        try {
            $role = Role::findOrFail($roleId);
            $permissions = $role->permissions()->pluck('id')->toArray();

            return response()->json([
                'success' => true,
                'permissions' => $permissions,
                'role_name' => $role->name
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading permissions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Modern Role Management Dashboard
     */
    public function modernRoleManagement()
    {
        $roles = Role::with('permissions')->get();
        $permissions = Permission::all();
        $permission_groups = User::getpermissionGroups();
        $users_with_roles = User::whereHas('roles')->count();

        return view('backend.pages.roles.modern_role_management', compact(
            'roles',
            'permissions',
            'permission_groups',
            'users_with_roles'
        ));
    }

}
 