@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="{{ route('admin.advertisements.create') }}" class="btn btn-primary">
                                <i class="mdi mdi-plus"></i> Add Advertisement
                            </a>
                        </ol>
                    </div>
                    <h4 class="page-title">Advertisement Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-advertisement text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['total'] }}</h5>
                                <p class="text-muted mb-0">Total Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['active'] }}</h5>
                                <p class="text-muted mb-0">Active Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-image text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['banner'] }}</h5>
                                <p class="text-muted mb-0">Banner Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-view-column text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $stats['sidebar'] }}</h5>
                                <p class="text-muted mb-0">Sidebar Ads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Advertisements</h4>
                        <p class="card-title-desc">Drag and drop to reorder advertisements, or use the action buttons to manage ads.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchAds" placeholder="Search advertisements...">
                                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="filterType">
                                    <option value="">All Types</option>
                                    <option value="banner">Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="adsTable">
                                <thead>
                                    <tr>
                                        <th width="50">Order</th>
                                        <th>Advertisement</th>
                                        <th>Type</th>
                                        <th>Position</th>
                                        <th>Status</th>
                                        <th>Performance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-ads">
                                    @foreach($advertisements as $ad)
                                    <tr data-ad-id="{{ $ad->id }}" data-order="{{ $ad->display_order }}" 
                                        class="ad-row {{ $ad->is_active ? '' : 'table-secondary' }}">
                                        <td>
                                            <span class="badge bg-secondary order-badge">{{ $ad->display_order }}</span>
                                            <div class="drag-handle-container ms-2 d-inline-block">
                                                <i class="mdi mdi-drag-vertical text-muted" style="cursor: move;"></i>
                                                <div class="drag-tooltip">Drag to reorder</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($ad->image)
                                                <img src="{{ asset($ad->image) }}" alt="{{ $ad->title }}" 
                                                     class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                                @endif
                                                <div>
                                                    <h6 class="mb-1">{{ Str::limit($ad->title, 40) }}</h6>
                                                    <small class="text-muted">{{ $ad->created_at->format('M d, Y') }}</small>
                                                    @if($ad->link_url)
                                                        <br><small class="text-info">{{ Str::limit($ad->link_url, 30) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ ucfirst($ad->ad_type) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($ad->position) }}</span>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input status-toggle" type="checkbox" 
                                                       data-ad-id="{{ $ad->id }}" {{ $ad->is_active ? 'checked' : '' }}>
                                                <label class="form-check-label">
                                                    {{ $ad->is_active ? 'Active' : 'Inactive' }}
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column performance-metrics" data-ad-id="{{ $ad->id }}" data-ad-type="advertisement">
                                                <small><strong>Views:</strong> <span class="view-count">{{ number_format($ad->view_count) }}</span></small>
                                                <small><strong>Clicks:</strong> <span class="click-count">{{ number_format($ad->click_count) }}</span></small>
                                                @if($ad->view_count > 0)
                                                <small><strong>CTR:</strong> <span class="ctr-rate">{{ round(($ad->click_count / $ad->view_count) * 100, 2) }}%</span></small>
                                                @endif
                                                @php
                                                    $performance = $ad->getPerformanceMetrics();
                                                    $statusClass = match($performance['status']) {
                                                        'excellent' => 'success',
                                                        'good' => 'primary',
                                                        'average' => 'warning',
                                                        'poor' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                @endphp
                                                <small>
                                                    <span class="badge bg-{{ $statusClass }} performance-badge">
                                                        Score: <span class="performance-score">{{ $performance['performance_score'] }}</span>
                                                    </span>
                                                </small>
                                                <div class="performance-refresh mt-1">
                                                    <button class="btn btn-xs btn-outline-secondary refresh-performance" data-ad-id="{{ $ad->id }}" data-ad-type="advertisement">
                                                        <i class="mdi mdi-refresh"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- View Button -->
                                                <a href="{{ route('admin.advertisements.show', $ad->id) }}" class="btn btn-sm btn-outline-primary" title="View">
                                                    <i class="mdi mdi-eye"></i>
                                                </a>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('admin.advertisements.edit', $ad->id) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>

                                                <!-- Status Toggle Button -->
                                                @if($ad->is_active)
                                                    <button class="btn btn-sm btn-outline-secondary action-toggle"
                                                            data-ad-id="{{ $ad->id }}" data-action="deactivate" title="Deactivate">
                                                        <i class="mdi mdi-pause"></i>
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-outline-success action-toggle"
                                                            data-ad-id="{{ $ad->id }}" data-action="activate" title="Activate">
                                                        <i class="mdi mdi-play"></i>
                                                    </button>
                                                @endif

                                                <!-- Delete Button -->
                                                <form method="POST" action="{{ route('admin.advertisements.destroy', $ad->id) }}" class="d-inline" 
                                                      onsubmit="return confirm('Are you sure you want to delete this advertisement?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                Showing {{ $advertisements->firstItem() }} to {{ $advertisements->lastItem() }} of {{ $advertisements->total() }} advertisements
                            </div>
                            <div>
                                {{ $advertisements->links() }}
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Advertisement Management Specific Styles */
.ad-row {
    transition: all 0.2s ease;
    cursor: default;
}

.ad-row:hover {
    background-color: #f8f9fa !important;
}

.ad-row.ui-sortable-helper {
    background-color: #fff !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
    border: 2px solid #007bff !important;
    opacity: 0.9 !important;
    transform: rotate(1deg);
    z-index: 1000 !important;
}

.ad-row.table-secondary {
    opacity: 0.6;
    background-color: #f8f9fa !important;
}

.ui-state-highlight {
    height: 60px !important;
    background-color: #e3f2fd !important;
    border: 2px dashed #2196f3 !important;
    border-radius: 4px;
    margin: 2px 0;
}

.order-badge {
    min-width: 35px;
    display: inline-block;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.order-badge.updating {
    background-color: #ffc107 !important;
    color: #000 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

#sortable-ads .mdi-drag-vertical {
    opacity: 0.3;
    transition: all 0.2s ease;
    cursor: grab;
    font-size: 18px;
    color: #6c757d;
}

#sortable-ads .mdi-drag-vertical:active {
    cursor: grabbing;
}

#sortable-ads tr:hover .mdi-drag-vertical {
    opacity: 1;
    color: #007bff;
}

.drag-handle-container {
    position: relative;
    display: inline-block;
}

.drag-tooltip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
    z-index: 1000;
}

.drag-handle-container:hover .drag-tooltip {
    opacity: 1;
}

/* Performance metrics styling */
.performance-updated {
    background-color: #d4edda !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}

/* Search and filter styling */
.search-filter-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group:last-child {
    margin-bottom: 0;
}
</style>

<script>
// Wait for core libraries to be ready
$(document).ready(function() {
    // Check if jQuery is available
    if (typeof $ === 'undefined' || typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded! Advertisement management cannot initialize.');

        // Try to wait for jQuery to load
        var checkJQuery = function() {
            if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
                console.log('jQuery loaded, initializing advertisement management...');
                initializeAdvertisementManagement();
            } else {
                console.warn('Still waiting for jQuery...');
                setTimeout(checkJQuery, 500);
            }
        };

        setTimeout(checkJQuery, 1000);
        return;
    }

    console.log('=== Advertisement Management Initialization ===');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');
    console.log('Toastr available:', typeof toastr !== 'undefined');

    // Initialize immediately if jQuery is available
    initializeAdvertisementManagement();
});

// Also listen for the custom event from master template
$(document).on('coreLibrariesReady', function() {
    console.log('Core libraries ready event received');
    if (typeof window.advertisementManagementInitialized === 'undefined') {
        window.advertisementManagementInitialized = true;
        initializeAdvertisementManagement();
    }
});

function initializeAdvertisementManagement() {
        console.log('Initializing Advertisement Management...');

        // Initialize toastr if available
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "3000",
                "preventDuplicates": true
            };
        }

        try {
            // Initialize search functionality
            initializeSearchFilters();

            // Initialize drag and drop functionality
            initializeAdvertisementSortable();

            // Initialize status toggles
            initializeStatusToggles();

            // Initialize performance tracking
            initializePerformanceTracking();

            console.log('Advertisement Management initialized successfully');
        } catch (error) {
            console.error('Error initializing Advertisement Management:', error);
        }
    }

    // Search and Filter Functionality
    function initializeSearchFilters() {
        console.log('Initializing search filters...');

        // Real-time search
        $('#searchAds').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            console.log('Searching for:', searchTerm);

            filterAdvertisements();
        });

        // Status filter
        $('#filterStatus').on('change', function() {
            var status = $(this).val();
            console.log('Filtering by status:', status);

            filterAdvertisements();
        });

        // Type filter
        $('#filterType').on('change', function() {
            var type = $(this).val();
            console.log('Filtering by type:', type);

            filterAdvertisements();
        });

        // Combined filter function
        function filterAdvertisements() {
            var searchTerm = $('#searchAds').val().toLowerCase();
            var statusFilter = $('#filterStatus').val();
            var typeFilter = $('#filterType').val();

            console.log('Filtering with:', { searchTerm, statusFilter, typeFilter });

            var visibleCount = 0;

            $('#sortable-ads tr').each(function() {
                var $row = $(this);

                // Get text content from different columns
                var title = $row.find('td').eq(1).find('h6').text().toLowerCase(); // Title in second column
                var url = $row.find('td').eq(1).find('.text-info').text().toLowerCase(); // URL in second column
                var typeText = $row.find('td').eq(2).text().toLowerCase(); // Type in third column
                var isActive = !$row.hasClass('table-secondary');

                console.log('Row data:', { title, url, typeText, isActive });

                // Search match - search in title, URL, and type
                var searchMatch = searchTerm === '' ||
                                title.includes(searchTerm) ||
                                url.includes(searchTerm) ||
                                typeText.includes(searchTerm);

                // Status match
                var statusMatch = statusFilter === '' ||
                                (statusFilter === 'active' && isActive) ||
                                (statusFilter === 'inactive' && !isActive);

                // Type match
                var typeMatch = typeFilter === '' || typeText.includes(typeFilter.toLowerCase());

                if (searchMatch && statusMatch && typeMatch) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            console.log('Visible rows:', visibleCount);

            // Update results count
            updateResultsCount(visibleCount);
        }

        function updateResultsCount(count) {
            var $resultsInfo = $('.results-info');
            if ($resultsInfo.length === 0) {
                // Add results info if it doesn't exist
                $('#adsTable').before('<div class="results-info mb-2"><small class="text-muted">Showing <span class="results-count">' + count + '</span> advertisements</small></div>');
            } else {
                $resultsInfo.find('.results-count').text(count);
            }
        }
    }

    // Drag and Drop Functionality
    function initializeAdvertisementSortable() {
        console.log('Initializing advertisement sortable...');

        var $table = $("#sortable-ads");
        console.log('Advertisement table found:', $table.length);

        if ($table.length === 0) {
            console.error('Advertisement sortable table not found');
            return;
        }

        // Check if jQuery UI is available
        if (typeof $.ui === 'undefined' || typeof $.ui.sortable === 'undefined') {
            console.error('jQuery UI sortable not available');
            // Try to load jQuery UI dynamically
            var script = document.createElement('script');
            script.src = 'https://code.jquery.com/ui/1.13.2/jquery-ui.min.js';
            script.onload = function() {
                console.log('jQuery UI loaded dynamically');
                setTimeout(function() {
                    initializeAdvertisementSortable();
                }, 100);
            };
            document.head.appendChild(script);
            return;
        }

        try {
            // Destroy existing sortable if it exists
            if ($table.hasClass('ui-sortable')) {
                $table.sortable('destroy');
                console.log('Destroyed existing sortable');
            }

            $table.sortable({
                handle: ".mdi-drag-vertical",
                items: "> tr:visible", // Only sort visible rows
                axis: "y",
                cursor: "grabbing",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                opacity: 0.8,
                distance: 5, // Minimum distance to start dragging
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Advertisement drag started');
                    ui.placeholder.height(ui.item.height());
                    ui.item.addClass('dragging');
                    $('body').addClass('dragging-active');
                },
                update: function(event, ui) {
                    console.log('Advertisement order updated');
                    updateAdOrder();
                },
                stop: function(event, ui) {
                    console.log('Advertisement drag stopped');
                    ui.item.removeClass('dragging');
                    $('body').removeClass('dragging-active');
                }
            });

            console.log('Advertisement sortable initialized successfully');

            // Add drag handle tooltips
            $('#sortable-ads .mdi-drag-vertical').each(function() {
                if (!$(this).parent().find('.drag-tooltip').length) {
                    $(this).wrap('<div class="drag-handle-container"></div>');
                    $(this).parent().append('<div class="drag-tooltip">Drag to reorder</div>');
                }
            });

        } catch (error) {
            console.error('Advertisement sortable initialization error:', error);
            // Fallback: show error message
            toastr.error('Drag and drop functionality failed to initialize. Please refresh the page.');
        }
    }

    // Status Toggle Functionality
    function initializeStatusToggles() {
        console.log('Initializing status toggles...');

        // Handle status toggle switches
        $(document).on('change', '.status-toggle', function() {
            var $toggle = $(this);
            var adId = $toggle.data('ad-id');
            var isActive = $toggle.is(':checked');

            console.log('Toggling status for ad:', adId, 'to:', isActive);

            // Disable toggle during request
            $toggle.prop('disabled', true);

            $.ajax({
                url: '/admin/advertisements/' + adId + '/toggle-status',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    is_active: isActive
                },
                success: function(response) {
                    console.log('Status toggle response:', response);

                    if (response.success) {
                        // Update row appearance
                        var $row = $toggle.closest('tr');
                        if (isActive) {
                            $row.removeClass('table-secondary');
                            toastr.success('Advertisement activated successfully!');
                        } else {
                            $row.addClass('table-secondary');
                            toastr.success('Advertisement deactivated successfully!');
                        }

                        // Update stats if visible
                        updateStatsDisplay();

                    } else {
                        // Revert toggle on failure
                        $toggle.prop('checked', !isActive);
                        toastr.error(response.message || 'Failed to update status');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Status toggle error:', xhr.responseText);

                    // Revert toggle on error
                    $toggle.prop('checked', !isActive);

                    var errorMessage = 'Failed to update advertisement status';
                    try {
                        var response = JSON.parse(xhr.responseText);
                        errorMessage = response.message || errorMessage;
                    } catch (e) {
                        errorMessage += ': ' + error;
                    }

                    toastr.error(errorMessage);
                },
                complete: function() {
                    // Re-enable toggle
                    $toggle.prop('disabled', false);
                }
            });
        });

        // Handle action buttons (activate/deactivate)
        $(document).on('click', '.action-toggle', function(e) {
            e.preventDefault();

            var $button = $(this);
            var adId = $button.data('ad-id');
            var action = $button.data('action'); // 'activate' or 'deactivate'
            var isActivating = action === 'activate';

            console.log('Action button clicked:', action, 'for ad:', adId);

            // Disable button during request
            $button.prop('disabled', true);

            $.ajax({
                url: '/admin/advertisements/' + adId + '/toggle-status',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    is_active: isActivating
                },
                success: function(response) {
                    console.log('Action toggle response:', response);

                    if (response.success) {
                        // Update row and toggle
                        var $row = $button.closest('tr');
                        var $toggle = $row.find('.status-toggle');

                        if (isActivating) {
                            $row.removeClass('table-secondary');
                            $toggle.prop('checked', true);
                            $button.removeClass('btn-success').addClass('btn-warning')
                                   .data('action', 'deactivate')
                                   .html('<i class="mdi mdi-pause"></i> Deactivate');
                            toastr.success('Advertisement activated successfully!');
                        } else {
                            $row.addClass('table-secondary');
                            $toggle.prop('checked', false);
                            $button.removeClass('btn-warning').addClass('btn-success')
                                   .data('action', 'activate')
                                   .html('<i class="mdi mdi-play"></i> Activate');
                            toastr.success('Advertisement deactivated successfully!');
                        }

                        // Update stats
                        updateStatsDisplay();

                    } else {
                        toastr.error(response.message || 'Failed to update status');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Action toggle error:', xhr.responseText);

                    var errorMessage = 'Failed to update advertisement status';
                    try {
                        var response = JSON.parse(xhr.responseText);
                        errorMessage = response.message || errorMessage;
                    } catch (e) {
                        errorMessage += ': ' + error;
                    }

                    toastr.error(errorMessage);
                },
                complete: function() {
                    // Re-enable button
                    $button.prop('disabled', false);
                }
            });
        });
    }

    function updateStatsDisplay() {
        // Update the stats cards at the top
        var totalAds = $('#sortable-ads tr').length;
        var activeAds = $('#sortable-ads tr:not(.table-secondary)').length;
        var inactiveAds = totalAds - activeAds;

        // Update stats if elements exist
        $('.stats-total').text(totalAds);
        $('.stats-active').text(activeAds);
        $('.stats-inactive').text(inactiveAds);
    }

    // Update advertisement order after drag and drop
    function updateAdOrder() {
        var ads = [];
        var hasChanges = false;

        $("#sortable-ads tr").each(function(index) {
            var adId = $(this).data('ad-id');
            var currentOrder = $(this).data('order');

            if (adId) {
                ads.push({
                    id: adId,
                    order: index
                });

                // Check if order actually changed
                if (currentOrder !== index) {
                    hasChanges = true;
                }

                // Update the order badge with new index
                $(this).find('.order-badge').text(index);
                $(this).data('order', index); // Update data attribute
            }
        });

        if (!hasChanges) {
            console.log('No order changes detected');
            return;
        }

        console.log('Updating order for advertisements:', ads);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route("admin.advertisements.update-order") }}',
            method: 'POST',
            data: {
                advertisements: ads,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                $('.order-badge').addClass('updating');
                $('#sortable-ads').addClass('updating');
            },
            success: function(response) {
                console.log('Order update response:', response);

                if (response.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message || 'Advertisement order updated successfully!');
                    }

                    // Update visual feedback
                    $('.order-badge').removeClass('updating');
                    $('#sortable-ads').removeClass('updating');

                    // Flash success indication
                    $('.order-badge').addClass('bg-success').removeClass('bg-secondary');
                    setTimeout(function() {
                        $('.order-badge').addClass('bg-secondary').removeClass('bg-success');
                    }, 1000);
                } else {
                    throw new Error(response.message || 'Unknown error occurred');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);

                var errorMessage = 'Failed to update advertisement order';
                try {
                    var response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                } else {
                    alert(errorMessage);
                }

                // Reset visual state
                $('.order-badge').removeClass('updating');
                $('#sortable-ads').removeClass('updating');

                // Reload to restore original order
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        });
    }

    // Status toggle functionality
    $(document).on('change', '.status-toggle', function() {
        var adId = $(this).data('ad-id');
        var isActive = $(this).is(':checked');
        var label = $(this).next('label');
        var toggle = $(this);

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $.ajax({
            url: '{{ route("admin.advertisements.toggle-status", ":id") }}'.replace(':id', adId),
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                toggle.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    label.text(response.is_active ? 'Active' : 'Inactive');
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                }
                toggle.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                console.error('Status toggle error:', xhr.responseText);
                // Revert the toggle
                toggle.prop('checked', !isActive);
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to toggle advertisement status');
                } else {
                    alert('Failed to toggle advertisement status');
                }
                toggle.prop('disabled', false);
            }
        });
    });

    // Search functionality
    $('#searchAds').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        $('#adsTable tbody tr').each(function() {
            var title = $(this).find('h6').text().toLowerCase();
            var url = $(this).find('.text-info').text().toLowerCase();
            
            if (title.includes(searchTerm) || url.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Filter functionality
    $('#filterType, #filterStatus').change(function() {
        var typeFilter = $('#filterType').val();
        var statusFilter = $('#filterStatus').val();
        
        $('#adsTable tbody tr').each(function() {
            var show = true;
            
            if (typeFilter) {
                var adType = $(this).find('.badge.bg-primary').text().toLowerCase();
                show = show && adType.includes(typeFilter);
            }
            
            if (statusFilter) {
                var isActive = $(this).find('.status-toggle').is(':checked');
                if (statusFilter === 'active') {
                    show = show && isActive;
                } else if (statusFilter === 'inactive') {
                    show = show && !isActive;
                }
            }
            
            if (show) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Real-time performance tracking
    initializePerformanceTracking();

    function initializePerformanceTracking() {
        // Auto-refresh performance every 30 seconds
        setInterval(function() {
            refreshAllPerformanceMetrics();
        }, 30000);

        // Manual refresh button click
        $(document).on('click', '.refresh-performance', function() {
            var adId = $(this).data('ad-id');
            var adType = $(this).data('ad-type');
            refreshPerformanceMetrics(adId, adType);
        });
    }

    function refreshAllPerformanceMetrics() {
        $('.performance-metrics').each(function() {
            var adId = $(this).data('ad-id');
            var adType = $(this).data('ad-type');
            refreshPerformanceMetrics(adId, adType);
        });
    }

    function refreshPerformanceMetrics(adId, adType) {
        var endpoint = adType === 'advertisement' ?
            '/api/ads/performance/' + adId :
            '/api/sponsored-ads/performance/' + adId;

        $.ajax({
            url: endpoint,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    updatePerformanceDisplay(adId, response.performance);
                }
            },
            error: function(xhr, status, error) {
                console.error('Performance refresh error:', error);
            }
        });
    }

    function updatePerformanceDisplay(adId, performance) {
        var container = $('.performance-metrics[data-ad-id="' + adId + '"]');

        // Update metrics
        container.find('.view-count').text(performance.views.toLocaleString());
        container.find('.click-count').text(performance.clicks.toLocaleString());
        container.find('.ctr-rate').text(performance.ctr + '%');
        container.find('.performance-score').text(performance.performance_score);

        // Update badge color based on status
        var badge = container.find('.performance-badge');
        badge.removeClass('bg-success bg-primary bg-warning bg-danger bg-secondary');

        var statusClass = getStatusClass(performance.status);
        badge.addClass('bg-' + statusClass);

        // Add flash effect
        container.addClass('performance-updated');
        setTimeout(function() {
            container.removeClass('performance-updated');
        }, 1000);
    }

    function getStatusClass(status) {
        switch(status) {
            case 'excellent': return 'success';
            case 'good': return 'primary';
            case 'average': return 'warning';
            case 'poor': return 'danger';
            default: return 'secondary';
        }
    }
} // End of main if statement
</script>

<!-- Add CSS for performance updates -->
<style>
.performance-updated {
    background-color: #e8f5e8 !important;
    transition: background-color 0.5s ease;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.refresh-performance {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.refresh-performance:hover {
    opacity: 1;
}
</style>

@endsection
