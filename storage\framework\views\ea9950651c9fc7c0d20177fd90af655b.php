<?php $__env->startSection('title'); ?>
<?php echo e($category->category_name); ?> - <PERSON><PERSON><PERSON>oth<PERSON> (Facebook Style)
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                <?php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                ?>
                <img src="<?php echo e($logoPath); ?>" alt="<?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?>">
            </div>
            <div class="fb-profile-info">
                <h3 class="fb-profile-name">NitiKotha</h3>
                <p class="fb-profile-tagline">Premium News Portal</p>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="fb-nav-menu">
            <a href="<?php echo e(url('/v2')); ?>" class="fb-nav-item">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="#" class="fb-nav-item active">
                <i class="fas fa-fire"></i>
                <span><?php echo e($category->category_name); ?></span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                <?php if(isset($categories)): ?>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category <?php echo e($cat->id == $category->id ? 'active' : ''); ?>" data-category-id="<?php echo e($cat->id); ?>">
                            <div class="fb-category-left">
                                <div class="fb-category-icon"><?php echo e($cat->category_icon); ?></div>
                                <span class="fb-category-name"><?php echo e($cat->category_name); ?></span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count"><?php echo e($cat->news_posts_count ?? 0); ?></span>
                                <?php if($cat->subcategories && $cat->subcategories->count() > 0): ?>
                                    <button class="fb-category-toggle" data-target="subcats-<?php echo e($cat->id); ?>">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="<?php echo e(url('v2/news/category/'.$cat->id.'/'.$cat->category_slug)); ?>" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        <?php if($cat->subcategories && $cat->subcategories->count() > 0): ?>
                        <div class="fb-subcategories <?php echo e($cat->id == $category->id ? 'expanded' : ''); ?>" id="subcats-<?php echo e($cat->id); ?>">
                            <?php $__currentLoopData = $cat->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug)); ?>" class="fb-subcategory-item">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name"><?php echo e($subcategory->subcategory_name); ?></span>
                                <span class="fb-subcategory-count"><?php echo e($subcategory->news_posts_count ?? 0); ?></span>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- Category Header -->
        <div class="fb-category-header">
            <div class="fb-category-cover">
                <?php
                    $siteLogo = \App\Models\SiteSetting::get('site_logo');
                    $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                ?>
                <img src="<?php echo e(asset('frontend/assets/images/category-cover.jpg')); ?>" alt="<?php echo e($category->category_name); ?>" onerror="this.src='<?php echo e($logoPath); ?>'">
                <div class="fb-category-overlay"></div>
                <div class="fb-category-info">
                    <h1 class="fb-category-title"><?php echo e($category->category_name); ?></h1>
                    <p class="fb-category-description">Latest news and updates from <?php echo e($category->category_name); ?></p>
                    <div class="fb-category-stats">
                        <span class="fb-category-posts"><?php echo e($catnews->total()); ?> Posts</span>
                        <span class="fb-category-followers"><?php echo e(rand(100, 5000)); ?> Followers</span>
                    </div>
                </div>
            </div>
            
            <!-- Category Actions -->
            <div class="fb-category-actions">
                <button class="fb-follow-btn">
                    <i class="fas fa-plus"></i>
                    <span>Follow</span>
                </button>
                <button class="fb-share-btn">
                    <i class="fas fa-share"></i>
                    <span>Share</span>
                </button>
                <button class="fb-more-btn">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="fb-filter-tabs">
            <button class="fb-filter-tab active" data-filter="all">
                <i class="fas fa-newspaper"></i>
                <span>All Posts</span>
            </button>
            <button class="fb-filter-tab" data-filter="recent">
                <i class="fas fa-clock"></i>
                <span>Recent</span>
            </button>
            <button class="fb-filter-tab" data-filter="popular">
                <i class="fas fa-fire"></i>
                <span>Popular</span>
            </button>
            <button class="fb-filter-tab" data-filter="trending">
                <i class="fas fa-trending-up"></i>
                <span>Trending</span>
            </button>
        </div>

        <!-- Top Category Advertisements -->
        <?php if(isset($sponsoredAds['category_top']) && $sponsoredAds['category_top']->count() > 0): ?>
            <?php $__currentLoopData = $sponsoredAds['category_top']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'top'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- Regular Banner Advertisements (Top) -->
        <?php if(isset($advertisements)): ?>
            <?php $__currentLoopData = $advertisements->where('ad_type', 'banner')->where('position', 'top'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'top'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- News Feed Posts -->
        <div class="fb-news-feed">
            <?php if($catnews->count() > 0): ?>
                <?php $__currentLoopData = $catnews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                    <!-- Insert Middle Advertisement after 3rd post -->
                    <?php if($index == 3): ?>
                        <?php if(isset($sponsoredAds['category_middle']) && $sponsoredAds['category_middle']->count() > 0): ?>
                            <?php $__currentLoopData = $sponsoredAds['category_middle']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'middle'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <!-- Regular Inline Advertisements (Middle) -->
                        <?php if(isset($advertisements)): ?>
                            <?php $__currentLoopData = $advertisements->where('ad_type', 'inline')->where('position', 'middle'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'middle'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                <article class="fb-post">
                    <!-- Post Header -->
                    <div class="fb-post-header">
                        <div class="fb-post-avatar">
                            <?php
                                $photoPath = 'upload/no_image.jpg';
                                if (!empty($news->user->photo)) {
                                    // Check if photo already contains path (starts with 'upload/')
                                    if (str_starts_with($news->user->photo, 'upload/')) {
                                        // Photo already contains full path (new format)
                                        $photoPath = $news->user->photo;
                                    } else {
                                        // Photo contains only filename (old format) - assume admin
                                        $photoPath = 'upload/admin_images/' . $news->user->photo;
                                    }
                                }
                            ?>
                            <img src="<?php echo e(url($photoPath)); ?>" alt="<?php echo e($news->user->name); ?>">
                        </div>
                        <div class="fb-post-info">
                            <h4 class="fb-post-author"><?php echo e($news->user->name ?? 'NitiKotha'); ?></h4>
                            <div class="fb-post-meta">
                                <span class="fb-post-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                <span class="fb-post-separator">·</span>
                                <span class="fb-post-category"><?php echo e($news->category->category_name); ?></span>
                                <span class="fb-post-separator">·</span>
                                <i class="fas fa-globe-americas"></i>
                            </div>
                        </div>
                        <div class="fb-post-options">
                            <button class="fb-options-btn">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="fb-post-content">
                        <h3 class="fb-post-title">
                            <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                <?php echo e($news->news_title); ?>

                            </a>
                        </h3>
                        <p class="fb-post-excerpt">
                            <?php echo e(Str::limit(strip_tags($news->news_details), 200)); ?>

                        </p>
                    </div>

                    <!-- Post Image -->
                    <?php if($news->image): ?>
                    <div class="fb-post-image">
                        <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>">
                        <a href="<?php echo e(url('v2/news/details/'.$news->id.'/'.$news->news_title_slug)); ?>" class="fb-image-overlay">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Post Stats -->
                    <div class="fb-post-stats">
                        <div class="fb-post-reactions">
                            <div class="fb-reaction-icons">
                                <span class="fb-reaction like">👍</span>
                                <span class="fb-reaction love">❤️</span>
                                <span class="fb-reaction wow">😮</span>
                            </div>
                            <span class="fb-reaction-count" id="likes-count-<?php echo e($news->id); ?>"><?php echo e($news->likes_count ?? 0); ?></span>
                        </div>
                        <div class="fb-post-engagement">
                            <span class="fb-comments-count" id="comments-count-<?php echo e($news->id); ?>"><?php echo e($news->comments_count ?? 0); ?> comments</span>
                            <span class="fb-shares-count" id="shares-count-<?php echo e($news->id); ?>"><?php echo e($news->shares_count ?? 0); ?> shares</span>
                        </div>
                    </div>

                    <!-- Post Actions -->
                    <div class="fb-post-actions">
                        <button class="fb-action-btn fb-like-btn <?php echo e($news->user_liked ? 'liked' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-thumbs-up"></i>
                            <span><?php echo e($news->user_liked ? 'Liked' : 'Like'); ?></span>
                        </button>
                        <button class="fb-action-btn fb-comment-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-comment"></i>
                            <span>Comment</span>
                        </button>
                        <button class="fb-action-btn fb-share-btn" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-share"></i>
                            <span>Share</span>
                        </button>
                        <button class="fb-action-btn fb-save-btn <?php echo e($news->user_saved ? 'saved' : ''); ?>" data-news-id="<?php echo e($news->id); ?>">
                            <i class="fas fa-bookmark"></i>
                            <span><?php echo e($news->user_saved ? 'Saved' : 'Save'); ?></span>
                        </button>
                    </div>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="fb-no-posts">
                    <div class="fb-no-posts-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3 class="fb-no-posts-title">No posts yet</h3>
                    <p class="fb-no-posts-text">There are no posts in this category yet. Check back later for updates!</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if($catnews->hasPages()): ?>
        <div class="fb-pagination">
            <?php echo e($catnews->links()); ?>

        </div>
        <?php endif; ?>

        <!-- Bottom Category Advertisements -->
        <?php if(isset($sponsoredAds['category_bottom']) && $sponsoredAds['category_bottom']->count() > 0): ?>
            <?php $__currentLoopData = $sponsoredAds['category_bottom']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.sponsored_ad', ['ad' => $ad, 'position' => 'bottom'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

        <!-- Regular Banner Advertisements (Bottom) -->
        <?php if(isset($advertisements)): ?>
            <?php $__currentLoopData = $advertisements->where('ad_type', 'banner')->where('position', 'bottom'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ad): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php echo $__env->make('frontend.partials.advertisement', ['ad' => $ad, 'position' => 'bottom'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Popular in Category -->
        <div class="fb-popular-section">
            <h4 class="fb-section-title">Popular in <?php echo e($category->category_name); ?></h4>
            <div class="fb-popular-list">
                <?php if(isset($newspopular)): ?>
                    <?php $__currentLoopData = $newspopular->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $popular): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-popular-item">
                        <div class="fb-popular-rank"><?php echo e($index + 1); ?></div>
                        <div class="fb-popular-image">
                            <img src="<?php echo e(asset($popular->image)); ?>" alt="<?php echo e($popular->news_title); ?>">
                        </div>
                        <div class="fb-popular-content">
                            <h5 class="fb-popular-title">
                                <a href="<?php echo e(url('v2/news/details/'.$popular->id.'/'.$popular->news_title_slug)); ?>">
                                    <?php echo e(Str::limit($popular->news_title, 60)); ?>

                                </a>
                            </h5>
                            <p class="fb-popular-meta"><?php echo e($popular->view_count ?? 0); ?> views</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Related Categories -->
        <div class="fb-related-categories">
            <h4 class="fb-section-title">Related Categories</h4>
            <div class="fb-related-list">
                <?php if(isset($categories)): ?>
                    <?php $__currentLoopData = $categories->where('id', '!=', $category->id)->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(url('v2/news/category/'.$related->id.'/'.$related->category_slug)); ?>" class="fb-related-item">
                        <div class="fb-related-icon"><?php echo e($related->category_icon); ?></div>
                        <div class="fb-related-info">
                            <h5 class="fb-related-name"><?php echo e($related->category_name); ?></h5>
                            <p class="fb-related-count"><?php echo e($related->news_posts_count ?? 0); ?> posts</p>
                        </div>
                    </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Suggested for You -->
        <div class="fb-suggested-section">
            <h4 class="fb-section-title">Suggested for You</h4>
            <div class="fb-suggested-list">
                <?php if(isset($newnewspost)): ?>
                    <?php $__currentLoopData = $newnewspost->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suggested): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="fb-suggested-item">
                        <div class="fb-suggested-image">
                            <img src="<?php echo e(asset($suggested->image)); ?>" alt="<?php echo e($suggested->news_title); ?>">
                        </div>
                        <div class="fb-suggested-content">
                            <h5 class="fb-suggested-title">
                                <a href="<?php echo e(url('v2/news/details/'.$suggested->id.'/'.$suggested->news_title_slug)); ?>">
                                    <?php echo e(Str::limit($suggested->news_title, 50)); ?>

                                </a>
                            </h5>
                            <p class="fb-suggested-meta"><?php echo e($suggested->created_at->format('M d')); ?></p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layout_facebook_style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/facebook_category.blade.php ENDPATH**/ ?>