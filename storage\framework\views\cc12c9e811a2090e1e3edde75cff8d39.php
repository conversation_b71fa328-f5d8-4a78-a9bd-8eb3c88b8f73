<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <?php
    $seo = App\Models\SeoSetting::find(1);
    ?>
    
    <title><?php echo $__env->yieldContent('title'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="title" content="<?php echo e($seo->meta_title ?? 'NitiKotha - Primium Blog'); ?>">
    <meta name="author" content="<?php echo e($seo->meta_author ?? 'NitiKotha'); ?>">
    <meta name="keywords" content="<?php echo e($seo->meta_keyword ?? 'news, facebook, news, social news'); ?>">
    <meta name="description" content="<?php echo e($seo->meta_description ?? 'Experience news in a Facebook-style interface with social interactions.'); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?php echo $__env->yieldContent('title'); ?>">
    <meta property="og:description" content="<?php echo e($seo->meta_description ?? 'Experience news in a Facebook-style interface.'); ?>">
    <meta property="og:image" content="<?php echo e(asset('frontend/assets/images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('frontend/assets/images/favicon.gif')); ?>" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Modern NitiKotha CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/nitikotha-modern.css')); ?>?v=<?php echo e(time()); ?>">
</head>
<body>
    <!-- Modern NitiKotha Header -->
    <header class="nk-header">
        <div class="nk-header-backdrop"></div>
        <div class="nk-header-container">
            <!-- Left Section - Logo & Brand -->
            <div class="nk-header-left">
                <div class="nk-brand">
                    <a href="<?php echo e(url('/v2')); ?>" class="nk-brand-link">
                        <?php
                            $siteLogo = \App\Models\SiteSetting::get('site_logo');
                            $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                        ?>
                        <div class="nk-logo-container">
                            <img src="<?php echo e($logoPath); ?>" alt="<?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?>" class="nk-logo">
                            <div class="nk-logo-glow"></div>
                        </div>
                        <div class="nk-brand-text">
                            <span class="nk-brand-name"><?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?></span>
                            <span class="nk-brand-tagline">Modern Blog Platform</span>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Center Section - Navigation -->
            <div class="nk-header-center">
                <nav class="nk-nav">
                    <a href="<?php echo e(url('/v2')); ?>" class="nk-nav-item <?php echo e(request()->is('v2') ? 'active' : ''); ?>" data-tooltip="Home">
                        <div class="nk-nav-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="nk-nav-label">Home</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <?php
                        $headerCategories = \App\Models\Category::where('is_active', 1)->orderBy('category_name')->take(4)->get();
                    ?>
                    <?php $__currentLoopData = $headerCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(url('v2/news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="nk-nav-item" data-tooltip="<?php echo e($category->category_name); ?>">
                        <div class="nk-nav-icon">
                            <?php
                                $iconMap = [
                                    'technology' => 'fas fa-microchip',
                                    'sports' => 'fas fa-trophy',
                                    'entertainment' => 'fas fa-star',
                                    'health' => 'fas fa-heart-pulse',
                                    'politics' => 'fas fa-landmark',
                                    'business' => 'fas fa-briefcase',
                                    'science' => 'fas fa-flask',
                                    'education' => 'fas fa-graduation-cap',
                                    'travel' => 'fas fa-plane',
                                    'food' => 'fas fa-utensils'
                                ];
                                $categorySlug = strtolower($category->category_slug);
                                $icon = $iconMap[$categorySlug] ?? 'fas fa-newspaper';
                            ?>
                            <i class="<?php echo e($icon); ?>"></i>
                        </div>
                        <span class="nk-nav-label"><?php echo e(Str::limit($category->category_name, 8)); ?></span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </nav>
            </div>

            <!-- Right Section - Search & Actions -->
            <div class="nk-header-right">
                <!-- Search -->
                <div class="nk-search-container">
                    <div class="nk-search-wrapper">
                        <i class="fas fa-search nk-search-icon"></i>
                        <input type="text" placeholder="Search news..." class="nk-search-input">
                        <div class="nk-search-backdrop"></div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button class="nk-theme-toggle" data-tooltip="Toggle Theme">
                    <i class="fas fa-moon nk-theme-icon"></i>
                    <div class="nk-theme-indicator"></div>
                </button>

                <!-- User Actions -->
                <div class="nk-user-actions">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- Notifications -->
                        <button class="nk-action-btn" data-tooltip="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="nk-notification-badge">3</span>
                            <div class="nk-action-ripple"></div>
                        </button>

                        <!-- Create Post -->
                        <button class="nk-action-btn" data-tooltip="Create Post">
                            <i class="fas fa-plus"></i>
                            <div class="nk-action-ripple"></div>
                        </button>

                        <!-- Profile Menu -->
                        <div class="nk-profile-menu">
                            <?php
                                $user = auth()->user();
                                $photo = 'upload/no_image.jpg';
                                if ($user && !empty($user->photo)) {
                                    if ($user->hasRole('admin')) {
                                        $photo = Str::startsWith($user->photo, 'upload/') ? $user->photo : 'upload/admin_images/' . $user->photo;
                                    } else {
                                        $photo = Str::startsWith($user->photo, 'upload/') ? $user->photo : 'upload/user_images/' . $user->photo;
                                    }
                                }
                            ?>
                            <div class="nk-profile-avatar">
                                <img src="<?php echo e(url($photo)); ?>" alt="<?php echo e($user->name); ?>">
                                <div class="nk-profile-status"></div>
                            </div>
                            <div class="nk-profile-info">
                                <span class="nk-profile-name"><?php echo e($user->name); ?></span>
                                <i class="fas fa-chevron-down nk-profile-arrow"></i>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Auth Buttons -->
                        <a href="<?php echo e(route('login')); ?>" class="nk-auth-btn nk-login-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                            <div class="nk-btn-glow"></div>
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="nk-auth-btn nk-register-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>Join</span>
                            <div class="nk-btn-glow"></div>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="nk-mobile-toggle">
            <span class="nk-hamburger"></span>
            <span class="nk-hamburger"></span>
            <span class="nk-hamburger"></span>
        </button>
    </header>

    <!-- Main Content -->
    <main class="nk-main">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Modern NitiKotha JavaScript -->
    <script src="<?php echo e(asset('frontend/assets/js/nitikotha-modern.js')); ?>?v=<?php echo e(time()); ?>" defer></script>
</body>
</html>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/layout_facebook_style.blade.php ENDPATH**/ ?>