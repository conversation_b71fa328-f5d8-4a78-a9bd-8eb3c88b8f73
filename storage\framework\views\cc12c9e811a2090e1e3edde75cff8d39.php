<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <?php
    $seo = App\Models\SeoSetting::find(1);
    ?>
    
    <title><?php echo $__env->yieldContent('title'); ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="title" content="<?php echo e($seo->meta_title ?? 'NitiKotha - Facebook Style News Portal'); ?>">
    <meta name="author" content="<?php echo e($seo->meta_author ?? 'NitiKotha'); ?>">
    <meta name="keywords" content="<?php echo e($seo->meta_keyword ?? 'news, facebook style, social news'); ?>">
    <meta name="description" content="<?php echo e($seo->meta_description ?? 'Experience news in a Facebook-style interface with social interactions.'); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?php echo $__env->yieldContent('title'); ?>">
    <meta property="og:description" content="<?php echo e($seo->meta_description ?? 'Experience news in a Facebook-style interface.'); ?>">
    <meta property="og:image" content="<?php echo e(asset('frontend/assets/images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('frontend/assets/images/favicon.gif')); ?>" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Facebook Style CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/assets/css/facebook-style.css')); ?>?v=<?php echo e(time()); ?>">
</head>
<body>
    <!-- Facebook-style Header -->
    <header class="fb-header">
        <div class="fb-header-container">
            <!-- Left Section -->
            <div class="fb-header-left">
                <div class="fb-logo">
                    <a href="<?php echo e(url('/v2')); ?>">
                        <?php
                            $siteLogo = \App\Models\SiteSetting::get('site_logo');
                            $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                        ?>
                        <img src="<?php echo e($logoPath); ?>" alt="<?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?>">
                        <span class="fb-logo-text"><?php echo e(\App\Models\SiteSetting::get('site_name', 'NitiKotha')); ?></span>
                    </a>
                </div>
                <div class="fb-search">
                    <div class="fb-search-container">
                        <i class="fas fa-search fb-search-icon"></i>
                        <input type="text" placeholder="Search NitiKotha" class="fb-search-input">
                    </div>
                </div>
            </div>

            <!-- Center Section -->
            <div class="fb-header-center">
                <nav class="fb-main-nav">
                    <a href="<?php echo e(url('/v2')); ?>" class="fb-nav-link active">
                        <i class="fas fa-home"></i>
                    </a>
                    <a href="#" class="fb-nav-link">
                        <i class="fas fa-fire"></i>
                    </a>
                    <a href="#" class="fb-nav-link">
                        <i class="fas fa-video"></i>
                    </a>
                    <a href="#" class="fb-nav-link">
                        <i class="fas fa-users"></i>
                    </a>
                    <a href="#" class="fb-nav-link">
                        <i class="fas fa-gamepad"></i>
                    </a>
                </nav>
            </div>

            <!-- Right Section -->
            <div class="fb-header-right">
                <div class="fb-header-actions">
                    <?php if(auth()->guard()->check()): ?>
                        <button class="fb-action-icon">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="fb-action-icon">
                            <i class="fab fa-facebook-messenger"></i>
                        </button>
                        <button class="fb-action-icon">
                            <i class="fas fa-bell"></i>
                            <span class="fb-notification-badge">3</span>
                        </button>
                        <div class="fb-profile-menu">
                            <img src="<?php echo e((!empty(auth()->user()->photo)) ? url('upload/admin_images/'.auth()->user()->photo): url('upload/no_image.jpg')); ?>" alt="Profile" class="fb-profile-avatar">
                            <i class="fas fa-caret-down"></i>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="fb-auth-btn fb-login-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="fb-auth-btn fb-register-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>Register</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="fb-main">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Facebook Style JavaScript -->
    <script src="<?php echo e(asset('frontend/assets/js/facebook-style.js')); ?>?v=<?php echo e(time()); ?>" defer></script>
</body>
</html>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/layout_facebook_style.blade.php ENDPATH**/ ?>