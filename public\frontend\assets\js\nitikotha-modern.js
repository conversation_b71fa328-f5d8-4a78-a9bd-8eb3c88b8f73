/**
 * NitiKotha Modern Header JavaScript
 * Handles theme switching, search functionality, and interactive elements
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeModernHeader();
    });

    function initializeModernHeader() {
        console.log('🚀 Initializing NitiKotha Modern Header');

        // Initialize all features
        initializeThemeToggle();
        initializeSearch();
        initializeMobileMenu();
        initializeScrollEffects();
        initializeAnimations();
        initializeTooltips();
        initializeCategoryToggles();
        initializePostInteractions();
        initializeLoadMore();
        initializeFilterTabs();
        initializeFollowButtons();

        console.log('✅ NitiKotha Modern Header initialized successfully');
    }

    // Theme Toggle Functionality
    function initializeThemeToggle() {
        const themeToggle = document.querySelector('.nk-theme-toggle');
        const themeIcon = document.querySelector('.nk-theme-icon');
        
        if (!themeToggle) return;

        // Get current theme from localStorage or default to light
        let currentTheme = localStorage.getItem('nk-theme') || 'light';
        
        // Apply initial theme
        applyTheme(currentTheme);

        themeToggle.addEventListener('click', function() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(currentTheme);
            localStorage.setItem('nk-theme', currentTheme);
            
            // Add click animation
            this.style.transform = 'translateY(-2px) rotate(360deg)';
            setTimeout(() => {
                this.style.transform = '';
            }, 300);
        });

        function applyTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            
            if (themeIcon) {
                themeIcon.className = theme === 'light' ? 'fas fa-moon nk-theme-icon' : 'fas fa-sun nk-theme-icon';
            }
        }
    }

    // Enhanced Search Functionality
    function initializeSearch() {
        const searchInput = document.querySelector('.nk-search-input');
        const searchWrapper = document.querySelector('.nk-search-wrapper');
        
        if (!searchInput || !searchWrapper) return;

        let searchTimeout;

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            // Clear previous timeout
            clearTimeout(searchTimeout);
            
            // Add loading state
            searchWrapper.classList.add('searching');
            
            // Debounce search
            searchTimeout = setTimeout(() => {
                if (query.length >= 2) {
                    performSearch(query);
                } else {
                    hideSearchResults();
                }
                searchWrapper.classList.remove('searching');
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            searchWrapper.classList.add('focused');
        });

        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                searchWrapper.classList.remove('focused');
                hideSearchResults();
            }, 200);
        });

        function performSearch(query) {
            // This would typically make an AJAX call to your search endpoint
            console.log('🔍 Searching for:', query);
            
            // For now, just show a placeholder
            showSearchResults([
                { title: 'Sample Result 1', url: '#' },
                { title: 'Sample Result 2', url: '#' }
            ]);
        }

        function showSearchResults(results) {
            // Create or update search results dropdown
            let dropdown = document.querySelector('.nk-search-dropdown');
            
            if (!dropdown) {
                dropdown = document.createElement('div');
                dropdown.className = 'nk-search-dropdown';
                searchWrapper.appendChild(dropdown);
            }

            dropdown.innerHTML = results.map(result => 
                `<a href="${result.url}" class="nk-search-result">
                    <i class="fas fa-search"></i>
                    <span>${result.title}</span>
                </a>`
            ).join('');

            dropdown.style.display = 'block';
        }

        function hideSearchResults() {
            const dropdown = document.querySelector('.nk-search-dropdown');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        }
    }

    // Mobile Menu Functionality
    function initializeMobileMenu() {
        const mobileToggle = document.querySelector('.nk-mobile-toggle');
        const headerCenter = document.querySelector('.nk-header-center');
        
        if (!mobileToggle) return;

        mobileToggle.addEventListener('click', function() {
            const isOpen = this.classList.contains('active');
            
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        });

        function openMobileMenu() {
            mobileToggle.classList.add('active');
            
            // Create mobile menu overlay
            const overlay = document.createElement('div');
            overlay.className = 'nk-mobile-overlay';
            overlay.innerHTML = `
                <div class="nk-mobile-menu">
                    <div class="nk-mobile-nav">
                        <a href="/v2" class="nk-mobile-nav-item">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <a href="/v2/category/technology" class="nk-mobile-nav-item">
                            <i class="fas fa-microchip"></i>
                            <span>Technology</span>
                        </a>
                        <a href="/v2/category/sports" class="nk-mobile-nav-item">
                            <i class="fas fa-trophy"></i>
                            <span>Sports</span>
                        </a>
                        <a href="/v2/category/entertainment" class="nk-mobile-nav-item">
                            <i class="fas fa-star"></i>
                            <span>Entertainment</span>
                        </a>
                        <a href="/v2/category/health" class="nk-mobile-nav-item">
                            <i class="fas fa-heart-pulse"></i>
                            <span>Health</span>
                        </a>
                    </div>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            // Animate in
            setTimeout(() => {
                overlay.classList.add('active');
            }, 10);
            
            // Close on overlay click
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeMobileMenu();
                }
            });
        }

        function closeMobileMenu() {
            mobileToggle.classList.remove('active');
            const overlay = document.querySelector('.nk-mobile-overlay');
            
            if (overlay) {
                overlay.classList.remove('active');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }
    }

    // Scroll Effects
    function initializeScrollEffects() {
        const header = document.querySelector('.nk-header');
        if (!header) return;

        let lastScrollY = window.scrollY;
        let ticking = false;

        function updateHeader() {
            const scrollY = window.scrollY;
            
            if (scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide/show header on scroll
            if (scrollY > lastScrollY && scrollY > 200) {
                header.classList.add('hidden');
            } else {
                header.classList.remove('hidden');
            }

            lastScrollY = scrollY;
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick, { passive: true });
    }

    // Animations
    function initializeAnimations() {
        // Add staggered animation delays to nav items
        const navItems = document.querySelectorAll('.nk-nav-item');
        navItems.forEach((item, index) => {
            item.style.setProperty('--i', index);
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements that should animate in
        document.querySelectorAll('.nk-nav-item, .nk-action-btn').forEach(el => {
            observer.observe(el);
        });
    }

    // Enhanced Tooltips
    function initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', showTooltip);
            element.addEventListener('mouseleave', hideTooltip);
        });

        function showTooltip(e) {
            const element = e.target;
            const tooltip = element.getAttribute('data-tooltip');
            
            if (!tooltip) return;

            // Create tooltip element
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'nk-tooltip';
            tooltipEl.textContent = tooltip;
            
            document.body.appendChild(tooltipEl);
            
            // Position tooltip
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltipEl.getBoundingClientRect();
            
            tooltipEl.style.left = rect.left + (rect.width / 2) - (tooltipRect.width / 2) + 'px';
            tooltipEl.style.top = rect.bottom + 10 + 'px';
            
            // Show tooltip
            setTimeout(() => {
                tooltipEl.classList.add('show');
            }, 10);
            
            element._tooltip = tooltipEl;
        }

        function hideTooltip(e) {
            const element = e.target;
            const tooltip = element._tooltip;
            
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    tooltip.remove();
                }, 200);
                element._tooltip = null;
            }
        }
    }

    // Category Toggle Functionality
    function initializeCategoryToggles() {
        const toggleButtons = document.querySelectorAll('.fb-category-toggle');

        toggleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const targetId = this.getAttribute('data-target');
                const target = document.getElementById(targetId);
                const icon = this.querySelector('i');

                if (target) {
                    const isExpanded = target.classList.contains('expanded');

                    if (isExpanded) {
                        target.classList.remove('expanded');
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        target.classList.add('expanded');
                        icon.style.transform = 'rotate(180deg)';
                    }
                }
            });
        });
    }

    // Post Interactions (Like, Comment, Share)
    function initializePostInteractions() {
        // Like functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.fb-like-btn')) {
                handleLike(e.target.closest('.fb-like-btn'));
            }

            if (e.target.closest('.fb-comment-btn')) {
                handleComment(e.target.closest('.fb-comment-btn'));
            }

            if (e.target.closest('.fb-share-btn')) {
                handleShare(e.target.closest('.fb-share-btn'));
            }
        });

        function handleLike(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const icon = button.querySelector('i');
            const countSpan = button.querySelector('.like-count') || document.querySelector(`#likes-count-${postId}`);

            // Toggle visual state immediately
            const isLiked = button.classList.contains('liked');

            if (isLiked) {
                button.classList.remove('liked');
                icon.className = 'fas fa-thumbs-up';
                button.querySelector('span').textContent = 'Like';
                if (countSpan) {
                    const count = parseInt(countSpan.textContent.replace(/\D/g, '')) - 1;
                    countSpan.textContent = count;
                }
            } else {
                button.classList.add('liked');
                icon.className = 'fas fa-thumbs-up';
                button.querySelector('span').textContent = 'Liked';
                if (countSpan) {
                    const count = parseInt(countSpan.textContent.replace(/\D/g, '')) + 1;
                    countSpan.textContent = count;
                }

                // Add animation
                icon.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            }

            // Send AJAX request
            fetch('/api/news/like', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({
                    news_post_id: postId,
                    action: isLiked ? 'unlike' : 'like'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    // Revert on failure
                    if (isLiked) {
                        button.classList.add('liked');
                        icon.className = 'fas fa-thumbs-up';
                        button.querySelector('span').textContent = 'Liked';
                    } else {
                        button.classList.remove('liked');
                        icon.className = 'fas fa-thumbs-up';
                        button.querySelector('span').textContent = 'Like';
                    }
                }
            })
            .catch(error => {
                console.error('Like error:', error);
                // Revert on error
                if (isLiked) {
                    button.classList.add('liked');
                    icon.className = 'fas fa-thumbs-up';
                    button.querySelector('span').textContent = 'Liked';
                } else {
                    button.classList.remove('liked');
                    icon.className = 'fas fa-thumbs-up';
                    button.querySelector('span').textContent = 'Like';
                }
            });
        }

        function handleComment(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const commentsSection = document.querySelector(`#comments-${postId}`);

            if (commentsSection) {
                const isVisible = commentsSection.style.display !== 'none';
                commentsSection.style.display = isVisible ? 'none' : 'block';

                if (!isVisible) {
                    // Focus on comment input
                    const commentInput = commentsSection.querySelector('.comment-input');
                    if (commentInput) {
                        setTimeout(() => commentInput.focus(), 100);
                    }
                }
            }
        }

        function handleShare(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const postUrl = button.getAttribute('data-post-url') || window.location.href;
            const postTitle = button.getAttribute('data-post-title') || document.title;

            // Show share modal or use Web Share API
            if (navigator.share) {
                navigator.share({
                    title: postTitle,
                    url: postUrl
                }).then(() => {
                    // Update share count
                    updateShareCount(postId);
                }).catch(err => {
                    console.log('Share cancelled');
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(postUrl).then(() => {
                    showToast('Link copied to clipboard!');
                    updateShareCount(postId);
                });
            }
        }

        function updateShareCount(postId) {
            fetch('/api/news/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({
                    news_post_id: postId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const shareBtn = document.querySelector(`[data-post-id="${postId}"].fb-share-btn`) ||
                                   document.querySelector(`[data-news-id="${postId}"].fb-share-btn`);
                    const countSpan = shareBtn?.querySelector('.share-count') ||
                                    document.querySelector(`#shares-count-${postId}`);
                    if (countSpan) {
                        const currentCount = parseInt(countSpan.textContent.replace(/\D/g, '')) || 0;
                        countSpan.textContent = `${currentCount + 1} shares`;
                    }
                }
            });
        }
    }

    // Load More Functionality
    function initializeLoadMore() {
        const loadMoreBtn = document.querySelector('.fb-load-more-btn') || document.querySelector('#load-more-btn');

        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                const currentPage = parseInt(this.getAttribute('data-page') || '1');
                const nextPage = currentPage + 1;

                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

                fetch(`/v2/load-more-posts?page=${nextPage}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.posts.length > 0) {
                            const container = document.querySelector('#news-feed-container');

                            data.posts.forEach(post => {
                                const postHtml = createPostHtml(post);
                                container.insertAdjacentHTML('beforeend', postHtml);
                            });

                            this.setAttribute('data-page', nextPage);

                            if (!data.hasMore) {
                                this.style.display = 'none';
                            }
                        } else {
                            this.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Load more error:', error);
                        showToast('Failed to load more posts');
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-plus"></i> Load More';
                    });
            });
        }
    }

    // Helper Functions
    function createPostHtml(post) {
        return `
            <article class="fb-post" data-post-id="${post.id}">
                <div class="fb-post-header">
                    <div class="fb-post-avatar">
                        <img src="${post.user?.photo || '/upload/no_image.jpg'}" alt="${post.user?.name || 'User'}">
                    </div>
                    <div class="fb-post-info">
                        <h4 class="fb-post-author">${post.user?.name || 'Anonymous'}</h4>
                        <div class="fb-post-meta">
                            <span class="fb-post-category">${post.category?.category_name || 'News'}</span>
                            <span>•</span>
                            <span class="fb-post-time">${formatDate(post.created_at)}</span>
                        </div>
                    </div>
                </div>
                <div class="fb-post-content">
                    <h2 class="fb-post-title">
                        <a href="/v2/news/details/${post.id}/${post.news_title_slug}">${post.news_title}</a>
                    </h2>
                    <p class="fb-post-excerpt">${post.news_details?.substring(0, 150) || ''}...</p>
                    ${post.image ? `
                        <div class="fb-post-image">
                            <img src="${post.image}" alt="${post.news_title}">
                        </div>
                    ` : ''}
                </div>
                <div class="fb-post-actions">
                    <div class="fb-post-stats">
                        <span><i class="fas fa-eye"></i> ${post.view_count || 0}</span>
                        <span><i class="fas fa-heart"></i> ${post.likes_count || 0}</span>
                        <span><i class="fas fa-comment"></i> ${post.comments_count || 0}</span>
                    </div>
                    <div class="fb-post-buttons">
                        <button class="fb-post-btn fb-like-btn" data-post-id="${post.id}">
                            <i class="far fa-heart"></i>
                            <span class="like-count">${post.likes_count || 0}</span>
                        </button>
                        <button class="fb-post-btn fb-comment-btn" data-post-id="${post.id}">
                            <i class="far fa-comment"></i>
                            Comment
                        </button>
                        <button class="fb-post-btn fb-share-btn" data-post-id="${post.id}" data-post-url="/v2/news/details/${post.id}/${post.news_title_slug}">
                            <i class="fas fa-share"></i>
                            <span class="share-count">${post.shares_count || 0}</span>
                        </button>
                    </div>
                </div>
            </article>
        `;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        if (days < 7) return `${days}d ago`;
        return date.toLocaleDateString();
    }

    function showToast(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'nk-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--nk-gray-800);
            color: var(--nk-white);
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Filter Tabs Functionality
    function initializeFilterTabs() {
        const filterTabs = document.querySelectorAll('.fb-filter-tab');
        const newsContainer = document.querySelector('.fb-news-feed');

        if (!filterTabs.length || !newsContainer) return;

        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active tab
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // Show loading state
                newsContainer.style.opacity = '0.5';
                newsContainer.style.pointerEvents = 'none';

                // Filter posts based on selection
                filterPosts(filter).then(() => {
                    newsContainer.style.opacity = '1';
                    newsContainer.style.pointerEvents = 'auto';
                });
            });
        });
    }

    async function filterPosts(filter) {
        const currentUrl = window.location.href;
        const categoryId = currentUrl.match(/category\/(\d+)/)?.[1];

        if (!categoryId) return;

        try {
            const response = await fetch(`/api/category/${categoryId}/filter?type=${filter}`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                const data = await response.json();
                updateNewsContainer(data.posts);
            }
        } catch (error) {
            console.error('Filter error:', error);
            // Fallback: client-side filtering
            clientSideFilter(filter);
        }
    }

    function clientSideFilter(filter) {
        const posts = document.querySelectorAll('.fb-post');
        const postsArray = Array.from(posts);

        // Sort posts based on filter
        let sortedPosts;
        switch (filter) {
            case 'recent':
                sortedPosts = postsArray.sort((a, b) => {
                    const dateA = new Date(a.querySelector('.fb-post-time')?.textContent || 0);
                    const dateB = new Date(b.querySelector('.fb-post-time')?.textContent || 0);
                    return dateB - dateA;
                });
                break;
            case 'popular':
                sortedPosts = postsArray.sort((a, b) => {
                    const viewsA = parseInt(a.querySelector('.fb-post-stats')?.textContent?.match(/\d+/)?.[0] || 0);
                    const viewsB = parseInt(b.querySelector('.fb-post-stats')?.textContent?.match(/\d+/)?.[0] || 0);
                    return viewsB - viewsA;
                });
                break;
            case 'trending':
                sortedPosts = postsArray.sort((a, b) => {
                    const likesA = parseInt(a.querySelector('#likes-count')?.textContent || 0);
                    const likesB = parseInt(b.querySelector('#likes-count')?.textContent || 0);
                    return likesB - likesA;
                });
                break;
            default:
                sortedPosts = postsArray;
        }

        // Reorder posts in DOM
        const container = document.querySelector('.fb-news-feed');
        sortedPosts.forEach(post => container.appendChild(post));
    }

    function updateNewsContainer(posts) {
        const container = document.querySelector('.fb-news-feed');
        if (!container) return;

        // Clear existing posts
        container.innerHTML = '';

        // Add new posts
        posts.forEach(post => {
            const postHtml = createPostHtml(post);
            container.insertAdjacentHTML('beforeend', postHtml);
        });

        // Reinitialize post interactions
        initializePostInteractions();
    }

    // Follow Button Functionality
    function initializeFollowButtons() {
        const followButtons = document.querySelectorAll('.fb-follow-btn');

        followButtons.forEach(button => {
            button.addEventListener('click', function() {
                const isFollowing = this.classList.contains('following');
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                if (isFollowing) {
                    this.classList.remove('following');
                    icon.className = 'fas fa-plus';
                    text.textContent = 'Follow';
                    showToast('Unfollowed successfully');
                } else {
                    this.classList.add('following');
                    icon.className = 'fas fa-check';
                    text.textContent = 'Following';
                    showToast('Following successfully');
                }

                // Send follow/unfollow request
                const categoryId = window.location.href.match(/category\/(\d+)/)?.[1];
                if (categoryId) {
                    fetch('/api/category/follow', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                        },
                        body: JSON.stringify({
                            category_id: categoryId,
                            action: isFollowing ? 'unfollow' : 'follow'
                        })
                    }).catch(error => console.error('Follow error:', error));
                }
            });
        });
    }

})();
