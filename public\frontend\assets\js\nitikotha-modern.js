/**
 * NitiKotha Modern Header JavaScript
 * Handles theme switching, search functionality, and interactive elements
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeModernHeader();
    });

    function initializeModernHeader() {
        console.log('🚀 Initializing NitiKotha Modern Header');

        // Initialize all features
        initializeThemeToggle();
        initializeSearch();
        initializeMobileMenu();
        initializeScrollEffects();
        initializeAnimations();
        initializeTooltips();
        initializeCategoryToggles();
        initializePostInteractions();
        initializeLoadMore();
        initializeFilterTabs();
        initializeFollowButtons();
        initializeSidebarScrollEffects();
        initializeCommentForms();
        initializeReactionPickers();
        initializeStoryCarousel();
        initializeImageErrorHandling();

        console.log('✅ NitiKotha Modern Header initialized successfully');
    }

    // Theme Toggle Functionality
    function initializeThemeToggle() {
        const themeToggle = document.querySelector('.nk-theme-toggle');
        const themeIcon = document.querySelector('.nk-theme-icon');

        if (!themeToggle) return;

        // Get current theme from localStorage or default to light
        let currentTheme = localStorage.getItem('nk-theme') || 'light';
        
        // Apply initial theme
        applyTheme(currentTheme);

        themeToggle.addEventListener('click', function() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(currentTheme);
            localStorage.setItem('nk-theme', currentTheme);
            
            // Add click animation
            this.style.transform = 'translateY(-2px) rotate(360deg)';
            setTimeout(() => {
                this.style.transform = '';
            }, 300);
        });

        function applyTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            
            if (themeIcon) {
                themeIcon.className = theme === 'light' ? 'fas fa-moon nk-theme-icon' : 'fas fa-sun nk-theme-icon';
            }
        }
    }

    // Enhanced Search Functionality
    function initializeSearch() {
        const searchInput = document.querySelector('.nk-search-input');
        const searchWrapper = document.querySelector('.nk-search-wrapper');
        
        if (!searchInput || !searchWrapper) return;

        let searchTimeout;

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            // Clear previous timeout
            clearTimeout(searchTimeout);
            
            // Add loading state
            searchWrapper.classList.add('searching');
            
            // Debounce search
            searchTimeout = setTimeout(() => {
                if (query.length >= 2) {
                    performSearch(query);
                } else {
                    hideSearchResults();
                }
                searchWrapper.classList.remove('searching');
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            searchWrapper.classList.add('focused');
        });

        searchInput.addEventListener('blur', function() {
            setTimeout(() => {
                searchWrapper.classList.remove('focused');
                hideSearchResults();
            }, 200);
        });

        function performSearch(query) {
            // This would typically make an AJAX call to your search endpoint
            console.log('🔍 Searching for:', query);
            
            // For now, just show a placeholder
            showSearchResults([
                { title: 'Sample Result 1', url: '#' },
                { title: 'Sample Result 2', url: '#' }
            ]);
        }

        function showSearchResults(results) {
            // Create or update search results dropdown
            let dropdown = document.querySelector('.nk-search-dropdown');
            
            if (!dropdown) {
                dropdown = document.createElement('div');
                dropdown.className = 'nk-search-dropdown';
                searchWrapper.appendChild(dropdown);
            }

            dropdown.innerHTML = results.map(result => 
                `<a href="${result.url}" class="nk-search-result">
                    <i class="fas fa-search"></i>
                    <span>${result.title}</span>
                </a>`
            ).join('');

            dropdown.style.display = 'block';
        }

        function hideSearchResults() {
            const dropdown = document.querySelector('.nk-search-dropdown');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        }
    }

    // Mobile Menu Functionality
    function initializeMobileMenu() {
        const mobileToggle = document.querySelector('.nk-mobile-toggle');
        const headerCenter = document.querySelector('.nk-header-center');
        
        if (!mobileToggle) return;

        mobileToggle.addEventListener('click', function() {
            const isOpen = this.classList.contains('active');
            
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        });

        function openMobileMenu() {
            mobileToggle.classList.add('active');
            
            // Create mobile menu overlay
            const overlay = document.createElement('div');
            overlay.className = 'nk-mobile-overlay';
            overlay.innerHTML = `
                <div class="nk-mobile-menu">
                    <div class="nk-mobile-nav">
                        <a href="/v2" class="nk-mobile-nav-item">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <a href="/v2/category/technology" class="nk-mobile-nav-item">
                            <i class="fas fa-microchip"></i>
                            <span>Technology</span>
                        </a>
                        <a href="/v2/category/sports" class="nk-mobile-nav-item">
                            <i class="fas fa-trophy"></i>
                            <span>Sports</span>
                        </a>
                        <a href="/v2/category/entertainment" class="nk-mobile-nav-item">
                            <i class="fas fa-star"></i>
                            <span>Entertainment</span>
                        </a>
                        <a href="/v2/category/health" class="nk-mobile-nav-item">
                            <i class="fas fa-heart-pulse"></i>
                            <span>Health</span>
                        </a>
                    </div>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            // Animate in
            setTimeout(() => {
                overlay.classList.add('active');
            }, 10);
            
            // Close on overlay click
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeMobileMenu();
                }
            });
        }

        function closeMobileMenu() {
            mobileToggle.classList.remove('active');
            const overlay = document.querySelector('.nk-mobile-overlay');
            
            if (overlay) {
                overlay.classList.remove('active');
                setTimeout(() => {
                    overlay.remove();
                }, 300);
            }
        }
    }

    // Scroll Effects
    function initializeScrollEffects() {
        const header = document.querySelector('.nk-header');
        if (!header) return;

        let lastScrollY = window.scrollY;
        let ticking = false;

        function updateHeader() {
            const scrollY = window.scrollY;
            
            if (scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            // Hide/show header on scroll
            if (scrollY > lastScrollY && scrollY > 200) {
                header.classList.add('hidden');
            } else {
                header.classList.remove('hidden');
            }

            lastScrollY = scrollY;
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        }

        window.addEventListener('scroll', requestTick, { passive: true });
    }

    // Animations
    function initializeAnimations() {
        // Add staggered animation delays to nav items
        const navItems = document.querySelectorAll('.nk-nav-item');
        navItems.forEach((item, index) => {
            item.style.setProperty('--i', index);
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements that should animate in
        document.querySelectorAll('.nk-nav-item, .nk-action-btn').forEach(el => {
            observer.observe(el);
        });
    }

    // Enhanced Tooltips
    function initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', showTooltip);
            element.addEventListener('mouseleave', hideTooltip);
        });

        function showTooltip(e) {
            const element = e.target;
            const tooltip = element.getAttribute('data-tooltip');
            
            if (!tooltip) return;

            // Create tooltip element
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'nk-tooltip';
            tooltipEl.textContent = tooltip;
            
            document.body.appendChild(tooltipEl);
            
            // Position tooltip
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltipEl.getBoundingClientRect();
            
            tooltipEl.style.left = rect.left + (rect.width / 2) - (tooltipRect.width / 2) + 'px';
            tooltipEl.style.top = rect.bottom + 10 + 'px';
            
            // Show tooltip
            setTimeout(() => {
                tooltipEl.classList.add('show');
            }, 10);
            
            element._tooltip = tooltipEl;
        }

        function hideTooltip(e) {
            const element = e.target;
            const tooltip = element._tooltip;
            
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    tooltip.remove();
                }, 200);
                element._tooltip = null;
            }
        }
    }

    // Category Toggle Functionality
    function initializeCategoryToggles() {
        const toggleButtons = document.querySelectorAll('.fb-category-toggle');

        toggleButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const targetId = this.getAttribute('data-target');
                const target = document.getElementById(targetId);
                const icon = this.querySelector('i');

                if (target) {
                    const isExpanded = target.classList.contains('expanded');

                    if (isExpanded) {
                        target.classList.remove('expanded');
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        target.classList.add('expanded');
                        icon.style.transform = 'rotate(180deg)';
                    }
                }
            });
        });
    }

    // Post Interactions (Like, Comment, Share)
    function initializePostInteractions() {
        // Like functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.fb-like-btn')) {
                handleLike(e.target.closest('.fb-like-btn'));
            }

            if (e.target.closest('.fb-comment-btn')) {
                handleComment(e.target.closest('.fb-comment-btn'));
            }

            if (e.target.closest('.fb-share-btn')) {
                handleShare(e.target.closest('.fb-share-btn'));
            }
        });

        function handleLike(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const icon = button.querySelector('i');
            const countSpan = button.querySelector('.like-count') || document.querySelector(`#likes-count-${postId}`);

            // Toggle visual state immediately
            const isLiked = button.classList.contains('liked');

            if (isLiked) {
                button.classList.remove('liked');
                icon.className = 'fas fa-thumbs-up';
                button.querySelector('span').textContent = 'Like';
                if (countSpan) {
                    const count = parseInt(countSpan.textContent.replace(/\D/g, '')) - 1;
                    countSpan.textContent = count;
                }
            } else {
                button.classList.add('liked');
                icon.className = 'fas fa-thumbs-up';
                button.querySelector('span').textContent = 'Liked';
                if (countSpan) {
                    const count = parseInt(countSpan.textContent.replace(/\D/g, '')) + 1;
                    countSpan.textContent = count;
                }

                // Add animation
                icon.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            }

            // Send AJAX request
            fetch('/api/news/like', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({
                    news_post_id: postId,
                    action: isLiked ? 'unlike' : 'like'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    // Revert on failure
                    if (isLiked) {
                        button.classList.add('liked');
                        icon.className = 'fas fa-thumbs-up';
                        button.querySelector('span').textContent = 'Liked';
                    } else {
                        button.classList.remove('liked');
                        icon.className = 'fas fa-thumbs-up';
                        button.querySelector('span').textContent = 'Like';
                    }
                }
            })
            .catch(error => {
                console.error('Like error:', error);
                // Revert on error
                if (isLiked) {
                    button.classList.add('liked');
                    icon.className = 'fas fa-thumbs-up';
                    button.querySelector('span').textContent = 'Liked';
                } else {
                    button.classList.remove('liked');
                    icon.className = 'fas fa-thumbs-up';
                    button.querySelector('span').textContent = 'Like';
                }
            });
        }

        function handleComment(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const commentsSection = document.querySelector(`#comments-${postId}`);

            if (commentsSection) {
                const isVisible = commentsSection.style.display !== 'none';

                if (isVisible) {
                    // Hide comments
                    commentsSection.style.display = 'none';
                    commentsSection.style.opacity = '0';
                    commentsSection.style.transform = 'translateY(-10px)';
                } else {
                    // Show comments with animation
                    commentsSection.style.display = 'block';
                    setTimeout(() => {
                        commentsSection.style.opacity = '1';
                        commentsSection.style.transform = 'translateY(0)';
                    }, 10);

                    // Focus on comment input
                    const commentInput = commentsSection.querySelector('.fb-comment-input');
                    if (commentInput) {
                        setTimeout(() => commentInput.focus(), 100);
                    }

                    // Load existing comments if not loaded
                    loadComments(postId);
                }
            }
        }

        function loadComments(postId) {
            const commentsList = document.querySelector(`#comments-list-${postId}`);
            if (!commentsList || commentsList.dataset.loaded === 'true') return;

            fetch(`/api/news/${postId}/comments`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.comments.length > 0) {
                        const noComments = commentsList.querySelector('.fb-no-comments');
                        if (noComments) noComments.remove();

                        data.comments.forEach(comment => {
                            const commentHtml = createCommentHtml(comment);
                            commentsList.insertAdjacentHTML('beforeend', commentHtml);
                        });
                    }
                    commentsList.dataset.loaded = 'true';
                })
                .catch(error => console.error('Failed to load comments:', error));
        }

        function createCommentHtml(comment) {
            return `
                <div class="fb-comment" data-comment-id="${comment.id}">
                    <div class="fb-comment-avatar">
                        <img src="${comment.user_avatar || '/upload/no_image.jpg'}" alt="${comment.user_name}">
                    </div>
                    <div class="fb-comment-content">
                        <div class="fb-comment-bubble">
                            <h5 class="fb-comment-author">${comment.user_name}</h5>
                            <p class="fb-comment-text">${comment.comment}</p>
                        </div>
                        <div class="fb-comment-meta">
                            <button class="fb-comment-like" data-comment-id="${comment.id}">
                                <i class="fas fa-thumbs-up"></i> Like
                            </button>
                            <button class="fb-comment-reply" data-comment-id="${comment.id}">
                                <i class="fas fa-reply"></i> Reply
                            </button>
                            <span class="fb-comment-time">${formatDate(comment.created_at)}</span>
                            <span class="fb-comment-likes">
                                <i class="fas fa-heart text-danger"></i> ${comment.likes_count || 0}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }

        function handleShare(button) {
            const postId = button.getAttribute('data-post-id') || button.getAttribute('data-news-id');
            const postUrl = button.getAttribute('data-post-url') || window.location.href;
            const postTitle = button.getAttribute('data-post-title') || document.title;

            // Show share modal or use Web Share API
            if (navigator.share) {
                navigator.share({
                    title: postTitle,
                    url: postUrl
                }).then(() => {
                    // Update share count
                    updateShareCount(postId);
                }).catch(err => {
                    console.log('Share cancelled');
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(postUrl).then(() => {
                    showToast('Link copied to clipboard!');
                    updateShareCount(postId);
                });
            }
        }

        function updateShareCount(postId) {
            fetch('/api/news/share', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({
                    news_post_id: postId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const shareBtn = document.querySelector(`[data-post-id="${postId}"].fb-share-btn`) ||
                                   document.querySelector(`[data-news-id="${postId}"].fb-share-btn`);
                    const countSpan = shareBtn?.querySelector('.share-count') ||
                                    document.querySelector(`#shares-count-${postId}`);
                    if (countSpan) {
                        const currentCount = parseInt(countSpan.textContent.replace(/\D/g, '')) || 0;
                        countSpan.textContent = `${currentCount + 1} shares`;
                    }
                }
            });
        }
    }

    // Load More Functionality
    function initializeLoadMore() {
        const loadMoreBtn = document.querySelector('.fb-load-more-btn') || document.querySelector('#load-more-btn');

        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                const currentPage = parseInt(this.getAttribute('data-page') || '1');
                const nextPage = currentPage + 1;

                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

                fetch(`/v2/load-more-posts?page=${nextPage}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.posts.length > 0) {
                            const container = document.querySelector('#news-feed-container');

                            data.posts.forEach(post => {
                                const postHtml = createPostHtml(post);
                                container.insertAdjacentHTML('beforeend', postHtml);
                            });

                            this.setAttribute('data-page', nextPage);

                            if (!data.hasMore) {
                                this.style.display = 'none';
                            }
                        } else {
                            this.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Load more error:', error);
                        showToast('Failed to load more posts');
                    })
                    .finally(() => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-plus"></i> Load More';
                    });
            });
        }
    }

    // Helper Functions
    function createPostHtml(post) {
        // Determine user photo path with proper URL construction
        let userPhotoPath = '/upload/no_image.jpg';
        if (post.user && post.user.photo) {
            if (post.user.photo.startsWith('upload/')) {
                userPhotoPath = '/' + post.user.photo;
            } else {
                userPhotoPath = '/upload/admin_images/' + post.user.photo;
            }
        }

        // Determine post image path with proper URL construction
        let postImageHtml = '';
        if (post.image) {
            let imagePath = post.image;
            if (!imagePath.startsWith('http') && !imagePath.startsWith('/')) {
                imagePath = '/' + imagePath;
            }
            postImageHtml = `
                <div class="fb-post-image">
                    <img src="${imagePath}" alt="${post.news_title}" onerror="this.src='/upload/no_image.jpg'">
                    <a href="/v2/news/details/${post.id}/${post.news_title_slug}" class="fb-image-overlay">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            `;
        }

        return `
            <article class="fb-post" data-post-id="${post.id}">
                <div class="fb-post-header">
                    <div class="fb-post-avatar">
                        <img src="${userPhotoPath}" alt="${post.user?.name || 'User'}" onerror="this.src='/upload/no_image.jpg'">
                    </div>
                    <div class="fb-post-info">
                        <h4 class="fb-post-author">${post.user?.name || 'Anonymous'}</h4>
                        <div class="fb-post-meta">
                            <span class="fb-post-time">${formatDate(post.created_at)}</span>
                            <span class="fb-post-separator">·</span>
                            <span class="fb-post-category">${post.category?.category_name || 'News'}</span>
                            <span class="fb-post-separator">·</span>
                            <i class="fas fa-globe-americas"></i>
                        </div>
                    </div>
                    <div class="fb-post-options">
                        <button class="fb-options-btn">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- Post Content -->
                <div class="fb-post-content">
                    <h3 class="fb-post-title">
                        <a href="/v2/news/details/${post.id}/${post.news_title_slug}">${post.news_title}</a>
                    </h3>
                    <p class="fb-post-excerpt">${post.news_details ? post.news_details.replace(/<[^>]*>/g, '').substring(0, 200) + '...' : ''}</p>
                </div>

                ${postImageHtml}
                <!-- Post Reactions Summary -->
                <div class="fb-post-reactions">
                    <div class="fb-reactions-summary">
                        <div class="fb-reactions-icons">
                            <span class="fb-reaction like">👍</span>
                            <span class="fb-reaction love">❤️</span>
                            <span class="fb-reaction wow">😮</span>
                        </div>
                        <span class="fb-reaction-count">${post.likes_count || 0}</span>
                    </div>
                    <div class="fb-post-engagement">
                        <span class="fb-comments-count">${post.comments_count || 0} comments</span>
                        <span class="fb-shares-count">${post.shares_count || 0} shares</span>
                        <span class="fb-views-count">${post.view_count || 0} views</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="fb-post-actions">
                    <button class="fb-action-btn fb-like-btn" data-news-id="${post.id}">
                        <i class="fas fa-thumbs-up"></i>
                        <span>Like</span>
                        <!-- Reaction Picker -->
                        <div class="fb-reaction-picker">
                            <div class="fb-reaction-option" data-reaction="like" title="Like">👍</div>
                            <div class="fb-reaction-option" data-reaction="love" title="Love">❤️</div>
                            <div class="fb-reaction-option" data-reaction="wow" title="Wow">😮</div>
                            <div class="fb-reaction-option" data-reaction="angry" title="Angry">😡</div>
                            <div class="fb-reaction-option" data-reaction="sad" title="Sad">😢</div>
                        </div>
                    </button>
                    <button class="fb-action-btn fb-comment-btn" data-news-id="${post.id}">
                        <i class="fas fa-comment"></i>
                        <span>Comment</span>
                    </button>
                    <button class="fb-action-btn fb-share-btn" data-news-id="${post.id}">
                        <i class="fas fa-share"></i>
                        <span>Share</span>
                    </button>
                    <button class="fb-action-btn fb-save-btn" data-news-id="${post.id}">
                        <i class="fas fa-bookmark"></i>
                        <span>Save</span>
                    </button>
                </div>

                <!-- Comments Section (Initially Hidden) -->
                <div class="fb-comments-section" id="comments-${post.id}" style="display: none;">
                    <!-- Write Comment -->
                    <div class="fb-write-comment">
                        <div class="fb-comment-avatar">
                            <img src="/upload/no_image.jpg" alt="User">
                        </div>
                        <div class="fb-comment-input-wrapper">
                            <form class="fb-comment-form" data-news-id="${post.id}">
                                <textarea class="fb-comment-input" placeholder="Write a comment..." rows="2"></textarea>
                                <div class="fb-comment-actions">
                                    <button type="button" class="fb-comment-emoji">😊</button>
                                    <button type="button" class="fb-comment-photo">📷</button>
                                    <button type="submit" class="fb-comment-submit">Post</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comments List -->
                    <div class="fb-comments-list" id="comments-list-${post.id}">
                        <div class="fb-no-comments">
                            <p>No comments yet. Be the first to comment!</p>
                        </div>
                    </div>
                </div>
            </article>
        `;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes}m ago`;
        if (hours < 24) return `${hours}h ago`;
        if (days < 7) return `${days}d ago`;
        return date.toLocaleDateString();
    }

    function showToast(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'nk-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--nk-gray-800);
            color: var(--nk-white);
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Filter Tabs Functionality
    function initializeFilterTabs() {
        const filterTabs = document.querySelectorAll('.fb-filter-tab');
        const newsContainer = document.querySelector('.fb-news-feed');

        if (!filterTabs.length || !newsContainer) return;

        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active tab
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // Show loading state
                newsContainer.style.opacity = '0.5';
                newsContainer.style.pointerEvents = 'none';

                // Filter posts based on selection
                filterPosts(filter).then(() => {
                    newsContainer.style.opacity = '1';
                    newsContainer.style.pointerEvents = 'auto';
                });
            });
        });
    }

    async function filterPosts(filter) {
        const currentUrl = window.location.href;
        const categoryId = currentUrl.match(/category\/(\d+)/)?.[1];

        if (!categoryId) return;

        try {
            const response = await fetch(`/api/category/${categoryId}/filter?type=${filter}`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                const data = await response.json();
                updateNewsContainer(data.posts);
            }
        } catch (error) {
            console.error('Filter error:', error);
            // Fallback: client-side filtering
            clientSideFilter(filter);
        }
    }

    function clientSideFilter(filter) {
        const posts = document.querySelectorAll('.fb-post');
        const postsArray = Array.from(posts);

        // Sort posts based on filter
        let sortedPosts;
        switch (filter) {
            case 'recent':
                sortedPosts = postsArray.sort((a, b) => {
                    const dateA = new Date(a.querySelector('.fb-post-time')?.textContent || 0);
                    const dateB = new Date(b.querySelector('.fb-post-time')?.textContent || 0);
                    return dateB - dateA;
                });
                break;
            case 'popular':
                sortedPosts = postsArray.sort((a, b) => {
                    const viewsA = parseInt(a.querySelector('.fb-post-stats')?.textContent?.match(/\d+/)?.[0] || 0);
                    const viewsB = parseInt(b.querySelector('.fb-post-stats')?.textContent?.match(/\d+/)?.[0] || 0);
                    return viewsB - viewsA;
                });
                break;
            case 'trending':
                sortedPosts = postsArray.sort((a, b) => {
                    const likesA = parseInt(a.querySelector('#likes-count')?.textContent || 0);
                    const likesB = parseInt(b.querySelector('#likes-count')?.textContent || 0);
                    return likesB - likesA;
                });
                break;
            default:
                sortedPosts = postsArray;
        }

        // Reorder posts in DOM
        const container = document.querySelector('.fb-news-feed');
        sortedPosts.forEach(post => container.appendChild(post));
    }

    function updateNewsContainer(posts) {
        const container = document.querySelector('.fb-news-feed');
        if (!container) return;

        // Clear existing posts
        container.innerHTML = '';

        // Add new posts
        posts.forEach(post => {
            const postHtml = createPostHtml(post);
            container.insertAdjacentHTML('beforeend', postHtml);
        });

        // Reinitialize post interactions
        initializePostInteractions();
    }

    // Follow Button Functionality
    function initializeFollowButtons() {
        const followButtons = document.querySelectorAll('.fb-follow-btn');

        followButtons.forEach(button => {
            button.addEventListener('click', function() {
                const isFollowing = this.classList.contains('following');
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                if (isFollowing) {
                    this.classList.remove('following');
                    icon.className = 'fas fa-plus';
                    text.textContent = 'Follow';
                    showToast('Unfollowed successfully');
                } else {
                    this.classList.add('following');
                    icon.className = 'fas fa-check';
                    text.textContent = 'Following';
                    showToast('Following successfully');
                }

                // Send follow/unfollow request
                const categoryId = window.location.href.match(/category\/(\d+)/)?.[1];
                if (categoryId) {
                    fetch('/api/category/follow', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                        },
                        body: JSON.stringify({
                            category_id: categoryId,
                            action: isFollowing ? 'unfollow' : 'follow'
                        })
                    }).catch(error => console.error('Follow error:', error));
                }
            });
        });
    }

    // Sidebar Scroll Effects
    function initializeSidebarScrollEffects() {
        const leftSidebar = document.querySelector('.fb-left-sidebar');
        const rightSidebar = document.querySelector('.fb-right-sidebar');

        if (leftSidebar) {
            setupSidebarScrollEffect(leftSidebar);
        }

        if (rightSidebar) {
            setupSidebarScrollEffect(rightSidebar);
        }
    }

    function setupSidebarScrollEffect(sidebar) {
        let scrollTimeout;

        // Add scroll indicator
        const scrollIndicator = document.createElement('div');
        scrollIndicator.className = 'fb-scroll-indicator';
        scrollIndicator.innerHTML = '<i class="fas fa-mouse"></i> Scroll to see more';
        scrollIndicator.style.cssText = `
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--nk-primary);
            color: var(--nk-white);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 10;
        `;

        // Check if sidebar is scrollable
        function checkScrollable() {
            const isScrollable = sidebar.scrollHeight > sidebar.clientHeight;
            if (isScrollable && sidebar.scrollTop === 0) {
                scrollIndicator.style.opacity = '0.7';
                setTimeout(() => {
                    scrollIndicator.style.opacity = '0';
                }, 3000);
            }
        }

        sidebar.style.position = 'relative';
        sidebar.appendChild(scrollIndicator);

        // Check scrollable on load
        setTimeout(checkScrollable, 1000);

        // Enhanced hover effects
        sidebar.addEventListener('mouseenter', function() {
            this.classList.add('sidebar-hover');
            clearTimeout(scrollTimeout);
        });

        // Hide scrollbar when not hovering
        sidebar.addEventListener('mouseleave', function() {
            this.classList.remove('sidebar-hover');
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                // Hide scroll indicator
                scrollIndicator.style.opacity = '0';
            }, 500);
        });

        // Simplified smooth scroll behavior - let browser handle most of the work
        // scrollTimeout already declared above

        sidebar.addEventListener('wheel', function(e) {
            // Only enhance scroll if sidebar is actually scrollable
            if (this.scrollHeight > this.clientHeight) {
                // Don't prevent default - let browser handle the scrolling
                // Just add smooth visual enhancements

                clearTimeout(scrollTimeout);

                // Add scrolling class for visual feedback
                this.classList.add('is-scrolling');

                // Remove scrolling class after scroll ends
                scrollTimeout = setTimeout(() => {
                    this.classList.remove('is-scrolling');
                }, 150);

                // Hide scroll indicator when user starts scrolling
                scrollIndicator.style.opacity = '0';
            }
        }, { passive: true }); // Use passive for better performance

        // Handle touch scrolling for mobile devices
        let touchStartY = 0;
        let touchEndY = 0;

        sidebar.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
            this.classList.add('is-scrolling');
        }, { passive: true });

        sidebar.addEventListener('touchmove', function(e) {
            touchEndY = e.touches[0].clientY;
            // Hide scroll indicator during touch scroll
            scrollIndicator.style.opacity = '0';
        }, { passive: true });

        sidebar.addEventListener('touchend', function(e) {
            setTimeout(() => {
                this.classList.remove('is-scrolling');
            }, 150);
        }, { passive: true });

        // Track scroll position
        sidebar.addEventListener('scroll', function() {
            const scrollPercentage = (this.scrollTop / (this.scrollHeight - this.clientHeight)) * 100;

            // Add scroll shadow effect
            if (this.scrollTop > 10) {
                this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.1), ' + getComputedStyle(this).boxShadow;
            } else {
                this.style.boxShadow = getComputedStyle(this).boxShadow.replace('inset 0 10px 10px -10px rgba(0,0,0,0.1), ', '');
            }
        });
    }

    // Comment Form Functionality
    function initializeCommentForms() {
        // Handle both specific forms and general comment forms
        const commentForms = document.querySelectorAll('.fb-comment-form, #comment-form');

        commentForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const newsId = this.getAttribute('data-news-id');
                const commentInput = this.querySelector('.fb-comment-input');
                const comment = commentInput.value.trim();

                if (!comment) {
                    showToast('Please enter a comment');
                    return;
                }

                // Show loading state
                const submitBtn = this.querySelector('.fb-comment-submit');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Posting...';
                submitBtn.disabled = true;

                // Submit comment
                fetch('/api/news/comment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    body: JSON.stringify({
                        news_post_id: newsId,
                        comment: comment
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message || 'Comment submitted successfully');
                        commentInput.value = '';

                        // Update comment count
                        const commentCount = document.querySelector('#comments-count');
                        if (commentCount) {
                            const currentCount = parseInt(commentCount.textContent.match(/\d+/)?.[0] || 0);
                            commentCount.textContent = `${currentCount + 1} comments`;
                        }

                        // Add comment to list if approved immediately
                        if (data.comment) {
                            addCommentToList(data.comment);
                        }
                    } else {
                        showToast(data.message || 'Failed to submit comment');
                    }
                })
                .catch(error => {
                    console.error('Comment submission error:', error);
                    showToast('Failed to submit comment. Please try again.');
                })
                .finally(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            });
        });
    }

    function addCommentToList(comment) {
        const commentsList = document.querySelector('#comments-list');
        const noComments = document.querySelector('.fb-no-comments');

        if (noComments) {
            noComments.remove();
        }

        const commentHtml = `
            <div class="fb-comment" data-comment-id="${comment.id}">
                <div class="fb-comment-avatar">
                    <img src="${comment.author_avatar}" alt="${comment.author_name}">
                </div>
                <div class="fb-comment-content">
                    <div class="fb-comment-bubble">
                        <h5 class="fb-comment-author">${comment.author_name}</h5>
                        <p class="fb-comment-text">${comment.comment}</p>
                    </div>
                    <div class="fb-comment-meta">
                        <button class="fb-comment-like" data-comment-id="${comment.id}">Like</button>
                        <button class="fb-comment-reply" data-comment-id="${comment.id}">Reply</button>
                        <span class="fb-comment-time">Just now</span>
                        <span class="fb-comment-likes">👍 0</span>
                    </div>
                </div>
            </div>
        `;

        if (commentsList) {
            commentsList.insertAdjacentHTML('afterbegin', commentHtml);
        }
    }

    // Reaction Picker Functionality
    function initializeReactionPickers() {
        // Handle reaction picker hover
        const likeButtons = document.querySelectorAll('.fb-like-btn');

        likeButtons.forEach(button => {
            const picker = button.querySelector('.fb-reaction-picker');
            if (!picker) return;

            let hoverTimeout;

            // Show picker on hover
            button.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
                picker.style.opacity = '1';
                picker.style.visibility = 'visible';
                picker.style.transform = 'translateX(-50%) translateY(-5px)';
            });

            // Hide picker when leaving
            button.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    picker.style.opacity = '0';
                    picker.style.visibility = 'hidden';
                    picker.style.transform = 'translateX(-50%) translateY(0)';
                }, 300);
            });

            // Keep picker visible when hovering over it
            picker.addEventListener('mouseenter', function() {
                clearTimeout(hoverTimeout);
            });

            picker.addEventListener('mouseleave', function() {
                hoverTimeout = setTimeout(() => {
                    picker.style.opacity = '0';
                    picker.style.visibility = 'hidden';
                    picker.style.transform = 'translateX(-50%) translateY(0)';
                }, 100);
            });
        });

        // Handle reaction selection
        document.addEventListener('click', function(e) {
            if (e.target.closest('.fb-reaction-option')) {
                const reactionOption = e.target.closest('.fb-reaction-option');
                const reaction = reactionOption.getAttribute('data-reaction');
                const likeButton = reactionOption.closest('.fb-like-btn');
                const postId = likeButton.getAttribute('data-news-id');

                handleReaction(postId, reaction, likeButton);
            }
        });
    }

    function handleReaction(postId, reaction, button) {
        const icon = button.querySelector('i');
        const text = button.querySelector('span');
        const picker = button.querySelector('.fb-reaction-picker');

        // Update button appearance immediately
        const reactionEmojis = {
            'like': '👍',
            'love': '❤️',
            'wow': '😮',
            'angry': '😡',
            'sad': '😢'
        };

        const reactionColors = {
            'like': '#1877f2',
            'love': '#e91e63',
            'wow': '#ffc107',
            'angry': '#f44336',
            'sad': '#ff9800'
        };

        // Update button state
        button.classList.add('reacted');
        button.style.color = reactionColors[reaction];
        text.textContent = reaction.charAt(0).toUpperCase() + reaction.slice(1);

        // Add reaction emoji to button
        if (!button.querySelector('.reaction-emoji')) {
            const emojiSpan = document.createElement('span');
            emojiSpan.className = 'reaction-emoji';
            emojiSpan.textContent = reactionEmojis[reaction];
            button.insertBefore(emojiSpan, icon);
        } else {
            button.querySelector('.reaction-emoji').textContent = reactionEmojis[reaction];
        }

        // Hide picker
        picker.style.opacity = '0';
        picker.style.visibility = 'hidden';

        // Send reaction to server
        fetch('/api/news/react', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify({
                news_post_id: postId,
                reaction: reaction
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update reaction counts
                updateReactionCounts(postId, data.reactions);
            }
        })
        .catch(error => {
            console.error('Reaction error:', error);
            // Revert on error
            button.classList.remove('reacted');
            button.style.color = '';
            text.textContent = 'Like';
            const emojiSpan = button.querySelector('.reaction-emoji');
            if (emojiSpan) emojiSpan.remove();
        });
    }

    function updateReactionCounts(postId, reactions) {
        const reactionsContainer = document.querySelector(`[data-post-id="${postId}"] .fb-reactions-summary`);
        if (!reactionsContainer) return;

        const iconsContainer = reactionsContainer.querySelector('.fb-reactions-icons');
        const countElement = reactionsContainer.querySelector('.fb-reaction-count');

        // Update reaction icons
        iconsContainer.innerHTML = '';
        let totalCount = 0;

        Object.entries(reactions).forEach(([reaction, count]) => {
            if (count > 0) {
                const reactionEmojis = {
                    'like': '👍',
                    'love': '❤️',
                    'wow': '😮',
                    'angry': '😡',
                    'sad': '😢'
                };

                const span = document.createElement('span');
                span.className = `fb-reaction ${reaction}`;
                span.textContent = reactionEmojis[reaction];
                iconsContainer.appendChild(span);

                totalCount += count;
            }
        });

        // Update total count
        countElement.textContent = totalCount;
    }

    // Story Carousel Functionality
    function initializeStoryCarousel() {
        const storyCards = document.querySelectorAll('.fb-story-card');

        storyCards.forEach(card => {
            // Make entire card clickable
            card.addEventListener('click', function(e) {
                e.preventDefault();

                const storyLink = this.querySelector('.fb-story-link');
                if (storyLink) {
                    const href = storyLink.getAttribute('href');
                    if (href) {
                        window.location.href = href;
                    }
                }
            });

            // Add hover effects
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.02)';
                this.style.boxShadow = '0 12px 30px rgba(102, 126, 234, 0.2)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            });
        });

        // Initialize horizontal scrolling for stories
        const storiesWrapper = document.querySelector('.fb-stories-wrapper');
        if (storiesWrapper) {
            let isScrolling = false;

            storiesWrapper.addEventListener('wheel', function(e) {
                if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
                    e.preventDefault();
                    this.scrollLeft += e.deltaY;
                }
            }, { passive: false });

            // Add scroll buttons if needed
            addScrollButtons(storiesWrapper);
        }
    }

    function addScrollButtons(container) {
        const wrapper = container.parentElement;

        // Create scroll buttons
        const leftBtn = document.createElement('button');
        leftBtn.className = 'fb-scroll-btn fb-scroll-left';
        leftBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';

        const rightBtn = document.createElement('button');
        rightBtn.className = 'fb-scroll-btn fb-scroll-right';
        rightBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';

        // Add buttons to wrapper
        wrapper.style.position = 'relative';
        wrapper.appendChild(leftBtn);
        wrapper.appendChild(rightBtn);

        // Button functionality
        leftBtn.addEventListener('click', () => {
            container.scrollBy({ left: -200, behavior: 'smooth' });
        });

        rightBtn.addEventListener('click', () => {
            container.scrollBy({ left: 200, behavior: 'smooth' });
        });

        // Show/hide buttons based on scroll position
        function updateButtons() {
            leftBtn.style.display = container.scrollLeft > 0 ? 'flex' : 'none';
            rightBtn.style.display =
                container.scrollLeft < container.scrollWidth - container.clientWidth ? 'flex' : 'none';
        }

        container.addEventListener('scroll', updateButtons);
        updateButtons();
    }

    // Image Error Handling
    function initializeImageErrorHandling() {
        // Handle existing images
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('onerror')) {
                img.setAttribute('onerror', "this.src='/upload/no_image.jpg'; this.onerror=null;");
            }
        });

        // Handle dynamically added images
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => {
                            if (!img.hasAttribute('onerror')) {
                                img.setAttribute('onerror', "this.src='/upload/no_image.jpg'; this.onerror=null;");
                            }
                        });

                        // If the node itself is an image
                        if (node.tagName === 'IMG' && !node.hasAttribute('onerror')) {
                            node.setAttribute('onerror', "this.src='/upload/no_image.jpg'; this.onerror=null;");
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Global error handler for images
        document.addEventListener('error', function(e) {
            if (e.target.tagName === 'IMG') {
                console.log('Image failed to load:', e.target.src);
                if (e.target.src !== '/upload/no_image.jpg') {
                    e.target.src = '/upload/no_image.jpg';
                }
            }
        }, true);
    }

})();
