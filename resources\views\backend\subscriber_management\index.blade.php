@extends('admin.admin_management_dashboard')
@section('admin')

<div class="page-content">
    <!--breadcrumb-->
    <div class="page-breadcrumb d-none d-sm-flex align-items-center mb-3">
        <div class="breadcrumb-title pe-3">Subscriber Management</div>
        <div class="ps-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0 p-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="bx bx-home-alt"></i></a></li>
                    <li class="breadcrumb-item active" aria-current="page">All Subscribers</li>
                </ol>
            </nav>
        </div>
        <div class="ms-auto">
            <div class="btn-group">
                <a href="{{ route('admin.subscribers.create') }}" class="btn btn-primary">
                    <i class="mdi mdi-plus"></i> Add New Subscriber
                </a>
            </div>
        </div>
    </div>
    <!--end breadcrumb-->

    <div class="card">
        <div class="card-body">
            <div class="d-lg-flex align-items-center mb-4 gap-3">
                <div class="position-relative">
                    <input type="text" class="form-control ps-5 radius-30" placeholder="Search Subscribers..." id="searchInput">
                    <span class="position-absolute top-50 product-show translate-middle-y"><i class="mdi mdi-magnify"></i></span>
                </div>
                <div class="ms-auto">
                    <form id="bulkActionForm" method="POST" action="{{ route('admin.subscribers.bulk-action') }}">
                        @csrf
                        <div class="d-flex gap-2">
                            <select name="action" class="form-select" required>
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button type="submit" class="btn btn-outline-secondary">Apply</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table mb-0" id="subscribersTable">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Subscriber Info</th>
                            <th>Contact</th>
                            <th>Posts Statistics</th>
                            <th>Status</th>
                            <th>Joined Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($subscribers as $subscriber)
                        <tr>
                            <td>
                                <input type="checkbox" name="subscriber_ids[]" value="{{ $subscriber->id }}" class="form-check-input subscriber-checkbox">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="recent-product-img">
                                        <img src="{{ (!empty($subscriber->photo)) ? url($subscriber->photo) : url('upload/no_image.jpg') }}"
                                             alt="{{ $subscriber->name }}" class="rounded-circle" width="40" height="40">
                                    </div>
                                    <div class="ms-2">
                                        <h6 class="mb-1 font-14">{{ $subscriber->name }}</h6>
                                        <p class="mb-0 font-13 text-secondary">{{ $subscriber->email }}</p>
                                        @if($subscriber->username)
                                            <small class="text-muted">@{{ $subscriber->username }}</small>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="font-13">
                                    @if($subscriber->phone)
                                        <div><i class="mdi mdi-phone"></i> {{ $subscriber->phone }}</div>
                                    @endif
                                    @if($subscriber->address)
                                        <div><i class="mdi mdi-map-marker"></i> {{ Str::limit($subscriber->address, 30) }}</div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="font-13">
                                    <div class="text-primary"><strong>{{ $subscriber->total_posts }}</strong> Total</div>
                                    <div class="text-success">{{ $subscriber->approved_posts }} Approved</div>
                                    <div class="text-warning">{{ $subscriber->pending_posts }} Pending</div>
                                    <div class="text-danger">{{ $subscriber->rejected_posts }} Rejected</div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center gap-2">
                                    <span class="badge {{ $subscriber->status === 'active' ? 'bg-success' : 'bg-secondary' }}">
                                        {{ ucfirst($subscriber->status) }}
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="font-13">
                                    {{ $subscriber->created_at->format('M d, Y') }}
                                    <br>
                                    <small class="text-muted">{{ $subscriber->created_at->diffForHumans() }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex order-actions gap-1">
                                    <a href="{{ route('admin.subscribers.show', $subscriber->id) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="mdi mdi-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.subscribers.edit', $subscriber->id) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                        <i class="mdi mdi-pencil"></i>
                                    </a>
                                    <form method="POST" action="{{ route('admin.subscribers.toggle-status', $subscriber->id) }}" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-sm {{ $subscriber->status === 'active' ? 'btn-outline-secondary' : 'btn-outline-success' }}"
                                                title="{{ $subscriber->status === 'active' ? 'Deactivate' : 'Activate' }}">
                                            <i class="mdi {{ $subscriber->status === 'active' ? 'mdi-pause' : 'mdi-play' }}"></i>
                                        </button>
                                    </form>
                                    <form method="POST" action="{{ route('admin.subscribers.destroy', $subscriber->id) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this subscriber?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="mdi mdi-delete"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="mdi mdi-account-off display-4 text-muted mb-2"></i>
                                    <h6 class="text-muted">No subscribers found</h6>
                                    <p class="text-muted">Get started by creating your first subscriber.</p>
                                    <a href="{{ route('admin.subscribers.create') }}" class="btn btn-primary">
                                        <i class="mdi mdi-plus"></i> Add New Subscriber
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($subscribers->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $subscribers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#subscribersTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Select all functionality
    $('#selectAll').change(function() {
        $('.subscriber-checkbox').prop('checked', this.checked);
        updateBulkActionForm();
    });

    $('.subscriber-checkbox').change(function() {
        updateBulkActionForm();
        
        // Update select all checkbox
        var total = $('.subscriber-checkbox').length;
        var checked = $('.subscriber-checkbox:checked').length;
        $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
        $('#selectAll').prop('checked', checked === total);
    });

    function updateBulkActionForm() {
        var checkedBoxes = $('.subscriber-checkbox:checked');
        $('#bulkActionForm input[name="subscriber_ids[]"]').remove();
        
        checkedBoxes.each(function() {
            $('#bulkActionForm').append('<input type="hidden" name="subscriber_ids[]" value="' + $(this).val() + '">');
        });
    }

    // Bulk action form submission
    $('#bulkActionForm').submit(function(e) {
        var checkedBoxes = $('.subscriber-checkbox:checked');
        if (checkedBoxes.length === 0) {
            e.preventDefault();
            alert('Please select at least one subscriber.');
            return false;
        }

        var action = $('select[name="action"]').val();
        if (!action) {
            e.preventDefault();
            alert('Please select an action.');
            return false;
        }

        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected subscribers? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
@endpush

@endsection
