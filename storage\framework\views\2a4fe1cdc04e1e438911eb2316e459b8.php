<?php $__env->startSection('admin'); ?>

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <a href="<?php echo e(route('admin.sponsored-ads.index')); ?>" class="btn btn-secondary">
                                <i class="mdi mdi-arrow-left"></i> Back to List
                            </a>
                            <a href="<?php echo e(route('admin.sponsored-ads.edit', $sponsoredAd->id)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit
                            </a>
                            <form method="POST" action="<?php echo e(route('admin.sponsored-ads.toggle-status', $sponsoredAd->id)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn <?php echo e($sponsoredAd->is_active ? 'btn-secondary' : 'btn-success'); ?>">
                                    <i class="mdi <?php echo e($sponsoredAd->is_active ? 'mdi-pause' : 'mdi-play'); ?>"></i>
                                    <?php echo e($sponsoredAd->is_active ? 'Deactivate' : 'Activate'); ?>

                                </button>
                            </form>
                        </ol>
                    </div>
                    <h4 class="page-title">Sponsored Ad Details</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <div class="row">
            <!-- Sponsored Ad Info -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><?php echo e($sponsoredAd->title); ?></h5>
                        <div class="d-flex gap-2 mt-2">
                            <span class="badge bg-<?php echo e($sponsoredAd->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($sponsoredAd->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                            <?php if($sponsoredAd->is_premium): ?>
                                <span class="badge bg-warning">
                                    <i class="mdi mdi-crown"></i> Premium
                                </span>
                            <?php endif; ?>
                            <span class="badge bg-primary"><?php echo e(ucfirst($sponsoredAd->ad_format)); ?></span>
                            <span class="badge bg-info"><?php echo e(ucfirst(str_replace('_', ' ', $sponsoredAd->placement))); ?></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if($sponsoredAd->image): ?>
                            <div class="mb-4">
                                <img src="<?php echo e(asset($sponsoredAd->image)); ?>" alt="<?php echo e($sponsoredAd->title); ?>" 
                                     class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <h6>Content:</h6>
                            <div class="text-muted"><?php echo e($sponsoredAd->content); ?></div>
                        </div>

                        <div class="mb-3">
                            <h6>Sponsor:</h6>
                            <div class="d-flex align-items-center">
                                <?php if($sponsoredAd->sponsor_logo): ?>
                                    <img src="<?php echo e(asset($sponsoredAd->sponsor_logo)); ?>" alt="<?php echo e($sponsoredAd->sponsor_name); ?>" 
                                         class="rounded me-2" width="40" height="40" style="object-fit: cover;">
                                <?php endif; ?>
                                <span class="fw-bold"><?php echo e($sponsoredAd->sponsor_name); ?></span>
                            </div>
                        </div>

                        <?php if($sponsoredAd->link_url): ?>
                            <div class="mb-3">
                                <h6>Link URL:</h6>
                                <a href="<?php echo e($sponsoredAd->link_url); ?>" target="_blank" class="text-primary">
                                    <?php echo e($sponsoredAd->link_url); ?> <i class="mdi mdi-open-in-new"></i>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Ad Format:</h6>
                                <p class="text-muted"><?php echo e(ucfirst($sponsoredAd->ad_format)); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Placement:</h6>
                                <p class="text-muted"><?php echo e(ucfirst(str_replace('_', ' ', $sponsoredAd->placement))); ?></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Display Order:</h6>
                                <p class="text-muted"><?php echo e($sponsoredAd->display_order); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Status:</h6>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-<?php echo e($sponsoredAd->is_active ? 'success' : 'secondary'); ?>">
                                        <?php echo e($sponsoredAd->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                    <?php if($sponsoredAd->is_premium): ?>
                                        <span class="badge bg-warning">Premium</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <?php if($sponsoredAd->budget): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Budget:</h6>
                                    <p class="text-muted">$<?php echo e(number_format($sponsoredAd->budget, 2)); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($sponsoredAd->start_date || $sponsoredAd->end_date): ?>
                            <div class="row">
                                <?php if($sponsoredAd->start_date): ?>
                                <div class="col-md-6">
                                    <h6>Start Date:</h6>
                                    <p class="text-muted"><?php echo e($sponsoredAd->start_date->format('F d, Y')); ?></p>
                                </div>
                                <?php endif; ?>
                                <?php if($sponsoredAd->end_date): ?>
                                <div class="col-md-6">
                                    <h6>End Date:</h6>
                                    <p class="text-muted"><?php echo e($sponsoredAd->end_date->format('F d, Y')); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if($sponsoredAd->target_demographics && count($sponsoredAd->target_demographics) > 0): ?>
                            <div class="mb-3">
                                <h6>Target Demographics:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $sponsoredAd->target_demographics; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $demographic): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-light text-dark"><?php echo e(ucfirst($demographic)); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($sponsoredAd->target_categories && count($sponsoredAd->target_categories) > 0): ?>
                            <div class="mb-3">
                                <h6>Target Categories:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $sponsoredAd->target_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $categoryId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $category = \App\Models\Category::find($categoryId);
                                        ?>
                                        <?php if($category): ?>
                                            <span class="badge bg-light text-dark"><?php echo e($category->category_name); ?></span>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics & Info -->
            <div class="col-md-4">
                <!-- Performance Stats -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Performance Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e(number_format($sponsoredAd->view_count)); ?></h4>
                                <small class="text-muted">Total Views</small>
                            </div>
                            <div class="text-primary">
                                <i class="mdi mdi-eye" style="font-size: 2rem;"></i>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e(number_format($sponsoredAd->click_count)); ?></h4>
                                <small class="text-muted">Total Clicks</small>
                            </div>
                            <div class="text-success">
                                <i class="mdi mdi-cursor-pointer" style="font-size: 2rem;"></i>
                            </div>
                        </div>

                        <?php if($sponsoredAd->view_count > 0): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0"><?php echo e($sponsoredAd->click_through_rate); ?>%</h4>
                                <small class="text-muted">Click-Through Rate</small>
                            </div>
                            <div class="text-warning">
                                <i class="mdi mdi-chart-line" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if($sponsoredAd->budget): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">$<?php echo e(number_format($sponsoredAd->budget, 2)); ?></h4>
                                <small class="text-muted">Budget</small>
                            </div>
                            <div class="text-info">
                                <i class="mdi mdi-currency-usd" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="progress mb-2">
                            <?php
                                $ctr = $sponsoredAd->view_count > 0 ? ($sponsoredAd->click_count / $sponsoredAd->view_count) * 100 : 0;
                                $progressColor = $ctr > 5 ? 'success' : ($ctr > 2 ? 'warning' : 'danger');
                            ?>
                            <div class="progress-bar bg-<?php echo e($progressColor); ?>" role="progressbar" style="width: <?php echo e(min($ctr * 10, 100)); ?>%"></div>
                        </div>
                        <small class="text-muted">Performance indicator</small>
                    </div>
                </div>

                <!-- Sponsored Ad Info -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Ad Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-calendar me-2 text-muted"></i>
                            <span>Created <?php echo e($sponsoredAd->created_at->format('F d, Y')); ?></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="mdi mdi-clock me-2 text-muted"></i>
                            <span>Last updated <?php echo e($sponsoredAd->updated_at->diffForHumans()); ?></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="mdi mdi-identifier me-2 text-muted"></i>
                            <span>ID: <?php echo e($sponsoredAd->id); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.sponsored-ads.edit', $sponsoredAd->id)); ?>" class="btn btn-warning">
                                <i class="mdi mdi-pencil"></i> Edit Sponsored Ad
                            </a>
                            
                            <form method="POST" action="<?php echo e(route('admin.sponsored-ads.toggle-status', $sponsoredAd->id)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn <?php echo e($sponsoredAd->is_active ? 'btn-secondary' : 'btn-success'); ?> w-100">
                                    <i class="mdi <?php echo e($sponsoredAd->is_active ? 'mdi-pause' : 'mdi-play'); ?>"></i>
                                    <?php echo e($sponsoredAd->is_active ? 'Deactivate' : 'Activate'); ?>

                                </button>
                            </form>

                            <form method="POST" action="<?php echo e(route('admin.sponsored-ads.toggle-premium', $sponsoredAd->id)); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn <?php echo e($sponsoredAd->is_premium ? 'btn-outline-warning' : 'btn-warning'); ?> w-100">
                                    <i class="mdi mdi-crown"></i>
                                    <?php echo e($sponsoredAd->is_premium ? 'Remove Premium' : 'Mark Premium'); ?>

                                </button>
                            </form>

                            <form method="POST" action="<?php echo e(route('admin.sponsored-ads.destroy', $sponsoredAd->id)); ?>" 
                                  onsubmit="return confirm('Are you sure you want to delete this sponsored ad?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="mdi mdi-delete"></i> Delete Sponsored Ad
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_management_dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/backend/sponsored_ads/show.blade.php ENDPATH**/ ?>