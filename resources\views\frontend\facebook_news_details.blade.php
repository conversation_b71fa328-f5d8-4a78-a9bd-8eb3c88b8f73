@extends('frontend.layout_facebook_style')

@section('title')
{{ $news->news_title }} - <PERSON><PERSON><PERSON><PERSON><PERSON> (Facebook Style)
@endsection

@section('content')

<!-- Facebook-style Main Container -->
<div class="fb-main-container">
    <!-- Left Sidebar -->
    <div class="fb-left-sidebar">
        <!-- User Profile Card -->
        <div class="fb-profile-card">
            <div class="fb-profile-avatar">
                <img src="{{ asset('frontend/assets/images/logo.png') }}" alt="NitiKotha">
            </div>
            <div class="fb-profile-info">
                <h3 class="fb-profile-name">NitiKotha</h3>
                <p class="fb-profile-tagline">Premium News Portal</p>
            </div>
        </div>

        <!-- Quick Navigation -->
        <div class="fb-nav-menu">
            <a href="{{ url('/v2') }}" class="fb-nav-item">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="{{ url('v2/news/category/'.$news->category->id.'/'.$news->category->category_slug) }}" class="fb-nav-item">
                <i class="fas fa-fire"></i>
                <span>{{ $news->category->category_name }}</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-bookmark"></i>
                <span>Saved</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-users"></i>
                <span>Groups</span>
            </a>
            <a href="#" class="fb-nav-item">
                <i class="fas fa-video"></i>
                <span>Watch</span>
            </a>
        </div>

        <!-- Categories Section -->
        <div class="fb-categories-section">
            <h4 class="fb-section-title">All Categories</h4>
            <div class="fb-categories-list">
                @if(isset($categories))
                    @foreach($categories as $category)
                    <div class="fb-category-group">
                        <div class="fb-category-item fb-main-category {{ $category->id == $news->category->id ? 'active' : '' }}" data-category-id="{{ $category->id }}">
                            <div class="fb-category-left">
                                <div class="fb-category-icon">{{ $category->category_icon }}</div>
                                <span class="fb-category-name">{{ $category->category_name }}</span>
                            </div>
                            <div class="fb-category-right">
                                <span class="fb-category-count">{{ $category->news_posts_count ?? 0 }}</span>
                                @if($category->subcategories && $category->subcategories->count() > 0)
                                    <button class="fb-category-toggle" data-target="subcats-{{ $category->id }}">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                @endif
                            </div>
                        </div>

                        <!-- Category Link -->
                        <a href="{{ url('v2/news/category/'.$category->id.'/'.$category->category_slug) }}" class="fb-category-link"></a>

                        <!-- Subcategories -->
                        @if($category->subcategories && $category->subcategories->count() > 0)
                        <div class="fb-subcategories {{ $category->id == $news->category->id ? 'expanded' : '' }}" id="subcats-{{ $category->id }}">
                            @foreach($category->subcategories as $subcategory)
                            <a href="{{ url('v2/news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug) }}" class="fb-subcategory-item {{ isset($news->subcategory) && $subcategory->id == $news->subcategory->id ? 'active' : '' }}">
                                <div class="fb-subcategory-icon">📄</div>
                                <span class="fb-subcategory-name">{{ $subcategory->subcategory_name }}</span>
                                <span class="fb-subcategory-count">{{ $subcategory->news_posts_count ?? 0 }}</span>
                            </a>
                            @endforeach
                        </div>
                        @endif
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Footer Links -->
        <div class="fb-footer-links">
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">About</a>
            <a href="#">Help</a>
        </div>
    </div>

    <!-- Main Feed -->
    <div class="fb-main-feed">
        <!-- News Article Post -->
        <article class="fb-post fb-article-post">
            <!-- Post Header -->
            <div class="fb-post-header">
                <div class="fb-post-avatar">
                    @php
                        $photoPath = 'upload/no_image.jpg';
                        if (!empty($news->user->photo)) {
                            // Check if user is admin or subscriber
                            $isSubscriber = $news->user->hasRole('Subscriber');
                            if ($isSubscriber) {
                                // Subscriber photos are stored with full path
                                $photoPath = $news->user->photo;
                            } else {
                                // Admin photos need path prefix
                                $photoPath = 'upload/admin_images/' . $news->user->photo;
                            }
                        }
                    @endphp
                    <img src="{{ url($photoPath) }}" alt="{{ $news->user->name }}">
                </div>
                <div class="fb-post-info">
                    <h4 class="fb-post-author">{{ $news->user->name ?? 'NitiKotha' }}</h4>
                    <div class="fb-post-meta">
                        <span class="fb-post-time">{{ $news->created_at->diffForHumans() }}</span>
                        <span class="fb-post-separator">·</span>
                        <a href="{{ url('v2/news/category/'.$news->category->id.'/'.$news->category->category_slug) }}" class="fb-post-category">{{ $news->category->category_name }}</a>
                        <span class="fb-post-separator">·</span>
                        <i class="fas fa-globe-americas"></i>
                    </div>
                </div>
                <div class="fb-post-options">
                    <button class="fb-options-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <!-- Article Title -->
            <div class="fb-article-title">
                <h1>{{ $news->news_title }}</h1>
            </div>

            <!-- Article Image -->
            @if($news->image)
            <div class="fb-article-image">
                <img src="{{ asset($news->image) }}" alt="{{ $news->news_title }}">
            </div>
            @endif

            <!-- Article Content -->
            <div class="fb-article-content">
                {!! $news->news_details !!}
            </div>

            <!-- Article Tags -->
            @if($news->tags)
            <div class="fb-article-tags">
                @foreach(explode(',', $news->tags) as $tag)
                <span class="fb-tag">#{{ trim($tag) }}</span>
                @endforeach
            </div>
            @endif

            <!-- Post Stats -->
            <div class="fb-post-stats">
                <div class="fb-post-reactions">
                    <div class="fb-reaction-icons">
                        <span class="fb-reaction like">👍</span>
                        <span class="fb-reaction love">❤️</span>
                        <span class="fb-reaction wow">😮</span>
                        <span class="fb-reaction angry">😡</span>
                        <span class="fb-reaction sad">😢</span>
                    </div>
                    <span class="fb-reaction-count" id="likes-count">{{ $likesCount }}</span>
                </div>
                <div class="fb-post-engagement">
                    <span class="fb-comments-count" id="comments-count">{{ $commentsCount }} comments</span>
                    <span class="fb-shares-count" id="shares-count">{{ $sharesCount }} shares</span>
                    <span class="fb-views-count">{{ $news->view_count ?? 0 }} views</span>
                </div>
            </div>

            <!-- Post Actions -->
            <div class="fb-post-actions">
                <button class="fb-action-btn fb-like-btn {{ $userLiked ? 'liked' : '' }}" data-news-id="{{ $news->id }}">
                    <i class="fas fa-thumbs-up"></i>
                    <span>{{ $userLiked ? 'Liked' : 'Like' }}</span>
                </button>
                <button class="fb-action-btn fb-comment-btn" data-action="comment">
                    <i class="fas fa-comment"></i>
                    <span>Comment</span>
                </button>
                <button class="fb-action-btn fb-share-btn" data-news-id="{{ $news->id }}">
                    <i class="fas fa-share"></i>
                    <span>Share</span>
                </button>
                <button class="fb-action-btn fb-save-btn {{ $userSaved ? 'saved' : '' }}" data-news-id="{{ $news->id }}">
                    <i class="fas fa-bookmark"></i>
                    <span>{{ $userSaved ? 'Saved' : 'Save' }}</span>
                </button>
            </div>

            <!-- Comments Section -->
            <div class="fb-comments-section">
                <!-- Write Comment -->
                <div class="fb-write-comment">
                    <div class="fb-comment-avatar">
                        @auth
                            @php
                                $userPhotoPath = 'upload/no_image.jpg';
                                if (!empty(auth()->user()->photo)) {
                                    // Check if user is admin or subscriber
                                    $isSubscriber = auth()->user()->hasRole('Subscriber');
                                    if ($isSubscriber) {
                                        // Subscriber photos are stored with full path
                                        $userPhotoPath = auth()->user()->photo;
                                    } else {
                                        // Admin photos need path prefix
                                        $userPhotoPath = 'upload/admin_images/' . auth()->user()->photo;
                                    }
                                }
                            @endphp
                            <img src="{{ url($userPhotoPath) }}" alt="You">
                        @else
                            <img src="{{ asset('frontend/assets/images/logo.png') }}" alt="Guest">
                        @endauth
                    </div>
                    <div class="fb-comment-input-wrapper">
                        <form id="comment-form" data-news-id="{{ $news->id }}">
                            @csrf
                            <input type="text" class="fb-comment-input" name="comment" placeholder="Write a comment..." required>
                            @guest
                                <input type="hidden" name="guest_name" value="Guest User">
                                <input type="hidden" name="guest_email" value="<EMAIL>">
                            @endguest
                            <div class="fb-comment-actions">
                                <button type="button" class="fb-comment-emoji">😊</button>
                                <button type="button" class="fb-comment-photo">📷</button>
                                <button type="submit" class="fb-comment-submit">Post</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Real Comments -->
                <div class="fb-comments-list" id="comments-list">
                    @if($comments && $comments->count() > 0)
                        @foreach($comments as $comment)
                        <div class="fb-comment" data-comment-id="{{ $comment->id }}">
                            <div class="fb-comment-avatar">
                                <img src="{{ $comment->author_avatar }}" alt="{{ $comment->author_name }}">
                            </div>
                            <div class="fb-comment-content">
                                <div class="fb-comment-bubble">
                                    <h5 class="fb-comment-author">{{ $comment->author_name }}</h5>
                                    <p class="fb-comment-text">{{ $comment->comment }}</p>
                                </div>
                                <div class="fb-comment-meta">
                                    <button class="fb-comment-like" data-comment-id="{{ $comment->id }}">Like</button>
                                    <button class="fb-comment-reply" data-comment-id="{{ $comment->id }}">Reply</button>
                                    <span class="fb-comment-time">{{ $comment->created_at->diffForHumans() }}</span>
                                    <span class="fb-comment-likes">👍 {{ $comment->likes_count }}</span>
                                </div>

                                <!-- Replies -->
                                @if($comment->replies && $comment->replies->count() > 0)
                                    <div class="fb-comment-replies">
                                        @foreach($comment->replies as $reply)
                                        <div class="fb-comment fb-comment-reply" data-comment-id="{{ $reply->id }}">
                                            <div class="fb-comment-avatar">
                                                <img src="{{ $reply->author_avatar }}" alt="{{ $reply->author_name }}">
                                            </div>
                                            <div class="fb-comment-content">
                                                <div class="fb-comment-bubble">
                                                    <h5 class="fb-comment-author">{{ $reply->author_name }}</h5>
                                                    <p class="fb-comment-text">{{ $reply->comment }}</p>
                                                </div>
                                                <div class="fb-comment-meta">
                                                    <button class="fb-comment-like" data-comment-id="{{ $reply->id }}">Like</button>
                                                    <span class="fb-comment-time">{{ $reply->created_at->diffForHumans() }}</span>
                                                    <span class="fb-comment-likes">👍 {{ $reply->likes_count }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="fb-no-comments">
                            <p>No comments yet. Be the first to comment!</p>
                        </div>
                    @endif
                </div>

                <!-- Load More Comments -->
                <div class="fb-load-more-comments">
                    <button class="fb-load-more-btn">
                        <i class="fas fa-chevron-down"></i>
                        <span>View more comments</span>
                    </button>
                </div>
            </div>
        </article>
    </div>

    <!-- Right Sidebar -->
    <div class="fb-right-sidebar">
        <!-- Related Articles -->
        <div class="fb-related-section">
            <h4 class="fb-section-title">Related Articles</h4>
            <div class="fb-related-list">
                @if(isset($related_news) && $related_news->count() > 0)
                    @foreach($related_news->take(5) as $related)
                    <div class="fb-related-item">
                        <div class="fb-related-image">
                            <img src="{{ asset($related->image) }}" alt="{{ $related->news_title }}">
                        </div>
                        <div class="fb-related-content">
                            <h5 class="fb-related-title">
                                <a href="{{ url('v2/news/details/'.$related->id.'/'.$related->news_title_slug) }}">
                                    {{ Str::limit($related->news_title, 60) }}
                                </a>
                            </h5>
                            <p class="fb-related-meta">{{ $related->created_at->format('M d') }} · {{ $related->view_count ?? 0 }} views</p>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Popular Articles -->
        <div class="fb-popular-section">
            <h4 class="fb-section-title">Popular Articles</h4>
            <div class="fb-popular-list">
                @if(isset($newspopular))
                    @foreach($newspopular->take(5) as $index => $popular)
                    <div class="fb-popular-item">
                        <div class="fb-popular-rank">{{ $index + 1 }}</div>
                        <div class="fb-popular-image">
                            <img src="{{ asset($popular->image) }}" alt="{{ $popular->news_title }}">
                        </div>
                        <div class="fb-popular-content">
                            <h5 class="fb-popular-title">
                                <a href="{{ url('v2/news/details/'.$popular->id.'/'.$popular->news_title_slug) }}">
                                    {{ Str::limit($popular->news_title, 50) }}
                                </a>
                            </h5>
                            <p class="fb-popular-meta">{{ $popular->view_count ?? 0 }} views</p>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Share Article -->
        <div class="fb-share-section">
            <h4 class="fb-section-title">Share this Article</h4>
            <div class="fb-share-buttons">
                <button class="fb-share-button facebook">
                    <i class="fab fa-facebook-f"></i>
                    <span>Facebook</span>
                </button>
                <button class="fb-share-button twitter">
                    <i class="fab fa-twitter"></i>
                    <span>Twitter</span>
                </button>
                <button class="fb-share-button linkedin">
                    <i class="fab fa-linkedin-in"></i>
                    <span>LinkedIn</span>
                </button>
                <button class="fb-share-button whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    <span>WhatsApp</span>
                </button>
                <button class="fb-share-button copy">
                    <i class="fas fa-link"></i>
                    <span>Copy Link</span>
                </button>
            </div>
        </div>

        <!-- Author Info -->
        <div class="fb-author-section">
            <h4 class="fb-section-title">About the Author</h4>
            <div class="fb-author-card">
                <div class="fb-author-avatar">
                    @php
                        $authorPhotoPath = 'upload/no_image.jpg';
                        if (!empty($news->user->photo)) {
                            // Check if user is admin or subscriber
                            $isSubscriber = $news->user->hasRole('Subscriber');
                            if ($isSubscriber) {
                                // Subscriber photos are stored with full path
                                $authorPhotoPath = $news->user->photo;
                            } else {
                                // Admin photos need path prefix
                                $authorPhotoPath = 'upload/admin_images/' . $news->user->photo;
                            }
                        }
                    @endphp
                    <img src="{{ url($authorPhotoPath) }}" alt="{{ $news->user->name }}">
                </div>
                <div class="fb-author-info">
                    <h5 class="fb-author-name">{{ $news->user->name ?? 'NitiKotha Team' }}</h5>
                    <p class="fb-author-role">News Reporter</p>
                    <p class="fb-author-bio">Experienced journalist covering {{ $news->category->category_name }} and current affairs.</p>
                    <button class="fb-follow-author-btn">
                        <i class="fas fa-plus"></i>
                        <span>Follow</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
