1752445425O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:14:{i:0;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:11;s:13:"category_name";s:2:"AI";s:13:"category_slug";s:2:"ai";s:4:"icon";N;s:13:"display_order";i:0;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";s:19:"2025-07-07 12:38:06";}s:11:" * original";a:9:{s:2:"id";i:11;s:13:"category_name";s:2:"AI";s:13:"category_slug";s:2:"ai";s:4:"icon";N;s:13:"display_order";i:0;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";s:19:"2025-07-07 12:38:06";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:1;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:5;s:13:"category_name";s:8:"Business";s:13:"category_slug";s:8:"business";s:4:"icon";N;s:13:"display_order";i:7;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:5;s:13:"category_name";s:8:"Business";s:13:"category_slug";s:8:"business";s:4:"icon";N;s:13:"display_order";i:7;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:2;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:13;s:13:"category_name";s:9:"Economics";s:13:"category_slug";s:9:"economics";s:4:"icon";N;s:13:"display_order";i:1;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:11:" * original";a:9:{s:2:"id";i:13;s:13:"category_name";s:9:"Economics";s:13:"category_slug";s:9:"economics";s:4:"icon";N;s:13:"display_order";i:1;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:3;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:14;s:13:"category_name";s:8:"Election";s:13:"category_slug";s:8:"election";s:4:"icon";N;s:13:"display_order";i:2;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:11:" * original";a:9:{s:2:"id";i:14;s:13:"category_name";s:8:"Election";s:13:"category_slug";s:8:"election";s:4:"icon";N;s:13:"display_order";i:2;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:4;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:4;s:13:"category_name";s:13:"Entertainment";s:13:"category_slug";s:13:"entertainment";s:4:"icon";N;s:13:"display_order";i:6;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:4;s:13:"category_name";s:13:"Entertainment";s:13:"category_slug";s:13:"entertainment";s:4:"icon";N;s:13:"display_order";i:6;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:5;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:6;s:13:"category_name";s:6:"Health";s:13:"category_slug";s:6:"health";s:4:"icon";N;s:13:"display_order";i:8;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:6;s:13:"category_name";s:6:"Health";s:13:"category_slug";s:6:"health";s:4:"icon";N;s:13:"display_order";i:8;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:6;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:9;s:13:"category_name";s:8:"National";s:13:"category_slug";s:8:"national";s:4:"icon";N;s:13:"display_order";i:11;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-12 17:52:32";}s:11:" * original";a:9:{s:2:"id";i:9;s:13:"category_name";s:8:"National";s:13:"category_slug";s:8:"national";s:4:"icon";N;s:13:"display_order";i:11;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-12 17:52:32";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:7;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:10;s:13:"category_name";s:7:"Opinion";s:13:"category_slug";s:7:"opinion";s:4:"icon";N;s:13:"display_order";i:12;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:10;s:13:"category_name";s:7:"Opinion";s:13:"category_slug";s:7:"opinion";s:4:"icon";N;s:13:"display_order";i:12;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:8;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:2;s:13:"category_name";s:8:"Politics";s:13:"category_slug";s:8:"politics";s:4:"icon";N;s:13:"display_order";i:4;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:2;s:13:"category_name";s:8:"Politics";s:13:"category_slug";s:8:"politics";s:4:"icon";N;s:13:"display_order";i:4;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:9;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:7;s:13:"category_name";s:7:"Science";s:13:"category_slug";s:7:"science";s:4:"icon";N;s:13:"display_order";i:9;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:7;s:13:"category_name";s:7:"Science";s:13:"category_slug";s:7:"science";s:4:"icon";N;s:13:"display_order";i:9;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:10;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:3;s:13:"category_name";s:6:"Sports";s:13:"category_slug";s:6:"sports";s:4:"icon";N;s:13:"display_order";i:5;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:3;s:13:"category_name";s:6:"Sports";s:13:"category_slug";s:6:"sports";s:4:"icon";N;s:13:"display_order";i:5;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:11;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:1;s:13:"category_name";s:10:"Technology";s:13:"category_slug";s:10:"technology";s:4:"icon";N;s:13:"display_order";i:3;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:05:02";s:10:"updated_at";s:19:"2025-07-07 10:05:02";}s:11:" * original";a:9:{s:2:"id";i:1;s:13:"category_name";s:10:"Technology";s:13:"category_slug";s:10:"technology";s:4:"icon";N;s:13:"display_order";i:3;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:05:02";s:10:"updated_at";s:19:"2025-07-07 10:05:02";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:12;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:15;s:13:"category_name";s:5:"Trend";s:13:"category_slug";s:5:"trend";s:4:"icon";s:6:"✈️";s:13:"display_order";i:0;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:11:" * original";a:9:{s:2:"id";i:15;s:13:"category_name";s:5:"Trend";s:13:"category_slug";s:5:"trend";s:4:"icon";s:6:"✈️";s:13:"display_order";i:0;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";N;s:10:"updated_at";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}i:13;O:19:"App\Models\Category":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:8;s:13:"category_name";s:5:"World";s:13:"category_slug";s:5:"world";s:4:"icon";N;s:13:"display_order";i:10;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:11:" * original";a:9:{s:2:"id";i:8;s:13:"category_name";s:5:"World";s:13:"category_slug";s:5:"world";s:4:"icon";N;s:13:"display_order";i:10;s:11:"is_featured";i:0;s:9:"is_active";i:1;s:10:"created_at";s:19:"2025-07-07 10:21:33";s:10:"updated_at";s:19:"2025-07-07 10:21:33";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:11:"is_featured";s:7:"boolean";s:9:"is_active";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}