<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    @php
    $seo = App\Models\SeoSetting::find(1);
    @endphp
    
    <title>@yield('title')</title>
    
    <!-- SEO Meta Tags -->
    <meta name="title" content="{{ $seo->meta_title ?? 'NitiKotha - Facebook Style News Portal' }}">
    <meta name="author" content="{{ $seo->meta_author ?? 'NitiKotha' }}">
    <meta name="keywords" content="{{ $seo->meta_keyword ?? 'news, facebook style, social news' }}">
    <meta name="description" content="{{ $seo->meta_description ?? 'Experience news in a Facebook-style interface with social interactions.' }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="@yield('title')">
    <meta property="og:description" content="{{ $seo->meta_description ?? 'Experience news in a Facebook-style interface.' }}">
    <meta property="og:image" content="{{ asset('frontend/assets/images/og-image.jpg') }}">
    <meta property="og:url" content="{{ url()->current() }}">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ asset('frontend/assets/images/favicon.gif') }}" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Segoe+UI:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Modern NitiKotha CSS -->
    <link rel="stylesheet" href="{{ asset('frontend/assets/css/nitikotha-modern.css') }}?v={{ time() }}">
</head>
<body>
    <!-- Modern NitiKotha Header -->
    <header class="nk-header">
        <div class="nk-header-backdrop"></div>
        <div class="nk-header-container">
            <!-- Left Section - Logo & Brand -->
            <div class="nk-header-left">
                <div class="nk-brand">
                    <a href="{{ url('/v2') }}" class="nk-brand-link">
                        @php
                            $siteLogo = \App\Models\SiteSetting::get('site_logo');
                            $logoPath = $siteLogo ? asset($siteLogo) : asset('frontend/assets/images/logo.gif');
                        @endphp
                        <div class="nk-logo-container">
                            <img src="{{ $logoPath }}" alt="{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}" class="nk-logo">
                            <div class="nk-logo-glow"></div>
                        </div>
                        <div class="nk-brand-text">
                            <span class="nk-brand-name">{{ \App\Models\SiteSetting::get('site_name', 'NitiKotha') }}</span>
                            <span class="nk-brand-tagline">Modern News Platform</span>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Center Section - Navigation -->
            <div class="nk-header-center">
                <nav class="nk-nav">
                    <a href="{{ url('/v2') }}" class="nk-nav-item {{ request()->is('v2') ? 'active' : '' }}" data-tooltip="Home">
                        <div class="nk-nav-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="nk-nav-label">Home</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <a href="{{ url('/v2/category/technology') }}" class="nk-nav-item" data-tooltip="Technology">
                        <div class="nk-nav-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <span class="nk-nav-label">Tech</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <a href="{{ url('/v2/category/sports') }}" class="nk-nav-item" data-tooltip="Sports">
                        <div class="nk-nav-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <span class="nk-nav-label">Sports</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <a href="{{ url('/v2/category/entertainment') }}" class="nk-nav-item" data-tooltip="Entertainment">
                        <div class="nk-nav-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <span class="nk-nav-label">Entertainment</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                    <a href="{{ url('/v2/category/health') }}" class="nk-nav-item" data-tooltip="Health">
                        <div class="nk-nav-icon">
                            <i class="fas fa-heart-pulse"></i>
                        </div>
                        <span class="nk-nav-label">Health</span>
                        <div class="nk-nav-indicator"></div>
                    </a>
                </nav>
            </div>

            <!-- Right Section - Search & Actions -->
            <div class="nk-header-right">
                <!-- Search -->
                <div class="nk-search-container">
                    <div class="nk-search-wrapper">
                        <i class="fas fa-search nk-search-icon"></i>
                        <input type="text" placeholder="Search news..." class="nk-search-input">
                        <div class="nk-search-backdrop"></div>
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button class="nk-theme-toggle" data-tooltip="Toggle Theme">
                    <i class="fas fa-moon nk-theme-icon"></i>
                    <div class="nk-theme-indicator"></div>
                </button>

                <!-- User Actions -->
                <div class="nk-user-actions">
                    @auth
                        <!-- Notifications -->
                        <button class="nk-action-btn" data-tooltip="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="nk-notification-badge">3</span>
                            <div class="nk-action-ripple"></div>
                        </button>

                        <!-- Create Post -->
                        <button class="nk-action-btn" data-tooltip="Create Post">
                            <i class="fas fa-plus"></i>
                            <div class="nk-action-ripple"></div>
                        </button>

                        <!-- Profile Menu -->
                        <div class="nk-profile-menu">
                            @php
                                $user = auth()->user();
                                $photo = 'upload/no_image.jpg';
                                if ($user && !empty($user->photo)) {
                                    if ($user->hasRole('admin')) {
                                        $photo = Str::startsWith($user->photo, 'upload/') ? $user->photo : 'upload/admin_images/' . $user->photo;
                                    } else {
                                        $photo = Str::startsWith($user->photo, 'upload/') ? $user->photo : 'upload/user_images/' . $user->photo;
                                    }
                                }
                            @endphp
                            <div class="nk-profile-avatar">
                                <img src="{{ url($photo) }}" alt="{{ $user->name }}">
                                <div class="nk-profile-status"></div>
                            </div>
                            <div class="nk-profile-info">
                                <span class="nk-profile-name">{{ $user->name }}</span>
                                <i class="fas fa-chevron-down nk-profile-arrow"></i>
                            </div>
                        </div>
                    @else
                        <!-- Auth Buttons -->
                        <a href="{{ route('login') }}" class="nk-auth-btn nk-login-btn">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                            <div class="nk-btn-glow"></div>
                        </a>
                        <a href="{{ route('register') }}" class="nk-auth-btn nk-register-btn">
                            <i class="fas fa-user-plus"></i>
                            <span>Join</span>
                            <div class="nk-btn-glow"></div>
                        </a>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="nk-mobile-toggle">
            <span class="nk-hamburger"></span>
            <span class="nk-hamburger"></span>
            <span class="nk-hamburger"></span>
        </button>
    </header>

    <!-- Main Content -->
    <main class="nk-main">
        @yield('content')
    </main>

    <!-- Modern NitiKotha JavaScript -->
    <script src="{{ asset('frontend/assets/js/nitikotha-modern.js') }}?v={{ time() }}" defer></script>
</body>
</html>
