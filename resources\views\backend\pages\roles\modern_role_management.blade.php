@extends('admin.admin_management_dashboard')
@section('admin')

<style>
.modern-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 2rem 0;
}

.management-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.management-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border: none;
}

.tab-content-modern {
    padding: 2rem;
    background: white;
}

.nav-pills-modern .nav-link {
    border-radius: 15px;
    padding: 1rem 2rem;
    margin: 0 0.5rem;
    background: rgba(255,255,255,0.1);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.nav-pills-modern .nav-link.active {
    background: white;
    color: #667eea;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.role-card {
    border: 1px solid #e3e6f0;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.role-card:hover {
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.permission-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.permission-card:hover {
    background: #e7f3ff;
    border-color: #667eea;
}

.btn-modern-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-modern-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.action-btn {
    display: block;
    width: 100%;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: white;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateX(5px);
}

.search-box {
    position: relative;
    margin-bottom: 2rem;
}

.search-box input {
    border-radius: 25px;
    padding: 1rem 1rem 1rem 3rem;
    border: 1px solid #e9ecef;
    width: 100%;
    font-size: 1rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

@media (max-width: 768px) {
    .modern-container {
        padding: 1rem 0;
    }
    
    .management-card {
        margin: 0 1rem 2rem 1rem;
    }
    
    .card-header-modern {
        padding: 1.5rem;
    }
    
    .tab-content-modern {
        padding: 1.5rem;
    }
    
    .nav-pills-modern .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem;
        font-size: 0.9rem;
    }
}
</style>

<div class="modern-container">
    <div class="container-fluid">
        <!-- Page Title -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">
                            <i class="mdi mdi-shield-account me-2"></i>
                            Role & Permission Management
                        </h2>
                        <p class="text-muted mb-0">Manage system roles and permissions</p>
                    </div>
                    <div>
                        <button class="btn btn-modern-primary" data-bs-toggle="modal" data-bs-target="#quickCreateModal">
                            <i class="mdi mdi-plus me-2"></i>
                            Quick Create
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">{{ $roles->count() }}</div>
                    <div class="stats-label">Total Roles</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">{{ $permissions->count() }}</div>
                    <div class="stats-label">Total Permissions</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">{{ $permission_groups->count() }}</div>
                    <div class="stats-label">Permission Groups</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">{{ $users_with_roles ?? 0 }}</div>
                    <div class="stats-label">Users with Roles</div>
                </div>
            </div>
        </div>

        <!-- Main Management Card -->
        <div class="row">
            <div class="col-12">
                <div class="management-card">
                    <div class="card-header-modern">
                        <ul class="nav nav-pills nav-pills-modern" id="managementTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="roles-tab" data-bs-toggle="pill" data-bs-target="#roles" type="button" role="tab">
                                    <i class="mdi mdi-account-group me-2"></i>Roles
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="permissions-tab" data-bs-toggle="pill" data-bs-target="#permissions" type="button" role="tab">
                                    <i class="mdi mdi-key-variant me-2"></i>Permissions
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="assign-tab" data-bs-toggle="pill" data-bs-target="#assign" type="button" role="tab">
                                    <i class="mdi mdi-shield-link-variant me-2"></i>Assign Permissions
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="tab-content tab-content-modern" id="managementTabsContent">
                        <!-- Roles Tab -->
                        <div class="tab-pane fade show active" id="roles" role="tabpanel">
                            <div class="search-box">
                                <i class="mdi mdi-magnify"></i>
                                <input type="text" class="form-control" id="roleSearch" placeholder="Search roles...">
                            </div>
                            
                            <div class="row" id="rolesContainer">
                                @foreach($roles as $role)
                                <div class="col-md-6 col-lg-4 role-item" data-role="{{ strtolower($role->name) }}">
                                    <div class="role-card">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="mb-0">{{ $role->name }}</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="mdi mdi-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('admin.edit.roles', $role->id) }}">
                                                        <i class="mdi mdi-pencil me-2"></i>Edit
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="{{ route('admin.delete.roles', $role->id) }}" onclick="return confirm('Are you sure?')">
                                                        <i class="mdi mdi-delete me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                <i class="mdi mdi-shield-check me-1"></i>
                                                {{ $role->permissions->count() }} permissions assigned
                                            </small>
                                        </div>
                                        <div class="permission-preview">
                                            @foreach($role->permissions->take(3) as $permission)
                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $permission->name }}</span>
                                            @endforeach
                                            @if($role->permissions->count() > 3)
                                                <span class="badge bg-secondary">+{{ $role->permissions->count() - 3 }} more</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Permissions Tab -->
                        <div class="tab-pane fade" id="permissions" role="tabpanel">
                            <div class="search-box">
                                <i class="mdi mdi-magnify"></i>
                                <input type="text" class="form-control" id="permissionSearch" placeholder="Search permissions...">
                            </div>
                            
                            @foreach($permission_groups as $group)
                            <div class="permission-group mb-4" data-group="{{ strtolower($group->group_name) }}">
                                <h5 class="mb-3">
                                    <i class="mdi mdi-folder-key me-2"></i>
                                    {{ $group->group_name }}
                                    <span class="badge bg-primary ms-2">
                                        {{ App\Models\User::getpermissionByGroupName($group->group_name)->count() }}
                                    </span>
                                </h5>
                                <div class="permission-grid">
                                    @foreach(App\Models\User::getpermissionByGroupName($group->group_name) as $permission)
                                    <div class="permission-card permission-item" data-permission="{{ strtolower($permission->name) }}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">{{ ucwords(str_replace('.', ' ', $permission->name)) }}</h6>
                                                <small class="text-muted">{{ $permission->name }}</small>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="mdi mdi-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('edit.permission', $permission->id) }}">
                                                        <i class="mdi mdi-pencil me-2"></i>Edit
                                                    </a></li>
                                                    <li><a class="dropdown-item text-danger" href="{{ route('delete.permission', $permission->id) }}" onclick="return confirm('Are you sure?')">
                                                        <i class="mdi mdi-delete me-2"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <!-- Assign Permissions Tab -->
                        <div class="tab-pane fade" id="assign" role="tabpanel">
                            <div class="text-center py-5">
                                <i class="mdi mdi-shield-link-variant" style="font-size: 4rem; color: #667eea;"></i>
                                <h4 class="mt-3 mb-3">Assign Permissions to Roles</h4>
                                <p class="text-muted mb-4">Use our advanced permission assignment interface</p>
                                <a href="{{ route('add.roles.permission') }}" class="btn btn-modern-primary btn-lg">
                                    <i class="mdi mdi-arrow-right me-2"></i>
                                    Go to Permission Assignment
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Sidebar -->
        <div class="row mt-4">
            <div class="col-md-8">
                <!-- Recent Activity or other content -->
            </div>
            <div class="col-md-4">
                <div class="quick-actions">
                    <h5 class="mb-3">
                        <i class="mdi mdi-lightning-bolt me-2"></i>
                        Quick Actions
                    </h5>
                    <a href="{{ route('add.roles') }}" class="action-btn">
                        <i class="mdi mdi-account-plus me-2"></i>
                        Create New Role
                    </a>
                    <a href="{{ route('add.permission') }}" class="action-btn">
                        <i class="mdi mdi-key-plus me-2"></i>
                        Create New Permission
                    </a>
                    <a href="{{ route('add.roles.permission') }}" class="action-btn">
                        <i class="mdi mdi-shield-link-variant me-2"></i>
                        Assign Permissions
                    </a>
                    <a href="{{ route('all.roles.permission') }}" class="action-btn">
                        <i class="mdi mdi-view-list me-2"></i>
                        View All Assignments
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality for roles
    $('#roleSearch').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.role-item').each(function() {
            const roleName = $(this).data('role');
            if (roleName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Search functionality for permissions
    $('#permissionSearch').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        $('.permission-item').each(function() {
            const permissionName = $(this).data('permission');
            if (permissionName.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
        
        // Hide/show groups based on visible permissions
        $('.permission-group').each(function() {
            const visiblePermissions = $(this).find('.permission-item:visible').length;
            if (visiblePermissions > 0) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>

@endsection
