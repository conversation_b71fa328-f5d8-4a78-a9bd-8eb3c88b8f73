<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Advertisement;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;

class AdvertisementController extends Controller
{
    /**
     * Display a listing of advertisements
     */
    public function index()
    {
        $advertisements = Advertisement::ordered()->paginate(15);

        $stats = [
            'total' => Advertisement::count(),
            'active' => Advertisement::active()->count(),
            'banner' => Advertisement::byType('banner')->count(),
            'sidebar' => Advertisement::byType('sidebar')->count(),
        ];

        return view('backend.advertisements.index', compact('advertisements', 'stats'));
    }

    /**
     * Show the form for creating a new advertisement
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->get();
        return view('backend.advertisements.create', compact('categories'));
    }

    /**
     * Store a newly created advertisement
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'nullable|url',
            'ad_type' => 'required|in:banner,sidebar,popup,inline',
            'position' => 'required|in:top,middle,bottom,left,right,header,footer',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'target_pages' => 'nullable|array',
            'target_categories' => 'nullable|array',
        ]);

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('upload/advertisements'), $imageName);
            $data['image'] = 'upload/advertisements/' . $imageName;
        }

        // Set default display order if not provided
        if (!$data['display_order']) {
            $maxOrder = Advertisement::where('ad_type', $data['ad_type'])
                                   ->where('position', $data['position'])
                                   ->max('display_order');
            $data['display_order'] = $maxOrder + 1;
        }

        Advertisement::create($data);

        $notification = [
            'message' => 'Advertisement created successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('admin.advertisements.index')->with($notification);
    }

    /**
     * Display the specified advertisement
     */
    public function show(Advertisement $advertisement)
    {
        return view('backend.advertisements.show', compact('advertisement'));
    }

    /**
     * Show the form for editing the specified advertisement
     */
    public function edit(Advertisement $advertisement)
    {
        $categories = Category::where('is_active', true)->get();
        return view('backend.advertisements.edit', compact('advertisement', 'categories'));
    }

    /**
     * Update the specified advertisement
     */
    public function update(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'nullable|url',
            'ad_type' => 'required|in:banner,sidebar,popup,inline',
            'position' => 'required|in:top,middle,bottom,left,right,header,footer',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'target_pages' => 'nullable|array',
            'target_categories' => 'nullable|array',
        ]);

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($advertisement->image && file_exists(public_path($advertisement->image))) {
                unlink(public_path($advertisement->image));
            }

            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $image->move(public_path('upload/advertisements'), $imageName);
            $data['image'] = 'upload/advertisements/' . $imageName;
        }

        $advertisement->update($data);

        $notification = [
            'message' => 'Advertisement updated successfully',
            'alert-type' => 'success'
        ];

        return redirect()->route('admin.advertisements.index')->with($notification);
    }

    /**
     * Remove the specified advertisement
     */
    public function destroy(Advertisement $advertisement)
    {
        // Delete image file
        if ($advertisement->image && file_exists(public_path($advertisement->image))) {
            unlink(public_path($advertisement->image));
        }

        $advertisement->delete();

        $notification = [
            'message' => 'Advertisement deleted successfully',
            'alert-type' => 'success'
        ];

        return redirect()->back()->with($notification);
    }

    /**
     * Toggle advertisement status
     */
    public function toggleStatus(Request $request, Advertisement $advertisement)
    {
        try {
            // Get the new status from request, or toggle current status
            $newStatus = $request->has('is_active')
                ? $request->boolean('is_active')
                : !$advertisement->is_active;

            $advertisement->update(['is_active' => $newStatus]);

            $message = $newStatus ? 'Advertisement activated successfully!' : 'Advertisement deactivated successfully!';

            return response()->json([
                'success' => true,
                'is_active' => $advertisement->is_active,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update advertisement status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update display order
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'advertisements' => 'required|array',
            'advertisements.*.id' => 'required|exists:advertisements,id',
            'advertisements.*.order' => 'required|integer|min:0'
        ]);

        foreach ($request->advertisements as $adData) {
            Advertisement::where('id', $adData['id'])
                        ->update(['display_order' => $adData['order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Advertisement order updated successfully!'
        ]);
    }
}
