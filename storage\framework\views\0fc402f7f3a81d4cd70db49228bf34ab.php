<?php $__env->startSection('title'); ?>
NitiKotha - Premium News Portal
<?php $__env->stopSection(); ?>

<?php $__env->startSection('home'); ?>

<!-- Breaking News Ticker -->
<div class="breaking-news-ticker">
    <div class="container">
        <div class="ticker-wrapper">
            <div class="ticker-label">
                <i class="fas fa-bolt"></i>
                <span>Breaking News</span>
            </div>
            <div class="ticker-content">
                <div class="ticker-items">
                    <?php if(isset($newnewspost)): ?>
                        <?php $__currentLoopData = $newnewspost->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breaking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="ticker-item">
                            <a href="<?php echo e(url('news/details/'.$breaking->id.'/'.$breaking->news_title_slug)); ?>">
                                <?php echo e(Str::limit($breaking->news_title, 80)); ?>

                            </a>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hero Section with 2x1 Grid -->
<section class="hero-smart-section">
    <div class="container">
        <div class="hero-smart-grid">
            <!-- Main Featured Slider (2fr) -->
            <div class="hero-main-slider">
                <div class="smart-slider">
                    <?php if(isset($news_slider) && $news_slider->count() > 0): ?>
                        <?php $__currentLoopData = $news_slider; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="smart-slide <?php echo e($index === 0 ? 'active' : ''); ?>">
                            <div class="slide-image">
                                <img src="<?php echo e(asset($slide->image)); ?>" alt="<?php echo e($slide->news_title); ?>" loading="lazy">
                            </div>
                            <div class="slide-overlay"></div>
                            <div class="slide-content">
                                <div class="slide-category">
                                    <a href="<?php echo e(url('news/category/'.$slide->category->id.'/'.$slide->category->category_slug)); ?>">
                                        <?php echo e($slide->category->category_name); ?>

                                    </a>
                                </div>
                                <h2 class="slide-title">
                                    <a href="<?php echo e(url('news/details/'.$slide->id.'/'.$slide->news_title_slug)); ?>">
                                        <?php echo e($slide->news_title); ?>

                                    </a>
                                </h2>
                                <div class="slide-meta">
                                    <span class="slide-author">
                                        <i class="fas fa-user"></i>
                                        <?php echo e($slide->user->name ?? 'Admin'); ?>

                                    </span>
                                    <span class="slide-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo e($slide->created_at->format('M d, Y')); ?>

                                    </span>
                                    <span class="slide-views">
                                        <i class="fas fa-eye"></i>
                                        <?php echo e($slide->view_count ?? 0); ?>

                                    </span>
                                </div>
                                <p class="slide-excerpt">
                                    <?php echo e(Str::limit(strip_tags($slide->news_details), 150)); ?>

                                </p>
                                <a href="<?php echo e(url('news/details/'.$slide->id.'/'.$slide->news_title_slug)); ?>" class="slide-read-more">
                                    Read Full Story
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
                
                <!-- Slider Navigation -->
                <div class="slider-navigation">
                    <button class="slider-btn prev-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="slider-btn next-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                
                <!-- Slider Indicators -->
                <div class="slider-indicators">
                    <?php if(isset($news_slider)): ?>
                        <?php $__currentLoopData = $news_slider; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <button class="indicator <?php echo e($index === 0 ? 'active' : ''); ?>" data-slide="<?php echo e($index); ?>"></button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Trending Sidebar (1fr) -->
            <div class="hero-trending-sidebar">
                <!-- Trending News Widget -->
                <div class="trending-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-fire"></i>
                            Trending Now
                        </h3>
                    </div>
                    <div class="trending-list">
                        <?php if(isset($newspopular)): ?>
                            <?php $__currentLoopData = $newspopular->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $trending): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="trending-item">
                                <div class="trending-rank"><?php echo e($index + 1); ?></div>
                                <div class="trending-image">
                                    <img src="<?php echo e(asset($trending->image)); ?>" alt="<?php echo e($trending->news_title); ?>" loading="lazy">
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="<?php echo e(url('news/details/'.$trending->id.'/'.$trending->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($trending->news_title, 60)); ?>

                                        </a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category"><?php echo e($trending->category->category_name); ?></span>
                                        <span class="trending-views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo e($trending->view_count ?? 0); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Categories Widget -->
                <div class="quick-categories-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-th-large"></i>
                            Quick Categories
                        </h3>
                    </div>
                    <div class="quick-categories-grid">
                        <?php if(isset($categories)): ?>
                            <?php $__currentLoopData = $categories->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="quick-category-item">
                                <div class="category-icon">📰</div>
                                <div class="category-name"><?php echo e($category->category_name); ?></div>
                                <div class="category-count"><?php echo e($category->news_posts_count ?? 0); ?></div>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Grid Section -->
<section class="categories-smart-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Explore Categories</h2>
            <p class="section-subtitle">Discover news from your favorite topics</p>
        </div>
        <div class="categories-smart-grid">
            <?php if(isset($categories)): ?>
                <?php $__currentLoopData = $categories->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="category-smart-card">
                    <div class="category-card-inner">
                        <div class="category-icon-large">📰</div>
                        <h3 class="category-card-title"><?php echo e($category->category_name); ?></h3>
                        <p class="category-card-count"><?php echo e($category->news_posts_count ?? 0); ?> Articles</p>
                        <a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="category-card-link">
                            Explore
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Latest News Section with 2x1 Grid -->
<section class="latest-news-section">
    <div class="container">
        <div class="latest-news-grid">
            <!-- Main News Column (2fr) -->
            <div class="main-news-column">
                <div class="section-header">
                    <h2 class="section-title">Latest News</h2>
                    <a href="#" class="view-all-link">View All <i class="fas fa-arrow-right"></i></a>
                </div>
                <div class="news-cards-grid">
                    <?php if(isset($newnewspost)): ?>
                        <?php $__currentLoopData = $newnewspost->take(6); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="news-smart-card">
                            <div class="news-card-image">
                                <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" loading="lazy">
                                <div class="news-card-category">
                                    <a href="<?php echo e(url('news/category/'.$news->category->id.'/'.$news->category->category_slug)); ?>">
                                        <?php echo e($news->category->category_name); ?>

                                    </a>
                                </div>
                            </div>
                            <div class="news-card-content">
                                <h3 class="news-card-title">
                                    <a href="<?php echo e(url('news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                        <?php echo e(Str::limit($news->news_title, 80)); ?>

                                    </a>
                                </h3>
                                <p class="news-card-excerpt">
                                    <?php echo e(Str::limit(strip_tags($news->news_details), 120)); ?>

                                </p>
                                <div class="news-card-meta">
                                    <span class="news-author">
                                        <i class="fas fa-user"></i>
                                        <?php echo e($news->user->name ?? 'Admin'); ?>

                                    </span>
                                    <span class="news-date">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo e($news->created_at->format('M d')); ?>

                                    </span>
                                    <span class="news-views">
                                        <i class="fas fa-eye"></i>
                                        <?php echo e($news->view_count ?? 0); ?>

                                    </span>
                                </div>
                            </div>
                        </article>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar Column (1fr) -->
            <div class="sidebar-column">
                <!-- Popular Articles Widget -->
                <div class="popular-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-chart-line"></i>
                            Most Popular
                        </h3>
                    </div>
                    <div class="popular-list">
                        <?php if(isset($newspopular)): ?>
                            <?php $__currentLoopData = $newspopular->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $popular): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="popular-item">
                                <div class="popular-image">
                                    <img src="<?php echo e(asset($popular->image)); ?>" alt="<?php echo e($popular->news_title); ?>" loading="lazy">
                                </div>
                                <div class="popular-content">
                                    <h4 class="popular-title">
                                        <a href="<?php echo e(url('news/details/'.$popular->id.'/'.$popular->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($popular->news_title, 70)); ?>

                                        </a>
                                    </h4>
                                    <div class="popular-meta">
                                        <span class="popular-date"><?php echo e($popular->created_at->format('M d, Y')); ?></span>
                                        <span class="popular-views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo e($popular->view_count ?? 0); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Newsletter Widget -->
                <div class="newsletter-smart-widget">
                    <div class="newsletter-content">
                        <div class="newsletter-icon">📧</div>
                        <h3 class="newsletter-title">Stay Updated</h3>
                        <p class="newsletter-subtitle">Get the latest news delivered to your inbox</p>
                        <form class="newsletter-form" action="#" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="newsletter-input-group">
                                <input type="email" name="email" placeholder="Enter your email" class="newsletter-input" required>
                                <button type="submit" class="newsletter-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                        <p class="newsletter-privacy">We respect your privacy. Unsubscribe anytime.</p>
                    </div>
                </div>

                <!-- Social Follow Widget -->
                <div class="social-follow-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-share-alt"></i>
                            Follow Us
                        </h3>
                    </div>
                    <div class="social-links-grid">
                        <a href="#" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span>Facebook</span>
                            <div class="follower-count">12.5K</div>
                        </a>
                        <a href="#" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                            <span>Twitter</span>
                            <div class="follower-count">8.2K</div>
                        </a>
                        <a href="#" class="social-link youtube">
                            <i class="fab fa-youtube"></i>
                            <span>YouTube</span>
                            <div class="follower-count">25K</div>
                        </a>
                        <a href="#" class="social-link instagram">
                            <i class="fab fa-instagram"></i>
                            <span>Instagram</span>
                            <div class="follower-count">15K</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.home_dashboard_modern', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/index_aggressive_smart.blade.php ENDPATH**/ ?>