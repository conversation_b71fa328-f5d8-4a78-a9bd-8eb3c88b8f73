<?php
$categories = App\Models\Category::orderBy('category_name','ASC')->get();
?>

<!-- Modern Categories Navigation with Trending News -->
<section class="categories-nav">
    <div class="container">
        <div class="nav-layout">
            <!-- Categories Column -->
            <div class="categories-column">
                <div class="column-header">
                    <h3 class="column-title">
                        <i class="fas fa-th-large"></i>
                        All Categories
                    </h3>
                </div>
                <div class="categories-list">
                    <!-- Home Link -->
                    <div class="category-item featured">
                        <a href="<?php echo e(url('/')); ?>" class="category-link <?php echo e(request()->is('/') ? 'active' : ''); ?>">
                            <div class="category-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="category-content">
                                <span class="category-name">Home</span>
                                <span class="category-count">Latest News</span>
                            </div>
                        </a>
                    </div>

                    <!-- Dynamic Categories with Subcategories -->
                    <?php if($categories->count() > 0): ?>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="category-group">
                            <div class="category-item">
                                <a href="<?php echo e(url('news/category/'.$category->id.'/'.$category->category_slug)); ?>" class="category-link">
                                    <div class="category-icon">
                                        <?php switch($category->category_name):
                                            case ('Politics'): ?>
                                                <i class="fas fa-landmark"></i>
                                                <?php break; ?>
                                            <?php case ('Sports'): ?>
                                                <i class="fas fa-futbol"></i>
                                                <?php break; ?>
                                            <?php case ('Technology'): ?>
                                                <i class="fas fa-microchip"></i>
                                                <?php break; ?>
                                            <?php case ('Business'): ?>
                                                <i class="fas fa-chart-line"></i>
                                                <?php break; ?>
                                            <?php case ('Health'): ?>
                                                <i class="fas fa-heartbeat"></i>
                                                <?php break; ?>
                                            <?php case ('Education'): ?>
                                                <i class="fas fa-graduation-cap"></i>
                                                <?php break; ?>
                                            <?php case ('Entertainment'): ?>
                                                <i class="fas fa-film"></i>
                                                <?php break; ?>
                                            <?php case ('Opinion'): ?>
                                                <i class="fas fa-comment-dots"></i>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <i class="fas fa-newspaper"></i>
                                        <?php endswitch; ?>
                                    </div>
                                    <div class="category-content">
                                        <span class="category-name"><?php echo e($category->category_name); ?></span>
                                        <span class="category-count"><?php echo e($category->news_posts_count ?? 0); ?> posts</span>
                                        <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                                            <i class="fas fa-chevron-down expand-icon"></i>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>

                            <!-- Subcategories -->
                            <?php if($category->subcategories && $category->subcategories->count() > 0): ?>
                            <div class="subcategories">
                                <?php $__currentLoopData = $category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="subcategory-item">
                                    <a href="<?php echo e(url('news/subcategory/'.$subcategory->id.'/'.$subcategory->subcategory_slug)); ?>" class="subcategory-link">
                                        <div class="subcategory-icon">
                                            <i class="fas fa-angle-right"></i>
                                        </div>
                                        <span class="subcategory-name"><?php echo e($subcategory->subcategory_name); ?></span>
                                        <span class="subcategory-count"><?php echo e($subcategory->news_posts_count ?? 0); ?></span>
                                    </a>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <!-- Sample Categories if none exist -->
                        <?php
                        $sampleCategories = [
                            ['name' => 'Politics', 'icon' => 'fas fa-landmark', 'count' => 25, 'subcategories' => ['National Politics', 'International']],
                            ['name' => 'Sports', 'icon' => 'fas fa-futbol', 'count' => 18, 'subcategories' => ['Football', 'Cricket', 'Basketball']],
                            ['name' => 'Technology', 'icon' => 'fas fa-microchip', 'count' => 32, 'subcategories' => ['AI & ML', 'Mobile Tech', 'Web Development']],
                            ['name' => 'Business', 'icon' => 'fas fa-chart-line', 'count' => 15, 'subcategories' => ['Finance', 'Startups', 'Markets']],
                            ['name' => 'Health', 'icon' => 'fas fa-heartbeat', 'count' => 22, 'subcategories' => ['Medical News', 'Fitness', 'Mental Health']],
                            ['name' => 'Education', 'icon' => 'fas fa-graduation-cap', 'count' => 12, 'subcategories' => ['Higher Education', 'Online Learning']],
                            ['name' => 'Entertainment', 'icon' => 'fas fa-film', 'count' => 28, 'subcategories' => ['Movies', 'Music', 'TV Shows']],
                            ['name' => 'Opinion', 'icon' => 'fas fa-comment-dots', 'count' => 8, 'subcategories' => ['Editorial', 'Op-Ed']]
                        ];
                        ?>
                        <?php $__currentLoopData = $sampleCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="category-group">
                            <div class="category-item">
                                <a href="#" class="category-link">
                                    <div class="category-icon">
                                        <i class="<?php echo e($category['icon']); ?>"></i>
                                    </div>
                                    <div class="category-content">
                                        <span class="category-name"><?php echo e($category['name']); ?></span>
                                        <span class="category-count"><?php echo e($category['count']); ?> posts</span>
                                        <i class="fas fa-chevron-down expand-icon"></i>
                                    </div>
                                </a>
                            </div>
                            <div class="subcategories">
                                <?php $__currentLoopData = $category['subcategories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="subcategory-item">
                                    <a href="#" class="subcategory-link">
                                        <div class="subcategory-icon">
                                            <i class="fas fa-angle-right"></i>
                                        </div>
                                        <span class="subcategory-name"><?php echo e($sub); ?></span>
                                        <span class="subcategory-count"><?php echo e(rand(3, 15)); ?></span>
                                    </a>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Dashboard Content Columns (3 columns) -->
            <div class="trending-columns">
                <?php if(request()->is('subscriber/posts')): ?>
                    <!-- My Posts List Column -->
                    <div class="posts-list-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-list-alt"></i>
                                My Posts
                            </h3>
                            <div class="header-actions">
                                <a href="<?php echo e(route('subscriber.dashboard')); ?>" class="back-dashboard-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Dashboard</span>
                                </a>
                                <a href="<?php echo e(route('subscriber.posts.create')); ?>" class="create-new-btn">
                                    <i class="fas fa-plus"></i>
                                    <span>Create New Post</span>
                                </a>
                            </div>
                        </div>
                        <div class="posts-table-container">
                            <?php if(isset($posts) && $posts->count() > 0): ?>
                                <div class="posts-table-wrapper">
                                    <table class="posts-table">
                                        <thead>
                                            <tr>
                                                <th>Image</th>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Approval Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="post-image-cell">
                                                    <div class="post-thumbnail">
                                                        <img src="<?php echo e(asset($post->image)); ?>" alt="<?php echo e($post->news_title); ?>" loading="lazy">
                                                    </div>
                                                </td>
                                                <td class="post-title-cell">
                                                    <div class="post-title-content">
                                                        <h4 class="post-title"><?php echo e(Str::limit($post->news_title, 50)); ?></h4>
                                                    </div>
                                                </td>
                                                <td class="post-category-cell">
                                                    <span class="category-badge"><?php echo e($post->category->category_name ?? 'Uncategorized'); ?></span>
                                                </td>
                                                <td class="post-status-cell">
                                                    <span class="status-badge active">Published</span>
                                                </td>
                                                <td class="approval-status-cell">
                                                    <span class="approval-badge <?php echo e(strtolower($post->approval_status)); ?>">
                                                        <?php if($post->approval_status == 'approved'): ?>
                                                            <i class="fas fa-check-circle"></i>
                                                            Approved
                                                        <?php elseif($post->approval_status == 'pending'): ?>
                                                            <i class="fas fa-clock"></i>
                                                            Pending
                                                        <?php else: ?>
                                                            <i class="fas fa-times-circle"></i>
                                                            Rejected
                                                        <?php endif; ?>
                                                    </span>
                                                </td>
                                                <td class="post-date-cell">
                                                    <div class="post-date">
                                                        <span class="date"><?php echo e($post->created_at->format('M d, Y')); ?></span>
                                                        <span class="time"><?php echo e($post->created_at->format('g:i A')); ?></span>
                                                    </div>
                                                </td>
                                                <td class="post-actions-cell">
                                                    <div class="action-buttons">
                                                        <a href="<?php echo e(route('subscriber.posts.show', $post->id)); ?>" class="action-btn view" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="<?php echo e(route('subscriber.posts.edit', $post->id)); ?>" class="action-btn edit" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <form action="<?php echo e(route('subscriber.posts.destroy', $post->id)); ?>" method="POST" class="delete-form" style="display: inline;">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="action-btn delete" title="Delete" onclick="return confirm('Are you sure you want to delete this post?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if($posts->hasPages()): ?>
                                    <div class="pagination-wrapper">
                                        <?php echo e($posts->links()); ?>

                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="no-posts-message">
                                    <div class="no-posts-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h3>No Posts Yet</h3>
                                    <p>You haven't created any posts yet. Start sharing your stories with our community!</p>
                                    <a href="<?php echo e(route('subscriber.posts.create')); ?>" class="create-first-post-btn">
                                        <i class="fas fa-plus"></i>
                                        Create Your First Post
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php elseif(request()->is('subscriber/profile')): ?>
                    <!-- Edit Profile Form Column -->
                    <div class="profile-edit-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-user-cog"></i>
                                Edit Profile
                            </h3>
                        </div>
                        <div class="profile-form-container">
                            <form action="<?php echo e(route('subscriber.profile.update')); ?>" method="POST" enctype="multipart/form-data" class="modern-profile-form">
                                <?php echo csrf_field(); ?>

                                <!-- Profile Avatar Section -->
                                <div class="profile-avatar-section">
                                    <div class="current-avatar">
                                        <?php if($user->photo): ?>
                                            <img src="<?php echo e(asset($user->photo)); ?>" alt="Current Avatar" class="avatar-image">
                                        <?php else: ?>
                                            <div class="default-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="avatar-upload">
                                        <label for="avatar" class="avatar-upload-btn">
                                            <i class="fas fa-camera"></i>
                                            Change Avatar
                                        </label>
                                        <input type="file" id="avatar" name="avatar" class="avatar-input" accept="image/*">
                                        <small class="avatar-help">JPG, PNG, GIF up to 2MB</small>
                                    </div>
                                </div>

                                <!-- Personal Information -->
                                <div class="form-section">
                                    <h4 class="section-title">
                                        <i class="fas fa-user"></i>
                                        Personal Information
                                    </h4>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Full Name</label>
                                            <input type="text" id="name" name="name" class="form-input"
                                                   value="<?php echo e(old('name', $user->name)); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" id="email" name="email" class="form-input"
                                                   value="<?php echo e(old('email', $user->email)); ?>" required>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="text" id="phone" name="phone" class="form-input"
                                                   value="<?php echo e(old('phone', $user->phone)); ?>" placeholder="Enter your phone number">
                                        </div>
                                        <div class="form-group">
                                            <label for="address" class="form-label">Address</label>
                                            <input type="text" id="address" name="address" class="form-input"
                                                   value="<?php echo e(old('address', $user->address)); ?>" placeholder="Enter your address">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="bio" class="form-label">Bio</label>
                                        <textarea id="bio" name="bio" class="form-textarea" rows="4"
                                                  placeholder="Tell us about yourself..."><?php echo e(old('bio', $user->bio)); ?></textarea>
                                    </div>
                                </div>

                                <!-- Security Section -->
                                <div class="form-section">
                                    <h4 class="section-title">
                                        <i class="fas fa-lock"></i>
                                        Change Password (Optional)
                                    </h4>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="password" class="form-label">New Password</label>
                                            <input type="password" id="password" name="password" class="form-input"
                                                   placeholder="Enter new password (leave empty to keep current)">
                                        </div>
                                        <div class="form-group">
                                            <label for="password_confirmation" class="form-label">Confirm Password</label>
                                            <input type="password" id="password_confirmation" name="password_confirmation" class="form-input"
                                                   placeholder="Confirm new password">
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <a href="<?php echo e(route('subscriber.dashboard')); ?>" class="btn-secondary">
                                        <i class="fas fa-arrow-left"></i>
                                        Back to Dashboard
                                    </a>
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-save"></i>
                                        Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php elseif(request()->is('subscriber/posts/*/edit')): ?>
                    <!-- Edit Post Form Column -->
                    <div class="edit-post-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-edit"></i>
                                Edit Post
                            </h3>
                        </div>
                        <div class="edit-post-form-container">
                            <form action="<?php echo e(route('subscriber.posts.update', $post->id)); ?>" method="POST" enctype="multipart/form-data" class="modern-post-form">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>

                                <!-- Post Title -->
                                <div class="form-group">
                                    <label for="news_title" class="form-label">
                                        <i class="fas fa-heading"></i>
                                        Post Title
                                    </label>
                                    <input type="text" id="news_title" name="news_title" class="form-input"
                                           value="<?php echo e(old('news_title', $post->news_title)); ?>"
                                           placeholder="Enter an engaging title for your post..." required>
                                </div>

                                <!-- Category Selection -->
                                <div class="form-group">
                                    <label for="category_id" class="form-label">
                                        <i class="fas fa-folder"></i>
                                        Category
                                    </label>
                                    <select id="category_id" name="category_id" class="form-select" required>
                                        <option value="">Select a category</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $post->category_id) == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->category_name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <!-- Subcategory Selection -->
                                <div class="form-group">
                                    <label for="subcategory_id" class="form-label">
                                        <i class="fas fa-tags"></i>
                                        Subcategory (Optional)
                                    </label>
                                    <select id="subcategory_id" name="subcategory_id" class="form-select">
                                        <option value="">Select a subcategory</option>
                                        <?php if(isset($subcategories)): ?>
                                            <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($subcategory->id); ?>" <?php echo e(old('subcategory_id', $post->subcategory_id) == $subcategory->id ? 'selected' : ''); ?>>
                                                    <?php echo e($subcategory->subcategory_name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>

                                <!-- Current Image Display -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-image"></i>
                                        Current Image
                                    </label>
                                    <div class="current-image-display">
                                        <img src="<?php echo e(asset($post->image)); ?>" alt="Current image" class="current-image">
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="form-group">
                                    <label for="image" class="form-label">
                                        <i class="fas fa-image"></i>
                                        Update Featured Image (Optional)
                                    </label>
                                    <div class="image-upload-area">
                                        <input type="file" id="image" name="image" class="form-file" accept="image/*">
                                        <div class="upload-placeholder">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Click to upload new image or drag and drop</span>
                                            <small>PNG, JPG, JPEG up to 2MB (Leave empty to keep current image)</small>
                                        </div>
                                        <div class="image-preview" style="display: none;">
                                            <img id="preview-img" src="" alt="Preview">
                                            <button type="button" class="remove-image">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Post Content with CKEditor -->
                                <div class="form-group">
                                    <label for="news_details" class="form-label">
                                        <i class="fas fa-edit"></i>
                                        Post Content
                                    </label>
                                    <textarea id="news_details" name="news_details" class="form-textarea ckeditor"
                                              placeholder="Write your post content here..." rows="12"><?php echo e(old('news_details', $post->news_details)); ?></textarea>
                                </div>

                                <!-- Tags -->
                                <div class="form-group">
                                    <label for="tags" class="form-label">
                                        <i class="fas fa-hashtag"></i>
                                        Tags (Optional)
                                    </label>
                                    <input type="text" id="tags" name="tags" class="form-input"
                                           value="<?php echo e(old('tags', $post->tags)); ?>"
                                           placeholder="Enter tags separated by commas (e.g., technology, news, update)">
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <a href="<?php echo e(route('subscriber.posts.index')); ?>" class="btn-secondary">
                                        <i class="fas fa-arrow-left"></i>
                                        Back to Posts
                                    </a>
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-save"></i>
                                        Update Post
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php elseif(request()->is('subscriber/posts/create')): ?>
                    <!-- Create Post Form Column -->
                    <div class="create-post-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-plus-circle"></i>
                                Create New Post
                            </h3>
                        </div>
                        <div class="create-post-form-container">
                            <form action="<?php echo e(route('subscriber.posts.store')); ?>" method="POST" enctype="multipart/form-data" class="modern-post-form">
                                <?php echo csrf_field(); ?>

                                <!-- Post Title -->
                                <div class="form-group">
                                    <label for="news_title" class="form-label">
                                        <i class="fas fa-heading"></i>
                                        Post Title
                                    </label>
                                    <input type="text" id="news_title" name="news_title" class="form-input"
                                           placeholder="Enter an engaging title for your post..." required>
                                </div>

                                <!-- Category Selection -->
                                <div class="form-group">
                                    <label for="category_id" class="form-label">
                                        <i class="fas fa-folder"></i>
                                        Category
                                    </label>
                                    <select id="category_id" name="category_id" class="form-select" required>
                                        <option value="">Select a category</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>"><?php echo e($category->category_name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <!-- Subcategory Selection -->
                                <div class="form-group">
                                    <label for="subcategory_id" class="form-label">
                                        <i class="fas fa-tags"></i>
                                        Subcategory (Optional)
                                    </label>
                                    <select id="subcategory_id" name="subcategory_id" class="form-select">
                                        <option value="">Select a subcategory</option>
                                    </select>
                                </div>

                                <!-- Featured Image -->
                                <div class="form-group">
                                    <label for="image" class="form-label">
                                        <i class="fas fa-image"></i>
                                        Featured Image
                                    </label>
                                    <div class="image-upload-area">
                                        <input type="file" id="image" name="image" class="form-file" accept="image/*" required>
                                        <div class="upload-placeholder">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>Click to upload or drag and drop</span>
                                            <small>PNG, JPG, JPEG up to 2MB</small>
                                        </div>
                                        <div class="image-preview" style="display: none;">
                                            <img id="preview-img" src="" alt="Preview">
                                            <button type="button" class="remove-image">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Post Content -->
                                <div class="form-group">
                                    <label for="news_details" class="form-label">
                                        <i class="fas fa-edit"></i>
                                        Post Content
                                    </label>
                                    <textarea id="news_details" name="news_details" class="form-textarea ckeditor"
                                              placeholder="Write your post content here..." rows="12"></textarea>
                                </div>

                                <!-- Tags -->
                                <div class="form-group">
                                    <label for="tags" class="form-label">
                                        <i class="fas fa-hashtag"></i>
                                        Tags (Optional)
                                    </label>
                                    <input type="text" id="tags" name="tags" class="form-input"
                                           placeholder="Enter tags separated by commas (e.g., technology, news, update)">
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="button" class="btn-secondary" onclick="history.back()">
                                        <i class="fas fa-arrow-left"></i>
                                        Cancel
                                    </button>
                                    <button type="submit" class="btn-primary">
                                        <i class="fas fa-paper-plane"></i>
                                        Submit for Review
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php elseif(request()->is('subscriber/dashboard')): ?>
                    <!-- Column 1: Dashboard Stats -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-chart-bar"></i>
                                Dashboard Stats
                            </h3>
                        </div>
                        <div class="dashboard-stats">
                            <?php
                            $userId = auth()->id();
                            $stats = [
                                'total_posts' => \App\Models\NewsPost::where('user_id', $userId)->count(),
                                'pending_posts' => \App\Models\NewsPost::where('user_id', $userId)->where('approval_status', 'pending')->count(),
                                'approved_posts' => \App\Models\NewsPost::where('user_id', $userId)->where('approval_status', 'approved')->count(),
                                'rejected_posts' => \App\Models\NewsPost::where('user_id', $userId)->where('approval_status', 'rejected')->count(),
                            ];
                            ?>

                            <div class="stat-card">
                                <div class="stat-number"><?php echo e($stats['total_posts']); ?></div>
                                <div class="stat-label">Total Posts</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo e($stats['pending_posts']); ?></div>
                                <div class="stat-label">Pending Approval</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo e($stats['approved_posts']); ?></div>
                                <div class="stat-label">Approved Posts</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?php echo e($stats['rejected_posts']); ?></div>
                                <div class="stat-label">Rejected Posts</div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Column 1: Latest Trending -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-fire"></i>
                                Latest Trending
                            </h3>
                        </div>
                    <div class="trending-news">
                        <?php
                        $trending_news = App\Models\NewsPost::where('status', 1)
                            ->orderBy('view_count', 'desc')
                            ->take(4)
                            ->get();
                        ?>

                        <?php if($trending_news->count() > 0): ?>
                            <?php $__currentLoopData = $trending_news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" loading="lazy">
                                    <div class="trending-badge">
                                        <i class="fas fa-fire"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="<?php echo e(url('news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($news->news_title, 60)); ?>

                                        </a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category"><?php echo e($news->category->category_name); ?></span>
                                        <span class="trending-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                        <span class="trending-views"><?php echo e($news->view_count ?? 0); ?> views</span>
                                    </div>
                                </div>
                            </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php for($i = 1; $i <= 4; $i++): ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset('upload/news/sample-news-image.svg')); ?>" alt="Trending News" loading="lazy">
                                    <div class="trending-badge">
                                        <i class="fas fa-fire"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="#">Latest Trending News Story <?php echo e($i); ?></a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category">Breaking</span>
                                        <span class="trending-time"><?php echo e($i); ?> hours ago</span>
                                        <span class="trending-views"><?php echo e(500 + ($i * 100)); ?> views</span>
                                    </div>
                                </div>
                            </article>
                            <?php endfor; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if(request()->is('subscriber/dashboard')): ?>
                    <!-- Column 2: Recent Posts -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-list"></i>
                                Recent Posts
                            </h3>
                        </div>
                        <div class="recent-posts-list">
                            <?php
                            $recent_posts = \App\Models\NewsPost::where('user_id', auth()->id())
                                ->orderBy('created_at', 'desc')
                                ->take(5)
                                ->get();
                            ?>

                            <?php if($recent_posts->count() > 0): ?>
                                <?php $__currentLoopData = $recent_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="post-item">
                                    <div class="post-image">
                                        <img src="<?php echo e(asset($post->image)); ?>" alt="<?php echo e($post->news_title); ?>" loading="lazy">
                                        <div class="post-status <?php echo e(strtolower($post->approval_status)); ?>">
                                            <?php if($post->approval_status == 'approved'): ?>
                                                <i class="fas fa-check"></i>
                                            <?php elseif($post->approval_status == 'pending'): ?>
                                                <i class="fas fa-clock"></i>
                                            <?php else: ?>
                                                <i class="fas fa-times"></i>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="post-content">
                                        <h4 class="post-title">
                                            <a href="<?php echo e(route('subscriber.posts.show', $post->id)); ?>">
                                                <?php echo e(Str::limit($post->news_title, 50)); ?>

                                            </a>
                                        </h4>
                                        <div class="post-meta">
                                            <span class="post-category"><?php echo e($post->category->category_name ?? 'AI'); ?></span>
                                            <span class="post-status-text"><?php echo e(ucfirst($post->approval_status)); ?></span>
                                            <span class="post-time"><?php echo e($post->created_at->format('M d, Y g:i A')); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <div class="view-all-link">
                                    <a href="<?php echo e(route('subscriber.posts.index')); ?>">View All Posts</a>
                                </div>
                            <?php else: ?>
                                <div class="no-posts">
                                    <i class="fas fa-file-alt"></i>
                                    <p>No posts yet. <a href="<?php echo e(route('subscriber.posts.create')); ?>">Create your first post</a></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Column 2: Popular This Week -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-star"></i>
                                Popular This Week
                            </h3>
                        </div>
                    <div class="trending-news">
                        <?php
                        $popular_news = App\Models\NewsPost::where('status', 1)
                            ->where('created_at', '>=', now()->subWeek())
                            ->orderBy('view_count', 'desc')
                            ->take(4)
                            ->get();
                        ?>

                        <?php if($popular_news->count() > 0): ?>
                            <?php $__currentLoopData = $popular_news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" loading="lazy">
                                    <div class="trending-badge popular">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="<?php echo e(url('news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($news->news_title, 60)); ?>

                                        </a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category"><?php echo e($news->category->category_name); ?></span>
                                        <span class="trending-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                        <span class="trending-views"><?php echo e($news->view_count ?? 0); ?> views</span>
                                    </div>
                                </div>
                            </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php for($i = 1; $i <= 4; $i++): ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset('upload/news/sample-news-image.svg')); ?>" alt="Popular News" loading="lazy">
                                    <div class="trending-badge popular">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="#">Popular News Story <?php echo e($i); ?></a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category">Popular</span>
                                        <span class="trending-time"><?php echo e($i); ?> days ago</span>
                                        <span class="trending-views"><?php echo e(800 + ($i * 150)); ?> views</span>
                                    </div>
                                </div>
                            </article>
                            <?php endfor; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if(request()->is('subscriber/dashboard')): ?>
                    <!-- Column 3: Quick Actions & Guidelines -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-bolt"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="quick-actions-section">
                            <div class="action-buttons">
                                <a href="<?php echo e(route('subscriber.posts.create')); ?>" class="action-btn primary">
                                    <i class="fas fa-plus"></i>
                                    <span>Create New Post</span>
                                </a>
                                <a href="<?php echo e(route('subscriber.posts.index')); ?>" class="action-btn">
                                    <i class="fas fa-list"></i>
                                    <span>View All Posts</span>
                                </a>
                                <a href="<?php echo e(route('subscriber.profile')); ?>" class="action-btn">
                                    <i class="fas fa-user-cog"></i>
                                    <span>Edit Profile</span>
                                </a>
                            </div>

                            <div class="guidelines-section">
                                <h4 class="guidelines-title">
                                    <i class="fas fa-info-circle"></i>
                                    Publishing Guidelines
                                </h4>
                                <ul class="guidelines-list">
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <span>Write clear, engaging titles</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <span>Use high-quality images</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <span>Add relevant tags</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <span>Proofread before submitting</span>
                                    </li>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <span>Follow community guidelines</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Column 3: Recent Updates -->
                    <div class="trending-column">
                        <div class="column-header">
                            <h3 class="column-title">
                                <i class="fas fa-clock"></i>
                                Recent Updates
                            </h3>
                        </div>
                    <div class="trending-news">
                        <?php
                        $recent_news = App\Models\NewsPost::where('status', 1)
                            ->orderBy('created_at', 'desc')
                            ->take(4)
                            ->get();
                        ?>

                        <?php if($recent_news->count() > 0): ?>
                            <?php $__currentLoopData = $recent_news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset($news->image)); ?>" alt="<?php echo e($news->news_title); ?>" loading="lazy">
                                    <div class="trending-badge recent">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="<?php echo e(url('news/details/'.$news->id.'/'.$news->news_title_slug)); ?>">
                                            <?php echo e(Str::limit($news->news_title, 60)); ?>

                                        </a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category"><?php echo e($news->category->category_name); ?></span>
                                        <span class="trending-time"><?php echo e($news->created_at->diffForHumans()); ?></span>
                                    </div>
                                </div>
                            </article>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <?php for($i = 1; $i <= 4; $i++): ?>
                            <article class="trending-item">
                                <div class="trending-image">
                                    <img src="<?php echo e(asset('upload/news/sample-news-image.svg')); ?>" alt="Recent News" loading="lazy">
                                    <div class="trending-badge recent">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                                <div class="trending-content">
                                    <h4 class="trending-title">
                                        <a href="#">Recent News Update <?php echo e($i); ?></a>
                                    </h4>
                                    <div class="trending-meta">
                                        <span class="trending-category">Latest</span>
                                        <span class="trending-time"><?php echo e($i * 30); ?> minutes ago</span>
                                    </div>
                                </div>
                            </article>
                            <?php endfor; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
<?php /**PATH D:\web_server\htdocs\new-blog\resources\views/frontend/body/categories_nav_modern.blade.php ENDPATH**/ ?>