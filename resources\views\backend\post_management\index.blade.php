@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <div class="container-fluid">
        
        <!-- Page Title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Manage Posts</li>
                        </ol>
                    </div>
                    <h4 class="page-title">
                        <i class="mdi mdi-view-list text-primary me-2"></i>Smart Posts Management
                    </h4>
                    <p class="page-title-desc">Use order controls and quick action buttons to manage your posts efficiently.</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ $totalPosts }}</h3>
                        <p class="text-muted mb-0">Total Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">{{ $pinnedCount }}</h3>
                        <p class="text-muted mb-0">Pinned Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{{ $featuredCount }}</h3>
                        <p class="text-muted mb-0">Featured Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ $trendingCount }}</h3>
                        <p class="text-muted mb-0">Trending Posts</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">
                            <i class="mdi mdi-cog text-primary me-2"></i>
                            Posts Management Dashboard
                        </h4>
                        <p class="card-title-desc">Use the controls below to manage post order and status efficiently.</p>
                    </div>
                    <div class="card-body">

                        <!-- Search and Filters -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label class="form-label">Search Posts</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="Search by title, category, or author...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status Filter</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="pinned">Pinned Only</option>
                                    <option value="featured">Featured Only</option>
                                    <option value="trending">Trending Only</option>
                                    <option value="normal">Normal Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Category Filter</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->category_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Actions</label>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-secondary" id="clearFilters">
                                        <i class="mdi mdi-filter-remove"></i> Clear
                                    </button>
                                    <button type="button" class="btn btn-primary" id="refreshPosts">
                                        <i class="mdi mdi-refresh"></i> Refresh
                                    </button>

                                </div>
                            </div>
                        </div>

                        <!-- Posts Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="postsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="80">Order</th>
                                        <th>Post Details</th>
                                        <th width="120">Author</th>
                                        <th width="150">Status</th>
                                        <th width="200">Order Controls</th>
                                        <th width="250">Quick Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="postsTableBody">
                                    @foreach($posts as $index => $post)
                                    <tr data-post-id="{{ $post->id }}" 
                                        data-order="{{ $post->display_order }}" 
                                        data-category-id="{{ $post->category_id }}"
                                        class="post-row {{ $post->is_pinned ? 'table-warning' : '' }}">
                                        
                                        <!-- Order Display -->
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2 order-display">{{ $post->display_order }}</span>
                                                <input type="number" class="form-control form-control-sm order-input" 
                                                       value="{{ $post->display_order }}" 
                                                       min="0" 
                                                       max="999" 
                                                       style="width: 60px; display: none;">
                                            </div>
                                        </td>
                                        
                                        <!-- Post Details -->
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <!-- Post Image -->
                                                <div class="me-3">
                                                    @if($post->image)
                                                        <img src="{{ asset($post->image) }}" 
                                                             alt="{{ $post->news_title }}" 
                                                             class="rounded" 
                                                             style="width: 50px; height: 50px; object-fit: cover;"
                                                             onerror="this.src='{{ asset('backend/assets/images/placeholder.jpg') }}'">
                                                    @else
                                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                             style="width: 50px; height: 50px;">
                                                            <i class="mdi mdi-image text-muted"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                
                                                <!-- Post Info -->
                                                <div>
                                                    <h6 class="mb-1 post-title">
                                                        <a href="{{ route('admin.posts.show', $post->id) }}" 
                                                           class="text-dark text-decoration-none">
                                                            {{ Str::limit($post->news_title, 40) }}
                                                        </a>
                                                    </h6>
                                                    <div class="d-flex gap-1 mb-1">
                                                        <span class="badge bg-primary post-category">{{ $post->category->category_name ?? 'No Category' }}</span>
                                                        @if($post->subcategory)
                                                            <span class="badge bg-secondary">{{ $post->subcategory->subcategory_name }}</span>
                                                        @endif
                                                    </div>
                                                    <small class="text-muted">
                                                        <i class="mdi mdi-calendar me-1"></i>{{ $post->created_at->format('M d, Y') }}
                                                        <i class="mdi mdi-eye ms-2 me-1"></i>{{ $post->view_count ?? 0 }} views
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        
                                        <!-- Author -->
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ substr($post->user->name ?? 'U', 0, 1) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-medium post-author">{{ $post->user->name ?? 'Unknown' }}</div>
                                                    <small class="text-muted">{{ $post->user->email ?? '' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        
                                        <!-- Status -->
                                        <td>
                                            <div class="d-flex flex-wrap gap-1 status-badges">
                                                @if($post->is_pinned)
                                                    <span class="badge bg-warning pinned-badge">
                                                        <i class="mdi mdi-pin me-1"></i>Pinned
                                                    </span>
                                                @endif
                                                @if($post->is_featured)
                                                    <span class="badge bg-info featured-badge">
                                                        <i class="mdi mdi-star me-1"></i>Featured
                                                    </span>
                                                @endif
                                                @if($post->is_trending)
                                                    <span class="badge bg-success trending-badge">
                                                        <i class="mdi mdi-trending-up me-1"></i>Trending
                                                    </span>
                                                @endif
                                                @if(!$post->is_pinned && !$post->is_featured && !$post->is_trending)
                                                    <span class="badge bg-light text-dark normal-badge">Normal</span>
                                                @endif
                                            </div>
                                        </td>
                                        
                                        <!-- Order Controls -->
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary move-up" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="Move Up">
                                                    <i class="mdi mdi-arrow-up"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary move-down" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="Move Down">
                                                    <i class="mdi mdi-arrow-down"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary edit-order" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="Edit Order">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-success save-order" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="Save Order"
                                                        style="display: none;">
                                                    <i class="mdi mdi-check"></i>
                                                </button>
                                            </div>
                                        </td>
                                        
                                        <!-- Quick Actions -->
                                        <td>
                                            <div class="d-flex gap-1 flex-wrap">
                                                <!-- Pin Toggle -->
                                                <button class="btn btn-sm {{ $post->is_pinned ? 'btn-warning' : 'btn-outline-warning' }} toggle-pin" 
                                                        data-post-id="{{ $post->id }}" 
                                                        data-current="{{ $post->is_pinned ? 'true' : 'false' }}"
                                                        title="{{ $post->is_pinned ? 'Unpin Post' : 'Pin Post' }}">
                                                    <i class="mdi mdi-pin"></i>
                                                </button>
                                                
                                                <!-- Feature Toggle -->
                                                <button class="btn btn-sm {{ $post->is_featured ? 'btn-info' : 'btn-outline-info' }} toggle-feature" 
                                                        data-post-id="{{ $post->id }}" 
                                                        data-current="{{ $post->is_featured ? 'true' : 'false' }}"
                                                        title="{{ $post->is_featured ? 'Remove Featured' : 'Mark Featured' }}">
                                                    <i class="mdi mdi-star"></i>
                                                </button>
                                                
                                                <!-- Trending Toggle -->
                                                <button class="btn btn-sm {{ $post->is_trending ? 'btn-success' : 'btn-outline-success' }} toggle-trending" 
                                                        data-post-id="{{ $post->id }}" 
                                                        data-current="{{ $post->is_trending ? 'true' : 'false' }}"
                                                        title="{{ $post->is_trending ? 'Remove Trending' : 'Mark Trending' }}">
                                                    <i class="mdi mdi-trending-up"></i>
                                                </button>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('edit.news.post', $post->id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="Edit Post">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        @if($posts->isEmpty())
                        <div class="text-center py-5">
                            <i class="mdi mdi-file-document-outline text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No posts found</h5>
                            <p class="text-muted">There are no approved posts to manage.</p>
                        </div>
                        @endif

                        <!-- Results Info -->
                        <div class="mt-3">
                            <small class="text-muted" id="resultsInfo">
                                Showing {{ $posts->count() }} of {{ $totalPosts }} posts
                            </small>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
    .post-row {
        transition: all 0.2s ease;
    }

    .post-row:hover {
        background-color: #f8f9fa !important;
    }

    .post-row.table-warning:hover {
        background-color: #fff3cd !important;
    }

    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
    }

    /* Loading animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mdi-loading {
        animation: spin 1s linear infinite;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .d-flex.gap-1 {
            flex-direction: column;
            gap: 0.25rem !important;
        }

        .btn-sm {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    }
</style>

<!-- JavaScript -->
<script>
$(document).ready(function() {
    console.log('=== Smart Post Management System Starting ===');
    console.log('jQuery version:', $.fn.jquery);
    console.log('Document ready fired');

    // Check if required elements exist
    console.log('Posts table body found:', $('#postsTableBody').length);
    console.log('Total rows found:', $('#postsTableBody tr').length);

    // CSRF Token Setup
    const csrfToken = $('meta[name="csrf-token"]').attr('content');
    console.log('CSRF Token:', csrfToken ? 'Found' : 'Missing');

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': csrfToken
        }
    });

    // Initialize all components
    console.log('Initializing components...');
    initializeSearch();
    initializeOrderControls();
    initializeQuickActions();

    console.log('=== Initialization Complete ===');

    // Initialize Search and Filters
    function initializeSearch() {
        let searchTimeout;

        // Real-time search
        $('#searchInput').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                applyFilters();
            }, 300);
        });

        // Filter dropdowns
        $('#statusFilter, #categoryFilter').on('change', applyFilters);

        // Clear filters
        $('#clearFilters').on('click', function() {
            $('#searchInput').val('');
            $('#statusFilter').val('');
            $('#categoryFilter').val('');
            applyFilters();
        });

        // Refresh posts
        $('#refreshPosts').on('click', function() {
            location.reload();
        });



        console.log('Search and filters initialized');
    }

    function applyFilters() {
        const searchTerm = $('#searchInput').val().toLowerCase().trim();
        const statusFilter = $('#statusFilter').val();
        const categoryFilter = $('#categoryFilter').val();

        let visibleCount = 0;

        $('#postsTableBody tr').each(function() {
            const $row = $(this);
            let show = true;

            // Search filter
            if (searchTerm) {
                const title = $row.find('.post-title a').text().toLowerCase();
                const category = $row.find('.post-category').text().toLowerCase();
                const author = $row.find('.post-author').text().toLowerCase();

                show = show && (title.includes(searchTerm) ||
                               category.includes(searchTerm) ||
                               author.includes(searchTerm));
            }

            // Status filter
            if (statusFilter) {
                const $statusBadges = $row.find('.status-badges');
                let hasStatus = false;

                if (statusFilter === 'pinned') {
                    hasStatus = $statusBadges.find('.pinned-badge').length > 0;
                } else if (statusFilter === 'featured') {
                    hasStatus = $statusBadges.find('.featured-badge').length > 0;
                } else if (statusFilter === 'trending') {
                    hasStatus = $statusBadges.find('.trending-badge').length > 0;
                } else if (statusFilter === 'normal') {
                    hasStatus = $statusBadges.find('.normal-badge').length > 0;
                }

                show = show && hasStatus;
            }

            // Category filter
            if (categoryFilter) {
                const postCategoryId = $row.data('category-id');
                show = show && (postCategoryId == categoryFilter);
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update results info
        const totalPosts = $('#postsTableBody tr').length;
        $('#resultsInfo').text(`Showing ${visibleCount} of ${totalPosts} posts`);

        console.log(`Filtered: ${visibleCount} of ${totalPosts} posts visible`);
    }

    // Initialize Order Controls
    function initializeOrderControls() {
        console.log('Initializing order controls...');

        // Move Up
        $(document).on('click', '.move-up', function(e) {
            e.preventDefault();
            console.log('Move up clicked');
            const postId = $(this).data('post-id');
            console.log('Post ID:', postId);
            movePost(postId, 'up');
        });

        // Move Down
        $(document).on('click', '.move-down', function(e) {
            e.preventDefault();
            console.log('Move down clicked');
            const postId = $(this).data('post-id');
            console.log('Post ID:', postId);
            movePost(postId, 'down');
        });

        // Edit Order
        $(document).on('click', '.edit-order', function(e) {
            e.preventDefault();
            console.log('Edit order clicked');
            const $row = $(this).closest('tr');
            const $orderDisplay = $row.find('.order-display');
            const $orderInput = $row.find('.order-input');
            const $editBtn = $(this);
            const $saveBtn = $row.find('.save-order');

            console.log('Switching to edit mode');
            $orderDisplay.hide();
            $orderInput.show().focus();
            $editBtn.hide();
            $saveBtn.show();
        });

        // Save Order
        $(document).on('click', '.save-order', function(e) {
            e.preventDefault();
            console.log('Save order clicked');
            const postId = $(this).data('post-id');
            const $row = $(this).closest('tr');
            const newOrder = $row.find('.order-input').val();

            console.log('Saving order:', newOrder, 'for post:', postId);
            updatePostOrder(postId, newOrder, $row);
        });

        // Cancel edit on Enter key
        $(document).on('keypress', '.order-input', function(e) {
            if (e.which === 13) {
                console.log('Enter pressed, saving order');
                const $saveBtn = $(this).closest('tr').find('.save-order');
                $saveBtn.click();
            }
        });

        console.log('Order controls initialized successfully');

        // Debug: Check if buttons exist
        setTimeout(function() {
            console.log('Move up buttons found:', $('.move-up').length);
            console.log('Move down buttons found:', $('.move-down').length);
            console.log('Edit order buttons found:', $('.edit-order').length);
        }, 1000);
    }

    function movePost(postId, direction) {
        console.log(`Moving post ${postId} ${direction}`);
        const $row = $(`tr[data-post-id="${postId}"]`);
        console.log('Row found:', $row.length);

        const currentOrder = parseInt($row.data('order'));
        console.log('Current order:', currentOrder);

        let newOrder;
        if (direction === 'up') {
            newOrder = Math.max(0, currentOrder - 1);
        } else {
            newOrder = currentOrder + 1;
        }

        console.log('New order:', newOrder);
        updatePostOrder(postId, newOrder, $row);
    }

    function updatePostOrder(postId, newOrder, $row) {
        console.log(`Updating post ${postId} to order ${newOrder}`);

        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        // Show loading
        $orderDisplay.text('...').addClass('bg-warning').removeClass('bg-secondary');

        const ajaxData = {
            post_id: parseInt(postId),
            order: parseInt(newOrder),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        console.log('AJAX data:', ajaxData);

        $.ajax({
            url: '{{ route("admin.posts.update-single-order") }}',
            method: 'POST',
            data: ajaxData,
            beforeSend: function() {
                console.log('Sending order update request...');
            },
            success: function(response) {
                console.log('Order update response:', response);

                if (response.success) {
                    $orderDisplay.text(newOrder).removeClass('bg-warning').addClass('bg-secondary');
                    $row.data('order', newOrder);

                    // Reset edit mode
                    $orderDisplay.show();
                    $orderInput.hide();
                    $editBtn.show();
                    $saveBtn.hide();

                    toastr.success(response.message || 'Order updated successfully!');
                } else {
                    console.error('Order update failed:', response.message);
                    toastr.error(response.message || 'Failed to update order');
                    resetOrderEdit($row);
                }
            },
            error: function(xhr, status, error) {
                console.error('Order update AJAX error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText,
                    url: '{{ route("admin.posts.update-single-order") }}'
                });

                let errorMessage = 'Failed to update order';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                toastr.error(errorMessage);
                resetOrderEdit($row);
            }
        });
    }

    function resetOrderEdit($row) {
        const $orderDisplay = $row.find('.order-display');
        const $orderInput = $row.find('.order-input');
        const $editBtn = $row.find('.edit-order');
        const $saveBtn = $row.find('.save-order');

        $orderDisplay.removeClass('bg-warning').addClass('bg-secondary').show();
        $orderInput.hide();
        $editBtn.show();
        $saveBtn.hide();
    }

    // Initialize Quick Actions
    function initializeQuickActions() {
        console.log('Initializing quick actions...');

        // Pin Toggle
        $(document).on('click', '.toggle-pin', function(e) {
            e.preventDefault();
            console.log('Pin toggle clicked');
            const postId = $(this).data('post-id');
            const $button = $(this);
            const $row = $button.closest('tr');

            console.log('Pin toggle - Post ID:', postId);
            togglePostStatus(postId, 'pin', $button, $row);
        });

        // Feature Toggle
        $(document).on('click', '.toggle-feature', function(e) {
            e.preventDefault();
            console.log('Feature toggle clicked');
            const postId = $(this).data('post-id');
            const $button = $(this);
            const $row = $button.closest('tr');

            console.log('Feature toggle - Post ID:', postId);
            togglePostStatus(postId, 'feature', $button, $row);
        });

        // Trending Toggle
        $(document).on('click', '.toggle-trending', function(e) {
            e.preventDefault();
            console.log('Trending toggle clicked');
            const postId = $(this).data('post-id');
            const $button = $(this);
            const $row = $button.closest('tr');

            console.log('Trending toggle - Post ID:', postId);
            togglePostStatus(postId, 'trending', $button, $row);
        });

        console.log('Quick actions initialized successfully');

        // Debug: Check if buttons exist
        setTimeout(function() {
            console.log('Pin buttons found:', $('.toggle-pin').length);
            console.log('Feature buttons found:', $('.toggle-feature').length);
            console.log('Trending buttons found:', $('.toggle-trending').length);
        }, 1000);
    }

    function togglePostStatus(postId, action, $button, $row) {
        console.log(`Toggling ${action} for post ${postId}`);

        const originalHtml = $button.html();
        const isCurrentlyActive = $button.data('current') === 'true';

        console.log(`Current ${action} status:`, isCurrentlyActive);

        // Show loading
        $button.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i>');

        let url;
        if (action === 'pin') {
            url = '{{ route("admin.posts.toggle-pin", ":id") }}'.replace(':id', postId);
        } else if (action === 'feature') {
            url = '{{ route("admin.posts.toggle-featured", ":id") }}'.replace(':id', postId);
        } else if (action === 'trending') {
            url = '{{ route("admin.posts.toggle-trending", ":id") }}'.replace(':id', postId);
        }

        console.log(`${action} toggle URL:`, url);

        $.ajax({
            url: url,
            method: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                console.log(`Sending ${action} toggle request...`);
            },
            success: function(response) {
                console.log(`${action} toggle response:`, response);

                if (response.success) {
                    updateButtonAndBadge(action, response, $button, $row);
                    toastr.success(response.message);
                } else {
                    console.error(`${action} toggle failed:`, response.message);
                    toastr.error(response.message || `Failed to toggle ${action}`);
                }
            },
            error: function(xhr, status, error) {
                console.error(`${action} toggle AJAX error:`, {
                    status: status,
                    error: error,
                    response: xhr.responseText,
                    url: url
                });

                let errorMessage = `Failed to toggle ${action}`;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                toastr.error(errorMessage);
            },
            complete: function() {
                $button.prop('disabled', false);
                if ($button.html().includes('mdi-loading')) {
                    $button.html(originalHtml);
                }
            }
        });
    }

    function updateButtonAndBadge(action, response, $button, $row) {
        const actionConfig = {
            pin: {
                property: 'is_pinned',
                activeClass: 'btn-warning',
                inactiveClass: 'btn-outline-warning',
                icon: 'mdi-pin',
                badgeClass: 'bg-warning',
                badgeText: 'Pinned',
                badgeSelector: '.pinned-badge'
            },
            feature: {
                property: 'is_featured',
                activeClass: 'btn-info',
                inactiveClass: 'btn-outline-info',
                icon: 'mdi-star',
                badgeClass: 'bg-info',
                badgeText: 'Featured',
                badgeSelector: '.featured-badge'
            },
            trending: {
                property: 'is_trending',
                activeClass: 'btn-success',
                inactiveClass: 'btn-outline-success',
                icon: 'mdi-trending-up',
                badgeClass: 'bg-success',
                badgeText: 'Trending',
                badgeSelector: '.trending-badge'
            }
        };

        const config = actionConfig[action];
        const isActive = response[config.property];

        // Update button
        if (isActive) {
            $button.removeClass(config.inactiveClass).addClass(config.activeClass);
            $button.attr('title', `Remove ${config.badgeText}`);
            $button.data('current', 'true');
        } else {
            $button.removeClass(config.activeClass).addClass(config.inactiveClass);
            $button.attr('title', `Mark ${config.badgeText}`);
            $button.data('current', 'false');
        }

        $button.html(`<i class="${config.icon}"></i>`);

        // Update status badges
        const $statusBadges = $row.find('.status-badges');
        const $existingBadge = $statusBadges.find(config.badgeSelector);

        if (isActive && $existingBadge.length === 0) {
            // Add badge
            $statusBadges.append(`
                <span class="badge ${config.badgeClass} ${config.badgeSelector.substring(1)}">
                    <i class="${config.icon} me-1"></i>${config.badgeText}
                </span>
            `);
        } else if (!isActive && $existingBadge.length > 0) {
            // Remove badge
            $existingBadge.remove();
        }

        // Handle row highlighting for pinned posts
        if (action === 'pin') {
            if (isActive) {
                $row.addClass('table-warning');
            } else {
                $row.removeClass('table-warning');
            }
        }

        // Update normal badge
        updateNormalBadge($statusBadges);
    }

    function updateNormalBadge($statusBadges) {
        const hasSpecialStatus = $statusBadges.find('.pinned-badge, .featured-badge, .trending-badge').length > 0;
        const $normalBadge = $statusBadges.find('.normal-badge');

        if (!hasSpecialStatus && $normalBadge.length === 0) {
            $statusBadges.append('<span class="badge bg-light text-dark normal-badge">Normal</span>');
        } else if (hasSpecialStatus && $normalBadge.length > 0) {
            $normalBadge.remove();
        }
    }
});
</script>

@endsection
