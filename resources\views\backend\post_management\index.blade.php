@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <div class="container-fluid">
        
        <!-- Page Title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                            <li class="breadcrumb-item active">Manage Posts</li>
                        </ol>
                    </div>
                    <h4 class="page-title">
                        <i class="mdi mdi-view-list text-primary me-2"></i>Manage Posts Display Order & Pinning
                    </h4>
                    <p class="page-title-desc">Drag and drop to reorder posts, or use the action buttons to pin/feature posts.</p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ $totalPosts }}</h3>
                        <p class="text-muted mb-0">Total Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">{{ $pinnedCount }}</h3>
                        <p class="text-muted mb-0">Pinned Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{{ $featuredCount }}</h3>
                        <p class="text-muted mb-0">Featured Posts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ $trendingCount }}</h3>
                        <p class="text-muted mb-0">Trending Posts</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">
                            <i class="mdi mdi-drag-vertical text-muted me-2"></i>
                            Posts Management
                        </h4>
                        <p class="card-title-desc">Drag posts to reorder them or use action buttons to manage status.</p>
                    </div>
                    <div class="card-body">

                        <!-- Search and Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="searchPosts" placeholder="Search posts...">
                                    <i class="mdi mdi-magnify position-absolute" style="right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="pinned">Pinned Only</option>
                                    <option value="featured">Featured Only</option>
                                    <option value="trending">Trending Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterCategory">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->category_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                                    <i class="mdi mdi-filter-remove me-1"></i>Clear
                                </button>
                            </div>
                        </div>

                        <!-- Posts Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="postsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <i class="mdi mdi-drag-vertical text-muted"></i>
                                        </th>
                                        <th width="50">Order</th>
                                        <th>Post Details</th>
                                        <th width="120">Author</th>
                                        <th width="150">Status</th>
                                        <th width="200">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-posts">
                                    @foreach($posts as $index => $post)
                                    <tr data-post-id="{{ $post->id }}" 
                                        data-order="{{ $post->display_order }}" 
                                        data-category-id="{{ $post->category_id }}"
                                        class="post-row {{ $post->is_pinned ? 'table-warning' : '' }}">
                                        
                                        <!-- Drag Handle -->
                                        <td class="text-center">
                                            <i class="mdi mdi-drag-vertical text-muted drag-handle" 
                                               style="cursor: move; font-size: 18px;" 
                                               title="Drag to reorder"></i>
                                        </td>
                                        
                                        <!-- Order Badge -->
                                        <td>
                                            <span class="badge bg-secondary order-badge">{{ $post->display_order }}</span>
                                        </td>
                                        
                                        <!-- Post Details -->
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <!-- Post Image -->
                                                <div class="me-3">
                                                    @if($post->image)
                                                        <img src="{{ asset($post->image) }}" 
                                                             alt="{{ $post->news_title }}" 
                                                             class="rounded" 
                                                             style="width: 60px; height: 60px; object-fit: cover;"
                                                             onerror="this.src='{{ asset('backend/assets/images/placeholder.jpg') }}'">
                                                    @else
                                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                             style="width: 60px; height: 60px;">
                                                            <i class="mdi mdi-image text-muted"></i>
                                                        </div>
                                                    @endif
                                                </div>
                                                
                                                <!-- Post Info -->
                                                <div>
                                                    <h6 class="mb-1">
                                                        <a href="{{ route('admin.posts.show', $post->id) }}" 
                                                           class="text-dark text-decoration-none">
                                                            {{ Str::limit($post->news_title, 50) }}
                                                        </a>
                                                    </h6>
                                                    <div class="d-flex gap-1 mb-1">
                                                        <span class="badge bg-primary">{{ $post->category->category_name ?? 'No Category' }}</span>
                                                        @if($post->subcategory)
                                                            <span class="badge bg-secondary">{{ $post->subcategory->subcategory_name }}</span>
                                                        @endif
                                                    </div>
                                                    <small class="text-muted">
                                                        <i class="mdi mdi-calendar me-1"></i>{{ $post->created_at->format('M d, Y') }}
                                                        <i class="mdi mdi-eye ms-2 me-1"></i>{{ $post->view_count ?? 0 }} views
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        
                                        <!-- Author -->
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-2">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        {{ substr($post->user->name ?? 'U', 0, 1) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="fw-medium">{{ $post->user->name ?? 'Unknown' }}</div>
                                                    <small class="text-muted">{{ $post->user->email ?? '' }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        
                                        <!-- Status -->
                                        <td>
                                            <div class="d-flex flex-wrap gap-1">
                                                @if($post->is_pinned)
                                                    <span class="badge bg-warning">
                                                        <i class="mdi mdi-pin me-1"></i>Pinned
                                                    </span>
                                                @endif
                                                @if($post->is_featured)
                                                    <span class="badge bg-info">
                                                        <i class="mdi mdi-star me-1"></i>Featured
                                                    </span>
                                                @endif
                                                @if($post->is_trending)
                                                    <span class="badge bg-success">
                                                        <i class="mdi mdi-trending-up me-1"></i>Trending
                                                    </span>
                                                @endif
                                                @if(!$post->is_pinned && !$post->is_featured && !$post->is_trending)
                                                    <span class="badge bg-light text-dark">Normal</span>
                                                @endif
                                            </div>
                                        </td>
                                        
                                        <!-- Actions -->
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- Pin Button -->
                                                <button class="btn btn-sm {{ $post->is_pinned ? 'btn-warning' : 'btn-outline-warning' }} pin-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_pinned ? 'Unpin' : 'Pin' }}">
                                                    <i class="mdi mdi-pin"></i>
                                                </button>
                                                
                                                <!-- Feature Button -->
                                                <button class="btn btn-sm {{ $post->is_featured ? 'btn-info' : 'btn-outline-info' }} feature-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_featured ? 'Remove Featured' : 'Mark Featured' }}">
                                                    <i class="mdi mdi-star"></i>
                                                </button>
                                                
                                                <!-- Trending Button -->
                                                <button class="btn btn-sm {{ $post->is_trending ? 'btn-success' : 'btn-outline-success' }} trending-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_trending ? 'Remove Trending' : 'Mark Trending' }}">
                                                    <i class="mdi mdi-trending-up"></i>
                                                </button>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('edit.news.post', $post->id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        @if($posts->isEmpty())
                        <div class="text-center py-5">
                            <i class="mdi mdi-file-document-outline text-muted" style="font-size: 48px;"></i>
                            <h5 class="mt-3 text-muted">No posts found</h5>
                            <p class="text-muted">There are no approved posts to manage.</p>
                        </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
    .drag-handle {
        cursor: move !important;
        opacity: 0.6;
        transition: opacity 0.2s ease;
    }

    .drag-handle:hover {
        opacity: 1;
    }

    .post-row:hover .drag-handle {
        opacity: 1;
    }

    .ui-sortable-helper {
        background-color: #f8f9fa !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        border: 1px solid #dee2e6 !important;
        z-index: 1000 !important;
    }

    .ui-state-highlight {
        height: 80px;
        background-color: #e3f2fd !important;
        border: 2px dashed #2196f3 !important;
        border-radius: 4px;
    }

    .post-row.dragging {
        opacity: 0.8;
    }

    .order-badge {
        min-width: 35px;
        text-align: center;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
    }

    .avatar-title {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
    }

    /* Additional enhancements */
    .post-row {
        transition: all 0.2s ease;
    }

    .post-row:hover {
        background-color: #f8f9fa !important;
    }

    .post-row.table-warning:hover {
        background-color: #fff3cd !important;
    }

    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }

    /* Loading animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mdi-loading {
        animation: spin 1s linear infinite;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .d-flex.gap-1 {
            flex-direction: column;
            gap: 0.25rem !important;
        }

        .btn-sm {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    }
</style>

<!-- JavaScript -->
<script>
$(document).ready(function() {
    console.log('Post Management System Initialized');

    // CSRF Token Setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize all components
    initializeSortable();
    initializeActionButtons();
    initializeFilters();

    // Initialize Sortable
    function initializeSortable() {
        const $sortableContainer = $("#sortable-posts");

        if ($sortableContainer.length === 0) {
            console.warn('Sortable container not found');
            return;
        }

        // Destroy existing sortable if exists
        if ($sortableContainer.hasClass('ui-sortable')) {
            $sortableContainer.sortable('destroy');
        }

        $sortableContainer.sortable({
            handle: ".drag-handle",
            items: "> tr",
            axis: "y",
            cursor: "move",
            tolerance: "pointer",
            placeholder: "ui-state-highlight",
            forcePlaceholderSize: true,
            containment: "parent",

            helper: function(e, tr) {
                const $originals = tr.children();
                const $helper = tr.clone();
                $helper.children().each(function(index) {
                    $(this).width($originals.eq(index).width());
                });
                return $helper;
            },

            start: function(event, ui) {
                ui.item.addClass('dragging');
                ui.placeholder.height(ui.item.height());
                console.log('Drag started for post:', ui.item.data('post-id'));
            },

            update: function(event, ui) {
                console.log('Order changed, updating...');
                updatePostOrder();
            },

            stop: function(event, ui) {
                ui.item.removeClass('dragging');
                console.log('Drag completed');
            }
        });

        console.log('Sortable initialized successfully');
    }

    // Update Post Order
    function updatePostOrder() {
        const posts = [];

        $("#sortable-posts tr").each(function(index) {
            const postId = $(this).data('post-id');
            if (postId) {
                posts.push({
                    id: parseInt(postId),
                    order: index
                });

                // Update order badge
                $(this).find('.order-badge').text(index);
            }
        });

        if (posts.length === 0) {
            console.warn('No posts to update');
            return;
        }

        console.log('Updating order for posts:', posts);

        // Show loading state
        $('.order-badge').removeClass('bg-secondary').addClass('bg-warning');

        $.ajax({
            url: '{{ route("admin.posts.update-order") }}',
            method: 'POST',
            data: { posts: posts },
            success: function(response) {
                console.log('Order updated successfully:', response);
                if (response.success) {
                    toastr.success(response.message || 'Post order updated successfully!');
                    $('.order-badge').removeClass('bg-warning').addClass('bg-secondary');
                } else {
                    toastr.error(response.message || 'Failed to update order');
                    revertOrderBadges();
                }
            },
            error: function(xhr, status, error) {
                console.error('Order update failed:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });

                let errorMessage = 'Failed to update post order';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                toastr.error(errorMessage);
                revertOrderBadges();
            }
        });
    }

    function revertOrderBadges() {
        $('.order-badge').removeClass('bg-warning').addClass('bg-secondary');
        // Optionally reload the page to revert order
        // location.reload();
    }

    // Initialize Action Buttons
    function initializeActionButtons() {
        // Remove existing handlers
        $(document).off('click', '.pin-btn, .feature-btn, .trending-btn');

        // Pin Button
        $(document).on('click', '.pin-btn', function(e) {
            e.preventDefault();
            handleToggleAction($(this), 'pin', '{{ route("admin.posts.toggle-pin", ":id") }}');
        });

        // Feature Button
        $(document).on('click', '.feature-btn', function(e) {
            e.preventDefault();
            handleToggleAction($(this), 'feature', '{{ route("admin.posts.toggle-featured", ":id") }}');
        });

        // Trending Button
        $(document).on('click', '.trending-btn', function(e) {
            e.preventDefault();
            handleToggleAction($(this), 'trending', '{{ route("admin.posts.toggle-trending", ":id") }}');
        });

        console.log('Action buttons initialized');
    }

    function handleToggleAction($button, action, urlTemplate) {
        const postId = $button.data('post-id');
        const $row = $button.closest('tr');

        if (!postId) {
            toastr.error('Post ID not found');
            return;
        }

        const url = urlTemplate.replace(':id', postId);
        const originalHtml = $button.html();

        console.log(`${action} button clicked for post:`, postId);

        // Show loading state
        $button.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i>');

        $.ajax({
            url: url,
            method: 'POST',
            success: function(response) {
                console.log(`${action} response:`, response);

                if (response.success) {
                    updateButtonState($button, $row, action, response);
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message || `Failed to toggle ${action}`);
                }
            },
            error: function(xhr, status, error) {
                console.error(`${action} error:`, xhr.responseText);

                let errorMessage = `Failed to toggle ${action}`;
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                toastr.error(errorMessage);
            },
            complete: function() {
                $button.prop('disabled', false);
                if ($button.html().includes('mdi-loading')) {
                    $button.html(originalHtml);
                }
            }
        });
    }

    function updateButtonState($button, $row, action, response) {
        const actionMap = {
            pin: {
                property: 'is_pinned',
                activeClass: 'btn-warning',
                inactiveClass: 'btn-outline-warning',
                icon: 'mdi-pin',
                badgeClass: 'bg-warning',
                badgeText: 'Pinned'
            },
            feature: {
                property: 'is_featured',
                activeClass: 'btn-info',
                inactiveClass: 'btn-outline-info',
                icon: 'mdi-star',
                badgeClass: 'bg-info',
                badgeText: 'Featured'
            },
            trending: {
                property: 'is_trending',
                activeClass: 'btn-success',
                inactiveClass: 'btn-outline-success',
                icon: 'mdi-trending-up',
                badgeClass: 'bg-success',
                badgeText: 'Trending'
            }
        };

        const config = actionMap[action];
        const isActive = response[config.property];

        // Update button appearance
        if (isActive) {
            $button.removeClass(config.inactiveClass).addClass(config.activeClass);
            $button.attr('title', `Remove ${config.badgeText}`);
        } else {
            $button.removeClass(config.activeClass).addClass(config.inactiveClass);
            $button.attr('title', `Mark ${config.badgeText}`);
        }

        $button.html(`<i class="${config.icon}"></i>`);

        // Update status badges
        const $statusCell = $row.find('td:nth-child(5)');
        const $existingBadge = $statusCell.find(`.badge:contains("${config.badgeText}")`);

        if (isActive && $existingBadge.length === 0) {
            $statusCell.find('.d-flex').append(`
                <span class="badge ${config.badgeClass}">
                    <i class="${config.icon} me-1"></i>${config.badgeText}
                </span>
            `);
        } else if (!isActive && $existingBadge.length > 0) {
            $existingBadge.remove();
        }

        // Update row highlighting for pinned posts
        if (action === 'pin') {
            if (isActive) {
                $row.addClass('table-warning');
            } else {
                $row.removeClass('table-warning');
            }
        }

        // Update normal badge if no special status
        const hasAnyStatus = $statusCell.find('.badge:not(.bg-light)').length > 0;
        const $normalBadge = $statusCell.find('.badge.bg-light');

        if (!hasAnyStatus && $normalBadge.length === 0) {
            $statusCell.find('.d-flex').append('<span class="badge bg-light text-dark">Normal</span>');
        } else if (hasAnyStatus && $normalBadge.length > 0) {
            $normalBadge.remove();
        }
    }

    // Initialize Filters
    function initializeFilters() {
        let searchTimeout;

        // Search functionality
        $('#searchPosts').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                applyFilters();
            }, 300);
        });

        // Filter dropdowns
        $('#filterStatus, #filterCategory').on('change', applyFilters);

        // Clear filters
        $('#clearFilters').on('click', function() {
            $('#searchPosts').val('');
            $('#filterStatus').val('');
            $('#filterCategory').val('');
            applyFilters();
        });

        console.log('Filters initialized');
    }

    function applyFilters() {
        const searchTerm = $('#searchPosts').val().toLowerCase().trim();
        const statusFilter = $('#filterStatus').val();
        const categoryFilter = $('#filterCategory').val();

        let visibleCount = 0;

        $('#sortable-posts tr').each(function() {
            const $row = $(this);
            let show = true;

            // Search filter
            if (searchTerm) {
                const title = $row.find('h6 a').text().toLowerCase();
                const category = $row.find('.badge.bg-primary').text().toLowerCase();
                const author = $row.find('.fw-medium').text().toLowerCase();

                show = show && (title.includes(searchTerm) ||
                               category.includes(searchTerm) ||
                               author.includes(searchTerm));
            }

            // Status filter
            if (statusFilter) {
                const badges = $row.find('td:nth-child(5) .badge').map(function() {
                    return $(this).text().toLowerCase();
                }).get().join(' ');

                show = show && badges.includes(statusFilter);
            }

            // Category filter
            if (categoryFilter) {
                const postCategoryId = $row.data('category-id');
                show = show && (postCategoryId == categoryFilter);
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        console.log(`Showing ${visibleCount} posts after filtering`);
    }
});
</script>

@endsection
