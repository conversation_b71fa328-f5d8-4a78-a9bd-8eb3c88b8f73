@extends('admin.admin_management_dashboard')
@section('admin')

<div class="content">
    <!-- Start Content-->
    <div class="container-fluid">
        
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                                <i class="mdi mdi-format-list-bulleted"></i> Bulk Actions
                            </button>
                        </ol>
                    </div>
                    <h4 class="page-title">Post Management</h4>
                </div>
            </div>
        </div>     
        <!-- end page title --> 

        <!-- Stats Cards -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-pin text-primary" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $pinnedCount }}</h5>
                                <p class="text-muted mb-0">Pinned Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-star text-warning" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $featuredCount }}</h5>
                                <p class="text-muted mb-0">Featured Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-trending-up text-success" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $trendingCount }}</h5>
                                <p class="text-muted mb-0">Trending Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-file-document text-info" style="font-size: 2rem;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-0">{{ $totalPosts }}</h5>
                                <p class="text-muted mb-0">Total Posts</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">Manage Posts Display Order & Pinning</h4>
                        <p class="card-title-desc">Drag and drop to reorder posts, or use the action buttons to pin/feature posts.</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <div class="search-box">
                                    <div class="position-relative">
                                        <input type="text" class="form-control" id="searchPosts" placeholder="Search by title, category, or author...">
                                        <i class="mdi mdi-magnify search-icon"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="filterStatus">
                                    <option value="">All Status</option>
                                    <option value="pinned">Pinned Only</option>
                                    <option value="featured">Featured Only</option>
                                    <option value="trending">Trending Only</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterCategory">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->category_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                                    <i class="mdi mdi-filter-remove me-1"></i>Clear
                                </button>
                            </div>
                        </div>



                        <div class="table-responsive">
                            <table class="table table-striped table-hover sortable-table" id="postsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th width="50">
                                            <i class="mdi mdi-drag-vertical text-muted"></i>
                                        </th>
                                        <th width="50">Order</th>
                                        <th>Post Details</th>
                                        <th>Author</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-posts">
                                    @foreach($posts as $post)
                                    <tr data-post-id="{{ $post->id }}" data-order="{{ $post->display_order }}" 
                                        data-category-id="{{ $post->category_id }}"
                                        class="post-row {{ $post->is_pinned ? 'table-warning' : '' }}">
                                        <td>
                                            <input type="checkbox" class="form-check-input post-checkbox" value="{{ $post->id }}">
                                        </td>
                                        <td>
                                            <i class="mdi mdi-drag-vertical text-muted drag-handle" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary order-badge">{{ $post->display_order }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($post->post_image)
                                                    <img src="{{ asset($post->post_image) }}" alt="Post Image" class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                                        <i class="mdi mdi-image text-muted"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <h6 class="mb-1">{{ Str::limit($post->post_title, 50) }}</h6>
                                                    <div class="d-flex gap-1 mb-1">
                                                        <span class="badge bg-primary">{{ $post->category->category_name }}</span>
                                                        @if($post->subcategory)
                                                            <span class="badge bg-secondary">{{ $post->subcategory->subcategory_name }}</span>
                                                        @endif
                                                    </div>
                                                    <small class="text-muted">{{ $post->created_at->format('M d, Y') }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $post->user->name }}</td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                @if($post->is_pinned)
                                                    <span class="badge bg-warning"><i class="mdi mdi-pin"></i> Pinned</span>
                                                @endif
                                                @if($post->is_featured)
                                                    <span class="badge bg-info"><i class="mdi mdi-star"></i> Featured</span>
                                                @endif
                                                @if($post->is_trending)
                                                    <span class="badge bg-success"><i class="mdi mdi-trending-up"></i> Trending</span>
                                                @endif
                                                @if(!$post->is_pinned && !$post->is_featured && !$post->is_trending)
                                                    <span class="badge bg-light text-dark">Normal</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <!-- Pin Button -->
                                                <button class="btn btn-sm {{ $post->is_pinned ? 'btn-warning' : 'btn-outline-warning' }} pin-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_pinned ? 'Unpin' : 'Pin' }}">
                                                    <i class="mdi mdi-pin"></i>
                                                </button>
                                                
                                                <!-- Feature Button -->
                                                <button class="btn btn-sm {{ $post->is_featured ? 'btn-info' : 'btn-outline-info' }} feature-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_featured ? 'Remove Featured' : 'Mark Featured' }}">
                                                    <i class="mdi mdi-star"></i>
                                                </button>
                                                
                                                <!-- Trending Button -->
                                                <button class="btn btn-sm {{ $post->is_trending ? 'btn-success' : 'btn-outline-success' }} trending-btn" 
                                                        data-post-id="{{ $post->id }}" 
                                                        title="{{ $post->is_trending ? 'Remove Trending' : 'Mark Trending' }}">
                                                    <i class="mdi mdi-trending-up"></i>
                                                </button>
                                                
                                                <!-- Edit Button -->
                                                <a href="{{ route('edit.news.post', $post->id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        
    </div> <!-- container -->
</div> <!-- content -->

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" aria-labelledby="bulkActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionModalLabel">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="bulkAction" class="form-label">Select Action</label>
                    <select class="form-select" id="bulkAction">
                        <option value="">Choose action...</option>
                        <option value="pin">Pin Selected Posts</option>
                        <option value="unpin">Unpin Selected Posts</option>
                        <option value="feature">Mark as Featured</option>
                        <option value="unfeature">Remove Featured</option>
                        <option value="trending">Mark as Trending</option>
                        <option value="untrending">Remove Trending</option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <strong><span id="selectedCount">0</span></strong> posts selected
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="executeBulkAction">Execute Action</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('Post Management Ready');
    console.log('jQuery version:', $.fn.jquery);
    console.log('jQuery UI available:', typeof $.ui !== 'undefined');
    console.log('CSRF Token:', $('meta[name="csrf-token"]').attr('content'));

    // Set up CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize all functionality
    console.log('Starting initialization...');
    initializeSortable();
    initializeActionButtons();
    initializeFilters();

    // Test button click
    $('body').on('click', '.pin-btn', function() {
        console.log('Pin button clicked - event working!');
    });

    // Debug: Check if elements exist
    console.log('Posts table found:', $('#postsTable').length);
    console.log('Sortable posts tbody found:', $('#sortable-posts').length);
    console.log('Pin buttons found:', $('.pin-btn').length);
    console.log('Drag handles found:', $('.drag-handle').length);



    // Initialize sortable functionality
    function initializeSortable() {
        console.log('Initializing sortable...');

        var $table = $("#sortable-posts");
        console.log('Table found:', $table.length);

        if ($table.length === 0) {
            console.error('Sortable table not found');
            return;
        }

        // Destroy existing sortable if it exists
        if ($table.hasClass('ui-sortable')) {
            $table.sortable('destroy');
        }

        try {
            $table.sortable({
                handle: ".drag-handle",
                items: "> tr",
                axis: "y",
                cursor: "move",
                tolerance: "pointer",
                placeholder: "ui-state-highlight",
                forcePlaceholderSize: true,
                containment: "parent",
                helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        $(this).width($originals.eq(index).width());
                    });
                    $helper.addClass('ui-sortable-helper');
                    return $helper;
                },
                start: function(event, ui) {
                    console.log('Drag started for post:', ui.item.data('post-id'));
                    ui.placeholder.height(ui.item.height());
                    ui.item.addClass('dragging');
                },
                update: function(event, ui) {
                    console.log('Order updated, calling updatePostOrder...');
                    updatePostOrder();
                },
                stop: function(event, ui) {
                    console.log('Drag stopped');
                    ui.item.removeClass('dragging');
                }
            });

            console.log('Sortable initialized successfully');
            console.log('Sortable options:', $table.sortable('option'));

        } catch (error) {
            console.error('Sortable initialization error:', error);
        }
    }

    // Update post order after drag and drop
    function updatePostOrder() {
        var posts = [];
        $("#sortable-posts tr").each(function(index) {
            var postId = $(this).data('post-id');
            if (postId) {
                posts.push({
                    id: parseInt(postId),
                    order: index
                });
                // Update the order badge
                $(this).find('.order-badge').text(index);
                // Update data attribute
                $(this).attr('data-order', index);
            }
        });

        console.log('Updating order for posts:', posts);

        if (posts.length === 0) {
            console.log('No posts to update');
            return;
        }

        $.ajax({
            url: '{{ route("admin.posts.update-order") }}',
            method: 'POST',
            data: {
                posts: posts,
                _token: '{{ csrf_token() }}'
            },
            beforeSend: function() {
                $('.order-badge').addClass('bg-warning').removeClass('bg-secondary');
            },
            success: function(response) {
                console.log('Order update response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    $('.order-badge').addClass('bg-secondary').removeClass('bg-warning');
                }
            },
            error: function(xhr, status, error) {
                console.error('Order update error:', {
                    status: status,
                    error: error,
                    response: xhr.responseText,
                    posts: posts
                });

                try {
                    var errorResponse = JSON.parse(xhr.responseText);
                    toastr.error(errorResponse.message || 'Failed to update post order');
                } catch (e) {
                    toastr.error('Failed to update post order: ' + error);
                }

                // Revert order badges on error
                $('.order-badge').addClass('bg-secondary').removeClass('bg-warning');
            }
        });
    }

    // Initialize action buttons
    function initializeActionButtons() {
        console.log('Initializing action buttons...');

        // Remove any existing event handlers to prevent duplicates
        $(document).off('click', '.pin-btn');
        $(document).off('click', '.feature-btn');
        $(document).off('click', '.trending-btn');

        // Pin/Unpin functionality
        $(document).on('click', '.pin-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Pin button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            console.log('Post ID:', postId);
            console.log('Button element:', btn);
            console.log('Row element:', row);

            if (!postId) {
                console.error('No post ID found');
                toastr.error('No post ID found');
                return;
            }

            var url = '{{ route("admin.posts.toggle-pin", ":id") }}'.replace(':id', postId);
            console.log('AJAX URL:', url);

            $.ajax({
                url: url,
                method: 'POST',
                beforeSend: function() {
                    console.log('Sending pin request...');
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Pin response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_pinned) {
                            btn.removeClass('btn-outline-warning').addClass('btn-warning');
                            btn.attr('title', 'Unpin');
                            btn.html('<i class="mdi mdi-pin"></i>');
                            row.addClass('table-warning');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Pinned")').length) {
                                statusCell.prepend('<span class="badge bg-warning me-1"><i class="mdi mdi-pin"></i> Pinned</span>');
                            }
                        } else {
                            btn.removeClass('btn-warning').addClass('btn-outline-warning');
                            btn.attr('title', 'Pin');
                            btn.html('<i class="mdi mdi-pin"></i>');
                            row.removeClass('table-warning');

                            // Remove pinned badge
                            row.find('.badge:contains("Pinned")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    } else {
                        toastr.error(response.message || 'Failed to toggle pin status');
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Pin error:', {
                        status: status,
                        error: error,
                        response: xhr.responseText,
                        url: url
                    });

                    try {
                        var errorResponse = JSON.parse(xhr.responseText);
                        toastr.error(errorResponse.message || 'Failed to toggle pin status');
                    } catch (e) {
                        toastr.error('Failed to toggle pin status: ' + error);
                    }

                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-pin"></i>');
                }
            });
        });

        // Feature functionality
        $(document).off('click', '.feature-btn').on('click', '.feature-btn', function(e) {
            e.preventDefault();
            console.log('Feature button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            if (!postId) {
                toastr.error('No post ID found');
                return;
            }

            $.ajax({
                url: '{{ route("admin.posts.toggle-featured", ":id") }}'.replace(':id', postId),
                method: 'POST',
                data: { _token: '{{ csrf_token() }}' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Feature response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_featured) {
                            btn.removeClass('btn-outline-info').addClass('btn-info');
                            btn.attr('title', 'Remove Featured');
                            btn.html('<i class="mdi mdi-star"></i>');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Featured")').length) {
                                statusCell.append('<span class="badge bg-info me-1"><i class="mdi mdi-star"></i> Featured</span>');
                            }
                        } else {
                            btn.removeClass('btn-info').addClass('btn-outline-info');
                            btn.attr('title', 'Mark Featured');
                            btn.html('<i class="mdi mdi-star"></i>');

                            // Remove featured badge
                            row.find('.badge:contains("Featured")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Feature error:', xhr.responseText);
                    toastr.error('Failed to toggle featured status');
                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-star"></i>');
                }
            });
        });

        // Trending functionality
        $(document).off('click', '.trending-btn').on('click', '.trending-btn', function(e) {
            e.preventDefault();
            console.log('Trending button clicked');

            var postId = $(this).data('post-id');
            var btn = $(this);
            var row = btn.closest('tr');

            if (!postId) {
                toastr.error('No post ID found');
                return;
            }

            $.ajax({
                url: '{{ route("admin.posts.toggle-trending", ":id") }}'.replace(':id', postId),
                method: 'POST',
                data: { _token: '{{ csrf_token() }}' },
                beforeSend: function() {
                    btn.prop('disabled', true);
                    btn.html('<i class="mdi mdi-loading mdi-spin"></i>');
                },
                success: function(response) {
                    console.log('Trending response:', response);
                    if (response.success) {
                        // Update button appearance
                        if (response.is_trending) {
                            btn.removeClass('btn-outline-success').addClass('btn-success');
                            btn.attr('title', 'Remove Trending');
                            btn.html('<i class="mdi mdi-trending-up"></i>');

                            // Update status badges
                            var statusCell = row.find('td:nth-child(6)');
                            if (!statusCell.find('.badge:contains("Trending")').length) {
                                statusCell.append('<span class="badge bg-success me-1"><i class="mdi mdi-trending-up"></i> Trending</span>');
                            }
                        } else {
                            btn.removeClass('btn-success').addClass('btn-outline-success');
                            btn.attr('title', 'Mark Trending');
                            btn.html('<i class="mdi mdi-trending-up"></i>');

                            // Remove trending badge
                            row.find('.badge:contains("Trending")').remove();
                        }
                        toastr.success(response.message);
                        updateStats();
                    }
                    btn.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Trending error:', xhr.responseText);
                    toastr.error('Failed to toggle trending status');
                    btn.prop('disabled', false);
                    btn.html('<i class="mdi mdi-trending-up"></i>');
                }
            });
        });

        console.log('Action buttons initialized');
    }

    // Initialize filters
    function initializeFilters() {
        console.log('Initializing filters...');

        // Search functionality with debounce
        var searchTimeout;
        $('#searchPosts').off('input').on('input', function() {
            var searchTerm = $(this).val().toLowerCase().trim();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                console.log('Searching for:', searchTerm);

                var visibleCount = 0;
                $('#postsTable tbody tr').each(function() {
                    var $row = $(this);
                    var postTitle = $row.find('h6').text().toLowerCase();
                    var category = $row.find('.badge.bg-primary').text().toLowerCase();
                    var subcategory = $row.find('.badge.bg-secondary').text().toLowerCase();
                    var author = $row.find('td:nth-child(5)').text().toLowerCase();

                    var isVisible = searchTerm === '' ||
                                   postTitle.includes(searchTerm) ||
                                   category.includes(searchTerm) ||
                                   subcategory.includes(searchTerm) ||
                                   author.includes(searchTerm);

                    if (isVisible) {
                        $row.show();
                        visibleCount++;
                    } else {
                        $row.hide();
                    }
                });

                // Show search results count
                updateSearchResults(visibleCount);
            }, 300);
        });

        // Filter functionality
        $('#filterStatus, #filterCategory').off('change').on('change', function() {
            var statusFilter = $('#filterStatus').val();
            var categoryFilter = $('#filterCategory').val();
            var searchTerm = $('#searchPosts').val().toLowerCase().trim();

            console.log('Applying filters:', {statusFilter, categoryFilter, searchTerm});

            var visibleCount = 0;
            $('#postsTable tbody tr').each(function() {
                var $row = $(this);
                var show = true;

                // Search filter
                if (searchTerm !== '') {
                    var postTitle = $row.find('h6').text().toLowerCase();
                    var category = $row.find('.badge.bg-primary').text().toLowerCase();
                    var subcategory = $row.find('.badge.bg-secondary').text().toLowerCase();
                    var author = $row.find('td:nth-child(5)').text().toLowerCase();

                    show = show && (postTitle.includes(searchTerm) ||
                                   category.includes(searchTerm) ||
                                   subcategory.includes(searchTerm) ||
                                   author.includes(searchTerm));
                }

                // Status filter
                if (statusFilter === 'pinned') {
                    show = show && ($row.hasClass('table-warning') || $row.find('.badge:contains("Pinned")').length > 0);
                } else if (statusFilter === 'featured') {
                    show = show && $row.find('.badge:contains("Featured")').length > 0;
                } else if (statusFilter === 'trending') {
                    show = show && $row.find('.badge:contains("Trending")').length > 0;
                }

                // Category filter
                if (categoryFilter) {
                    var postCategoryId = $row.data('category-id');
                    show = show && (postCategoryId == categoryFilter);
                }

                if (show) {
                    $row.show();
                    visibleCount++;
                } else {
                    $row.hide();
                }
            });

            updateSearchResults(visibleCount);
        });

        // Clear filters button
        $('#clearFilters').off('click').on('click', function() {
            $('#searchPosts').val('');
            $('#filterStatus').val('');
            $('#filterCategory').val('');
            $('#postsTable tbody tr').show();
            updateSearchResults($('#postsTable tbody tr').length);
        });

        console.log('Filters initialized');
    }

    // Update search results count
    function updateSearchResults(count) {
        var total = $('#postsTable tbody tr').length;
        var resultsText = count === total ?
            `Showing all ${total} posts` :
            `Showing ${count} of ${total} posts`;

        // Add or update results indicator
        if ($('#searchResults').length === 0) {
            $('#searchPosts').parent().after(`<small id="searchResults" class="text-muted">${resultsText}</small>`);
        } else {
            $('#searchResults').text(resultsText);
        }
    }

    // Update stats function
    function updateStats() {
        // For now, we'll just reload the page after a short delay
        setTimeout(function() {
            location.reload();
        }, 1000);
    }
});
</script>

@endsection
